# Properties for CiaD Jenkins build pipelines
# email list for <PERSON>, comma separation for multiple emails
emailList: <EMAIL>

#Enable it if you don't need the Fortify scan
skipFortifyScan: false
skipSonarScan: true
jdkVersion: openjdk17
skipUnitTests: true

#Specify your artifact file name, enable it if the file name is different from the "artifactId" attribute in pom file
#For gradle, add the line: rootProject.name = 'YOUR_ARTIFACT_NAME' to your settings.gradle
#artifactId: fietor
mvnTarget: dependency:purge-local-repository -DactTransitively=false clean validate compile package

#Your artifact file exension, enable it if your artifact extension is different with the "packaging" attribute in pom file
packageType: tar

#Revison delimiter, default value: -b
versionDelimiter: -r

  #Specify the Artifactory repository, enable it if you need a different one
  #artifactoryReleaseRepo: local-release-bns

#Enable it if you are not ready to deploy your application
#skipDeployment: true
