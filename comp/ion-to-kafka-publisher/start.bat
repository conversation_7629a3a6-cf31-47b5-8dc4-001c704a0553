set PROJECT_ROOT=c:\Dev\StrategicalSolution\ion-kafka-publisher
set BASE=%PROJECT_ROOT%\comp\ion-to-kafka-publisher
set CONF=%BASE%\config
set PLUGIN=%PROJECT_ROOT%\comp\kafka-serializers-plugins
C:\Utils\java\jdk-21.0.5-Temurin\bin\java.exe -Djava.security.properties=%CONF%\mmi-tor\trade_split\java.properties -Djavax.security.auth.useSubjectCredsOnly=true -Djava.security.auth.login.config=%CONF%\kafka\ist\kafka_client_jaas.conf -Djava.security.krb5.conf=%CONF%\kafka\ist\krb5_ist.conf -Dsun.security.krb5.disableReferrals=true -Dspring.kafka.ssl.trust-store-location=file:%CONF%\kafka\ist\kafka.client.truststore.jks -Dspring.kafka.ssl.trust-store-password=kafkaclientssl -DDB_USERNAME=fietoapp -Dspring.devtools.restart.enabled=false -server -Xrs -Xms712m -Xss4096k -Dloader.path=%PLUGIN%\target\kafka-serializers-plugins-1.0-SNAPSHOT.jar -jar %BASE%\target\ion-to-kafka-publisher-1.0-SNAPSHOT.jar -init %CONF%\mmi-tor\trade_split\ist2ist\mkv_TRADE_SPLIT_TOR.jinit --spring.profiles.active=ist-trade_split_tor --spring.config.location=file:%CONF%\mmi-tor\trade_split\ist2ist\



