{"name": "ion-kafka-metrics", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development"}, "private": true, "dependencies": {"@angular/animations": "^16.2.0", "@angular/common": "^16.2.0", "@angular/compiler": "^16.2.0", "@angular/core": "^16.2.0", "@angular/forms": "^16.2.0", "@angular/platform-browser": "^16.2.0", "@angular/platform-browser-dynamic": "^16.2.0", "@angular/router": "^16.2.0", "rxjs": "~7.8.0", "tailwindcss": "^2.2.19", "tslib": "^2.3.0", "zone.js": "~0.13.0"}, "devDependencies": {"@angular-devkit/build-angular": "^16.2.1", "@angular/cli": "~16.2.1", "@angular/compiler-cli": "^16.2.0", "@types/node": "^18.11.9", "typescript": "~5.1.3"}}