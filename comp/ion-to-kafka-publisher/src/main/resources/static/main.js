"use strict";(self.webpackChunkion_kafka_metrics=self.webpackChunkion_kafka_metrics||[]).push([[792],{988:()=>{function te(e){return"function"==typeof e}function So(e){const n=e(r=>{Error.call(r),r.stack=(new Error).stack});return n.prototype=Object.create(Error.prototype),n.prototype.constructor=n,n}const rs=So(e=>function(n){e(this),this.message=n?`${n.length} errors occurred during unsubscription:\n${n.map((r,o)=>`${o+1}) ${r.toString()}`).join("\n  ")}`:"",this.name="UnsubscriptionError",this.errors=n});function hr(e,t){if(e){const n=e.indexOf(t);0<=n&&e.splice(n,1)}}class Ze{constructor(t){this.initialTeardown=t,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let t;if(!this.closed){this.closed=!0;const{_parentage:n}=this;if(n)if(this._parentage=null,Array.isArray(n))for(const i of n)i.remove(this);else n.remove(this);const{initialTeardown:r}=this;if(te(r))try{r()}catch(i){t=i instanceof rs?i.errors:[i]}const{_finalizers:o}=this;if(o){this._finalizers=null;for(const i of o)try{hh(i)}catch(s){t=t??[],s instanceof rs?t=[...t,...s.errors]:t.push(s)}}if(t)throw new rs(t)}}add(t){var n;if(t&&t!==this)if(this.closed)hh(t);else{if(t instanceof Ze){if(t.closed||t._hasParent(this))return;t._addParent(this)}(this._finalizers=null!==(n=this._finalizers)&&void 0!==n?n:[]).push(t)}}_hasParent(t){const{_parentage:n}=this;return n===t||Array.isArray(n)&&n.includes(t)}_addParent(t){const{_parentage:n}=this;this._parentage=Array.isArray(n)?(n.push(t),n):n?[n,t]:t}_removeParent(t){const{_parentage:n}=this;n===t?this._parentage=null:Array.isArray(n)&&hr(n,t)}remove(t){const{_finalizers:n}=this;n&&hr(n,t),t instanceof Ze&&t._removeParent(this)}}Ze.EMPTY=(()=>{const e=new Ze;return e.closed=!0,e})();const dh=Ze.EMPTY;function fh(e){return e instanceof Ze||e&&"closed"in e&&te(e.remove)&&te(e.add)&&te(e.unsubscribe)}function hh(e){te(e)?e():e.unsubscribe()}const Un={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1},os={setTimeout(e,t,...n){const{delegate:r}=os;return r?.setTimeout?r.setTimeout(e,t,...n):setTimeout(e,t,...n)},clearTimeout(e){const{delegate:t}=os;return(t?.clearTimeout||clearTimeout)(e)},delegate:void 0};function ph(e){os.setTimeout(()=>{const{onUnhandledError:t}=Un;if(!t)throw e;t(e)})}function Ou(){}const sb=Pu("C",void 0,void 0);function Pu(e,t,n){return{kind:e,value:t,error:n}}let zn=null;function is(e){if(Un.useDeprecatedSynchronousErrorHandling){const t=!zn;if(t&&(zn={errorThrown:!1,error:null}),e(),t){const{errorThrown:n,error:r}=zn;if(zn=null,n)throw r}}else e()}class Fu extends Ze{constructor(t){super(),this.isStopped=!1,t?(this.destination=t,fh(t)&&t.add(this)):this.destination=hb}static create(t,n,r){return new Ao(t,n,r)}next(t){this.isStopped?Lu(function ub(e){return Pu("N",e,void 0)}(t),this):this._next(t)}error(t){this.isStopped?Lu(function ab(e){return Pu("E",void 0,e)}(t),this):(this.isStopped=!0,this._error(t))}complete(){this.isStopped?Lu(sb,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(t){this.destination.next(t)}_error(t){try{this.destination.error(t)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}}const lb=Function.prototype.bind;function ku(e,t){return lb.call(e,t)}class db{constructor(t){this.partialObserver=t}next(t){const{partialObserver:n}=this;if(n.next)try{n.next(t)}catch(r){ss(r)}}error(t){const{partialObserver:n}=this;if(n.error)try{n.error(t)}catch(r){ss(r)}else ss(t)}complete(){const{partialObserver:t}=this;if(t.complete)try{t.complete()}catch(n){ss(n)}}}class Ao extends Fu{constructor(t,n,r){let o;if(super(),te(t)||!t)o={next:t??void 0,error:n??void 0,complete:r??void 0};else{let i;this&&Un.useDeprecatedNextContext?(i=Object.create(t),i.unsubscribe=()=>this.unsubscribe(),o={next:t.next&&ku(t.next,i),error:t.error&&ku(t.error,i),complete:t.complete&&ku(t.complete,i)}):o=t}this.destination=new db(o)}}function ss(e){Un.useDeprecatedSynchronousErrorHandling?function cb(e){Un.useDeprecatedSynchronousErrorHandling&&zn&&(zn.errorThrown=!0,zn.error=e)}(e):ph(e)}function Lu(e,t){const{onStoppedNotification:n}=Un;n&&os.setTimeout(()=>n(e,t))}const hb={closed:!0,next:Ou,error:function fb(e){throw e},complete:Ou},Vu="function"==typeof Symbol&&Symbol.observable||"@@observable";function _n(e){return e}function gh(e){return 0===e.length?_n:1===e.length?e[0]:function(n){return e.reduce((r,o)=>o(r),n)}}let he=(()=>{class e{constructor(n){n&&(this._subscribe=n)}lift(n){const r=new e;return r.source=this,r.operator=n,r}subscribe(n,r,o){const i=function mb(e){return e&&e instanceof Fu||function gb(e){return e&&te(e.next)&&te(e.error)&&te(e.complete)}(e)&&fh(e)}(n)?n:new Ao(n,r,o);return is(()=>{const{operator:s,source:a}=this;i.add(s?s.call(i,a):a?this._subscribe(i):this._trySubscribe(i))}),i}_trySubscribe(n){try{return this._subscribe(n)}catch(r){n.error(r)}}forEach(n,r){return new(r=mh(r))((o,i)=>{const s=new Ao({next:a=>{try{n(a)}catch(u){i(u),s.unsubscribe()}},error:i,complete:o});this.subscribe(s)})}_subscribe(n){var r;return null===(r=this.source)||void 0===r?void 0:r.subscribe(n)}[Vu](){return this}pipe(...n){return gh(n)(this)}toPromise(n){return new(n=mh(n))((r,o)=>{let i;this.subscribe(s=>i=s,s=>o(s),()=>r(i))})}}return e.create=t=>new e(t),e})();function mh(e){var t;return null!==(t=e??Un.Promise)&&void 0!==t?t:Promise}const yb=So(e=>function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});let At=(()=>{class e extends he{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(n){const r=new yh(this,this);return r.operator=n,r}_throwIfClosed(){if(this.closed)throw new yb}next(n){is(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(const r of this.currentObservers)r.next(n)}})}error(n){is(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=n;const{observers:r}=this;for(;r.length;)r.shift().error(n)}})}complete(){is(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;const{observers:n}=this;for(;n.length;)n.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var n;return(null===(n=this.observers)||void 0===n?void 0:n.length)>0}_trySubscribe(n){return this._throwIfClosed(),super._trySubscribe(n)}_subscribe(n){return this._throwIfClosed(),this._checkFinalizedStatuses(n),this._innerSubscribe(n)}_innerSubscribe(n){const{hasError:r,isStopped:o,observers:i}=this;return r||o?dh:(this.currentObservers=null,i.push(n),new Ze(()=>{this.currentObservers=null,hr(i,n)}))}_checkFinalizedStatuses(n){const{hasError:r,thrownError:o,isStopped:i}=this;r?n.error(o):i&&n.complete()}asObservable(){const n=new he;return n.source=this,n}}return e.create=(t,n)=>new yh(t,n),e})();class yh extends At{constructor(t,n){super(),this.destination=t,this.source=n}next(t){var n,r;null===(r=null===(n=this.destination)||void 0===n?void 0:n.next)||void 0===r||r.call(n,t)}error(t){var n,r;null===(r=null===(n=this.destination)||void 0===n?void 0:n.error)||void 0===r||r.call(n,t)}complete(){var t,n;null===(n=null===(t=this.destination)||void 0===t?void 0:t.complete)||void 0===n||n.call(t)}_subscribe(t){var n,r;return null!==(r=null===(n=this.source)||void 0===n?void 0:n.subscribe(t))&&void 0!==r?r:dh}}function vh(e){return te(e?.lift)}function _e(e){return t=>{if(vh(t))return t.lift(function(n){try{return e(n,this)}catch(r){this.error(r)}});throw new TypeError("Unable to lift unknown Observable type")}}function Ce(e,t,n,r,o){return new vb(e,t,n,r,o)}class vb extends Fu{constructor(t,n,r,o,i,s){super(t),this.onFinalize=i,this.shouldUnsubscribe=s,this._next=n?function(a){try{n(a)}catch(u){t.error(u)}}:super._next,this._error=o?function(a){try{o(a)}catch(u){t.error(u)}finally{this.unsubscribe()}}:super._error,this._complete=r?function(){try{r()}catch(a){t.error(a)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var t;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){const{closed:n}=this;super.unsubscribe(),!n&&(null===(t=this.onFinalize)||void 0===t||t.call(this))}}}function Z(e,t){return _e((n,r)=>{let o=0;n.subscribe(Ce(r,i=>{r.next(e.call(t,i,o++))}))})}function En(e){return this instanceof En?(this.v=e,this):new En(e)}function _h(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var n,t=e[Symbol.asyncIterator];return t?t.call(e):(e=function $u(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}(e),n={},r("next"),r("throw"),r("return"),n[Symbol.asyncIterator]=function(){return this},n);function r(i){n[i]=e[i]&&function(s){return new Promise(function(a,u){!function o(i,s,a,u){Promise.resolve(u).then(function(c){i({value:c,done:a})},s)}(a,u,(s=e[i](s)).done,s.value)})}}}"function"==typeof SuppressedError&&SuppressedError;const Eh=e=>e&&"number"==typeof e.length&&"function"!=typeof e;function bh(e){return te(e?.then)}function Mh(e){return te(e[Vu])}function Ih(e){return Symbol.asyncIterator&&te(e?.[Symbol.asyncIterator])}function Sh(e){return new TypeError(`You provided ${null!==e&&"object"==typeof e?"an invalid object":`'${e}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}const Ah=function Gb(){return"function"==typeof Symbol&&Symbol.iterator?Symbol.iterator:"@@iterator"}();function Th(e){return te(e?.[Ah])}function Nh(e){return function wh(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var o,r=n.apply(e,t||[]),i=[];return o=Object.create(("function"==typeof AsyncIterator?AsyncIterator:Object).prototype),a("next"),a("throw"),a("return",function s(h){return function(p){return Promise.resolve(p).then(h,d)}}),o[Symbol.asyncIterator]=function(){return this},o;function a(h,p){r[h]&&(o[h]=function(g){return new Promise(function(y,D){i.push([h,g,y,D])>1||u(h,g)})},p&&(o[h]=p(o[h])))}function u(h,p){try{!function c(h){h.value instanceof En?Promise.resolve(h.value.v).then(l,d):f(i[0][2],h)}(r[h](p))}catch(g){f(i[0][3],g)}}function l(h){u("next",h)}function d(h){u("throw",h)}function f(h,p){h(p),i.shift(),i.length&&u(i[0][0],i[0][1])}}(this,arguments,function*(){const n=e.getReader();try{for(;;){const{value:r,done:o}=yield En(n.read());if(o)return yield En(void 0);yield yield En(r)}}finally{n.releaseLock()}})}function Rh(e){return te(e?.getReader)}function rt(e){if(e instanceof he)return e;if(null!=e){if(Mh(e))return function qb(e){return new he(t=>{const n=e[Vu]();if(te(n.subscribe))return n.subscribe(t);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}(e);if(Eh(e))return function Wb(e){return new he(t=>{for(let n=0;n<e.length&&!t.closed;n++)t.next(e[n]);t.complete()})}(e);if(bh(e))return function Zb(e){return new he(t=>{e.then(n=>{t.closed||(t.next(n),t.complete())},n=>t.error(n)).then(null,ph)})}(e);if(Ih(e))return xh(e);if(Th(e))return function Yb(e){return new he(t=>{for(const n of e)if(t.next(n),t.closed)return;t.complete()})}(e);if(Rh(e))return function Qb(e){return xh(Nh(e))}(e)}throw Sh(e)}function xh(e){return new he(t=>{(function Xb(e,t){var n,r,o,i;return function Dh(e,t,n,r){return new(n||(n=Promise))(function(i,s){function a(l){try{c(r.next(l))}catch(d){s(d)}}function u(l){try{c(r.throw(l))}catch(d){s(d)}}function c(l){l.done?i(l.value):function o(i){return i instanceof n?i:new n(function(s){s(i)})}(l.value).then(a,u)}c((r=r.apply(e,t||[])).next())})}(this,void 0,void 0,function*(){try{for(n=_h(e);!(r=yield n.next()).done;)if(t.next(r.value),t.closed)return}catch(s){o={error:s}}finally{try{r&&!r.done&&(i=n.return)&&(yield i.call(n))}finally{if(o)throw o.error}}t.complete()})})(e,t).catch(n=>t.error(n))})}function nn(e,t,n,r=0,o=!1){const i=t.schedule(function(){n(),o?e.add(this.schedule(null,r)):this.unsubscribe()},r);if(e.add(i),!o)return i}function Ae(e,t,n=1/0){return te(t)?Ae((r,o)=>Z((i,s)=>t(r,i,o,s))(rt(e(r,o))),n):("number"==typeof t&&(n=t),_e((r,o)=>function Jb(e,t,n,r,o,i,s,a){const u=[];let c=0,l=0,d=!1;const f=()=>{d&&!u.length&&!c&&t.complete()},h=g=>c<r?p(g):u.push(g),p=g=>{i&&t.next(g),c++;let y=!1;rt(n(g,l++)).subscribe(Ce(t,D=>{o?.(D),i?h(D):t.next(D)},()=>{y=!0},void 0,()=>{if(y)try{for(c--;u.length&&c<r;){const D=u.shift();s?nn(t,s,()=>p(D)):p(D)}f()}catch(D){t.error(D)}}))};return e.subscribe(Ce(t,h,()=>{d=!0,f()})),()=>{a?.()}}(r,o,e,n)))}function pr(e=1/0){return Ae(_n,e)}const jt=new he(e=>e.complete());function Oh(e){return e&&te(e.schedule)}function zu(e){return e[e.length-1]}function Ph(e){return te(zu(e))?e.pop():void 0}function To(e){return Oh(zu(e))?e.pop():void 0}function Fh(e,t=0){return _e((n,r)=>{n.subscribe(Ce(r,o=>nn(r,e,()=>r.next(o),t),()=>nn(r,e,()=>r.complete(),t),o=>nn(r,e,()=>r.error(o),t)))})}function kh(e,t=0){return _e((n,r)=>{r.add(e.schedule(()=>n.subscribe(r),t))})}function Lh(e,t){if(!e)throw new Error("Iterable cannot be null");return new he(n=>{nn(n,t,()=>{const r=e[Symbol.asyncIterator]();nn(n,t,()=>{r.next().then(o=>{o.done?n.complete():n.next(o.value)})},0,!0)})})}function Ee(e,t){return t?function sM(e,t){if(null!=e){if(Mh(e))return function tM(e,t){return rt(e).pipe(kh(t),Fh(t))}(e,t);if(Eh(e))return function rM(e,t){return new he(n=>{let r=0;return t.schedule(function(){r===e.length?n.complete():(n.next(e[r++]),n.closed||this.schedule())})})}(e,t);if(bh(e))return function nM(e,t){return rt(e).pipe(kh(t),Fh(t))}(e,t);if(Ih(e))return Lh(e,t);if(Th(e))return function oM(e,t){return new he(n=>{let r;return nn(n,t,()=>{r=e[Ah](),nn(n,t,()=>{let o,i;try{({value:o,done:i}=r.next())}catch(s){return void n.error(s)}i?n.complete():n.next(o)},0,!0)}),()=>te(r?.return)&&r.return()})}(e,t);if(Rh(e))return function iM(e,t){return Lh(Nh(e),t)}(e,t)}throw Sh(e)}(e,t):rt(e)}class mt extends At{constructor(t){super(),this._value=t}get value(){return this.getValue()}_subscribe(t){const n=super._subscribe(t);return!n.closed&&t.next(this._value),n}getValue(){const{hasError:t,thrownError:n,_value:r}=this;if(t)throw n;return this._throwIfClosed(),r}next(t){super.next(this._value=t)}}function T(...e){return Ee(e,To(e))}function Vh(e={}){const{connector:t=(()=>new At),resetOnError:n=!0,resetOnComplete:r=!0,resetOnRefCountZero:o=!0}=e;return i=>{let s,a,u,c=0,l=!1,d=!1;const f=()=>{a?.unsubscribe(),a=void 0},h=()=>{f(),s=u=void 0,l=d=!1},p=()=>{const g=s;h(),g?.unsubscribe()};return _e((g,y)=>{c++,!d&&!l&&f();const D=u=u??t();y.add(()=>{c--,0===c&&!d&&!l&&(a=Gu(p,o))}),D.subscribe(y),!s&&c>0&&(s=new Ao({next:m=>D.next(m),error:m=>{d=!0,f(),a=Gu(h,n,m),D.error(m)},complete:()=>{l=!0,f(),a=Gu(h,r),D.complete()}}),rt(g).subscribe(s))})(i)}}function Gu(e,t,...n){if(!0===t)return void e();if(!1===t)return;const r=new Ao({next:()=>{r.unsubscribe(),e()}});return rt(t(...n)).subscribe(r)}function yt(e,t){return _e((n,r)=>{let o=null,i=0,s=!1;const a=()=>s&&!o&&r.complete();n.subscribe(Ce(r,u=>{o?.unsubscribe();let c=0;const l=i++;rt(e(u,l)).subscribe(o=Ce(r,d=>r.next(t?t(u,d,l,c++):d),()=>{o=null,a()}))},()=>{s=!0,a()}))})}function cM(e,t){return e===t}function J(e){for(let t in e)if(e[t]===J)return t;throw Error("Could not find renamed property on target object.")}function as(e,t){for(const n in t)t.hasOwnProperty(n)&&!e.hasOwnProperty(n)&&(e[n]=t[n])}function be(e){if("string"==typeof e)return e;if(Array.isArray(e))return"["+e.map(be).join(", ")+"]";if(null==e)return""+e;if(e.overriddenName)return`${e.overriddenName}`;if(e.name)return`${e.name}`;const t=e.toString();if(null==t)return""+t;const n=t.indexOf("\n");return-1===n?t:t.substring(0,n)}function qu(e,t){return null==e||""===e?null===t?"":t:null==t||""===t?e:e+" "+t}const lM=J({__forward_ref__:J});function re(e){return e.__forward_ref__=re,e.toString=function(){return be(this())},e}function x(e){return Wu(e)?e():e}function Wu(e){return"function"==typeof e&&e.hasOwnProperty(lM)&&e.__forward_ref__===re}function Zu(e){return e&&!!e.\u0275providers}class C extends Error{constructor(t,n){super(function us(e,t){return`NG0${Math.abs(e)}${t?": "+t:""}`}(t,n)),this.code=t}}function P(e){return"string"==typeof e?e:null==e?"":String(e)}function Yu(e,t){throw new C(-201,!1)}function vt(e,t){null==e&&function N(e,t,n,r){throw new Error(`ASSERTION ERROR: ${e}`+(null==r?"":` [Expected=> ${n} ${r} ${t} <=Actual]`))}(t,e,null,"!=")}function S(e){return{token:e.token,providedIn:e.providedIn||null,factory:e.factory,value:void 0}}function it(e){return{providers:e.providers||[],imports:e.imports||[]}}function cs(e){return Bh(e,ds)||Bh(e,Hh)}function Bh(e,t){return e.hasOwnProperty(t)?e[t]:null}function ls(e){return e&&(e.hasOwnProperty(Qu)||e.hasOwnProperty(vM))?e[Qu]:null}const ds=J({\u0275prov:J}),Qu=J({\u0275inj:J}),Hh=J({ngInjectableDef:J}),vM=J({ngInjectorDef:J});var $=function(e){return e[e.Default=0]="Default",e[e.Host=1]="Host",e[e.Self=2]="Self",e[e.SkipSelf=4]="SkipSelf",e[e.Optional=8]="Optional",e}($||{});let Xu;function Ye(e){const t=Xu;return Xu=e,t}function Uh(e,t,n){const r=cs(e);return r&&"root"==r.providedIn?void 0===r.value?r.value=r.factory():r.value:n&$.Optional?null:void 0!==t?t:void Yu(be(e))}const oe=globalThis;class M{constructor(t,n){this._desc=t,this.ngMetadataName="InjectionToken",this.\u0275prov=void 0,"number"==typeof n?this.__NG_ELEMENT_ID__=n:void 0!==n&&(this.\u0275prov=S({token:this,providedIn:n.providedIn||"root",factory:n.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}}const No={},nc="__NG_DI_FLAG__",fs="ngTempTokenPath",wM=/\n/gm,Gh="__source";let gr;function bn(e){const t=gr;return gr=e,t}function bM(e,t=$.Default){if(void 0===gr)throw new C(-203,!1);return null===gr?Uh(e,void 0,t):gr.get(e,t&$.Optional?null:void 0,t)}function I(e,t=$.Default){return(function $h(){return Xu}()||bM)(x(e),t)}function E(e,t=$.Default){return I(e,hs(t))}function hs(e){return typeof e>"u"||"number"==typeof e?e:0|(e.optional&&8)|(e.host&&1)|(e.self&&2)|(e.skipSelf&&4)}function rc(e){const t=[];for(let n=0;n<e.length;n++){const r=x(e[n]);if(Array.isArray(r)){if(0===r.length)throw new C(900,!1);let o,i=$.Default;for(let s=0;s<r.length;s++){const a=r[s],u=MM(a);"number"==typeof u?-1===u?o=a.token:i|=u:o=a}t.push(I(o,i))}else t.push(I(r))}return t}function Ro(e,t){return e[nc]=t,e.prototype[nc]=t,e}function MM(e){return e[nc]}function rn(e){return{toString:e}.toString()}var ps=function(e){return e[e.OnPush=0]="OnPush",e[e.Default=1]="Default",e}(ps||{}),Tt=function(e){return e[e.Emulated=0]="Emulated",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom",e}(Tt||{});const Bt={},W=[],gs=J({\u0275cmp:J}),oc=J({\u0275dir:J}),ic=J({\u0275pipe:J}),Wh=J({\u0275mod:J}),on=J({\u0275fac:J}),xo=J({__NG_ELEMENT_ID__:J}),Zh=J({__NG_ENV_ID__:J});function Yh(e,t,n){let r=e.length;for(;;){const o=e.indexOf(t,n);if(-1===o)return o;if(0===o||e.charCodeAt(o-1)<=32){const i=t.length;if(o+i===r||e.charCodeAt(o+i)<=32)return o}n=o+1}}function sc(e,t,n){let r=0;for(;r<n.length;){const o=n[r];if("number"==typeof o){if(0!==o)break;r++;const i=n[r++],s=n[r++],a=n[r++];e.setAttribute(t,s,a,i)}else{const i=o,s=n[++r];Xh(i)?e.setProperty(t,i,s):e.setAttribute(t,i,s),r++}}return r}function Qh(e){return 3===e||4===e||6===e}function Xh(e){return 64===e.charCodeAt(0)}function Oo(e,t){if(null!==t&&0!==t.length)if(null===e||0===e.length)e=t.slice();else{let n=-1;for(let r=0;r<t.length;r++){const o=t[r];"number"==typeof o?n=o:0===n||Jh(e,n,o,null,-1===n||2===n?t[++r]:null)}}return e}function Jh(e,t,n,r,o){let i=0,s=e.length;if(-1===t)s=-1;else for(;i<e.length;){const a=e[i++];if("number"==typeof a){if(a===t){s=-1;break}if(a>t){s=i-1;break}}}for(;i<e.length;){const a=e[i];if("number"==typeof a)break;if(a===n){if(null===r)return void(null!==o&&(e[i+1]=o));if(r===e[i+1])return void(e[i+2]=o)}i++,null!==r&&i++,null!==o&&i++}-1!==s&&(e.splice(s,0,t),i=s+1),e.splice(i++,0,n),null!==r&&e.splice(i++,0,r),null!==o&&e.splice(i++,0,o)}const Kh="ng-template";function AM(e,t,n){let r=0,o=!0;for(;r<e.length;){let i=e[r++];if("string"==typeof i&&o){const s=e[r++];if(n&&"class"===i&&-1!==Yh(s.toLowerCase(),t,0))return!0}else{if(1===i){for(;r<e.length&&"string"==typeof(i=e[r++]);)if(i.toLowerCase()===t)return!0;return!1}"number"==typeof i&&(o=!1)}}return!1}function ep(e){return 4===e.type&&e.value!==Kh}function TM(e,t,n){return t===(4!==e.type||n?e.value:Kh)}function NM(e,t,n){let r=4;const o=e.attrs||[],i=function OM(e){for(let t=0;t<e.length;t++)if(Qh(e[t]))return t;return e.length}(o);let s=!1;for(let a=0;a<t.length;a++){const u=t[a];if("number"!=typeof u){if(!s)if(4&r){if(r=2|1&r,""!==u&&!TM(e,u,n)||""===u&&1===t.length){if(Nt(r))return!1;s=!0}}else{const c=8&r?u:t[++a];if(8&r&&null!==e.attrs){if(!AM(e.attrs,c,n)){if(Nt(r))return!1;s=!0}continue}const d=RM(8&r?"class":u,o,ep(e),n);if(-1===d){if(Nt(r))return!1;s=!0;continue}if(""!==c){let f;f=d>i?"":o[d+1].toLowerCase();const h=8&r?f:null;if(h&&-1!==Yh(h,c,0)||2&r&&c!==f){if(Nt(r))return!1;s=!0}}}}else{if(!s&&!Nt(r)&&!Nt(u))return!1;if(s&&Nt(u))continue;s=!1,r=u|1&r}}return Nt(r)||s}function Nt(e){return 0==(1&e)}function RM(e,t,n,r){if(null===t)return-1;let o=0;if(r||!n){let i=!1;for(;o<t.length;){const s=t[o];if(s===e)return o;if(3===s||6===s)i=!0;else{if(1===s||2===s){let a=t[++o];for(;"string"==typeof a;)a=t[++o];continue}if(4===s)break;if(0===s){o+=4;continue}}o+=i?1:2}return-1}return function PM(e,t){let n=e.indexOf(4);if(n>-1)for(n++;n<e.length;){const r=e[n];if("number"==typeof r)return-1;if(r===t)return n;n++}return-1}(t,e)}function tp(e,t,n=!1){for(let r=0;r<t.length;r++)if(NM(e,t[r],n))return!0;return!1}function np(e,t){return e?":not("+t.trim()+")":t}function kM(e){let t=e[0],n=1,r=2,o="",i=!1;for(;n<e.length;){let s=e[n];if("string"==typeof s)if(2&r){const a=e[++n];o+="["+s+(a.length>0?'="'+a+'"':"")+"]"}else 8&r?o+="."+s:4&r&&(o+=" "+s);else""!==o&&!Nt(s)&&(t+=np(i,o),o=""),r=s,i=i||!Nt(r);n++}return""!==o&&(t+=np(i,o)),t}function ms(e){return rn(()=>{const t=op(e),n={...t,decls:e.decls,vars:e.vars,template:e.template,consts:e.consts||null,ngContentSelectors:e.ngContentSelectors,onPush:e.changeDetection===ps.OnPush,directiveDefs:null,pipeDefs:null,dependencies:t.standalone&&e.dependencies||null,getStandaloneInjector:null,signals:e.signals??!1,data:e.data||{},encapsulation:e.encapsulation||Tt.Emulated,styles:e.styles||W,_:null,schemas:e.schemas||null,tView:null,id:""};ip(n);const r=e.dependencies;return n.directiveDefs=ys(r,!1),n.pipeDefs=ys(r,!0),n.id=function zM(e){let t=0;const n=[e.selectors,e.ngContentSelectors,e.hostVars,e.hostAttrs,e.consts,e.vars,e.decls,e.encapsulation,e.standalone,e.signals,e.exportAs,JSON.stringify(e.inputs),JSON.stringify(e.outputs),Object.getOwnPropertyNames(e.type.prototype),!!e.contentQueries,!!e.viewQuery].join("|");for(const o of n)t=Math.imul(31,t)+o.charCodeAt(0)<<0;return t+=2147483648,"c"+t}(n),n})}function BM(e){return G(e)||Te(e)}function HM(e){return null!==e}function Dt(e){return rn(()=>({type:e.type,bootstrap:e.bootstrap||W,declarations:e.declarations||W,imports:e.imports||W,exports:e.exports||W,transitiveCompileScopes:null,schemas:e.schemas||null,id:e.id||null}))}function rp(e,t){if(null==e)return Bt;const n={};for(const r in e)if(e.hasOwnProperty(r)){let o=e[r],i=o;Array.isArray(o)&&(i=o[1],o=o[0]),n[o]=r,t&&(t[o]=i)}return n}function O(e){return rn(()=>{const t=op(e);return ip(t),t})}function Qe(e){return{type:e.type,name:e.name,factory:null,pure:!1!==e.pure,standalone:!0===e.standalone,onDestroy:e.type.prototype.ngOnDestroy||null}}function G(e){return e[gs]||null}function Te(e){return e[oc]||null}function je(e){return e[ic]||null}function st(e,t){const n=e[Wh]||null;if(!n&&!0===t)throw new Error(`Type ${be(e)} does not have '\u0275mod' property.`);return n}function op(e){const t={};return{type:e.type,providersResolver:null,factory:null,hostBindings:e.hostBindings||null,hostVars:e.hostVars||0,hostAttrs:e.hostAttrs||null,contentQueries:e.contentQueries||null,declaredInputs:t,inputTransforms:null,inputConfig:e.inputs||Bt,exportAs:e.exportAs||null,standalone:!0===e.standalone,signals:!0===e.signals,selectors:e.selectors||W,viewQuery:e.viewQuery||null,features:e.features||null,setInput:null,findHostDirectiveDefs:null,hostDirectives:null,inputs:rp(e.inputs,t),outputs:rp(e.outputs)}}function ip(e){e.features?.forEach(t=>t(e))}function ys(e,t){if(!e)return null;const n=t?je:BM;return()=>("function"==typeof e?e():e).map(r=>n(r)).filter(HM)}const pe=0,_=1,L=2,ce=3,Rt=4,Po=5,Pe=6,yr=7,ye=8,Mn=9,vr=10,F=11,Fo=12,sp=13,Dr=14,ve=15,ko=16,Cr=17,Ht=18,Lo=19,ap=20,In=21,sn=22,Vo=23,jo=24,U=25,ac=1,up=2,$t=7,wr=9,Ne=11;function Xe(e){return Array.isArray(e)&&"object"==typeof e[ac]}function Be(e){return Array.isArray(e)&&!0===e[ac]}function uc(e){return 0!=(4&e.flags)}function qn(e){return e.componentOffset>-1}function Ds(e){return 1==(1&e.flags)}function xt(e){return!!e.template}function cc(e){return 0!=(512&e[L])}function Wn(e,t){return e.hasOwnProperty(on)?e[on]:null}let Re=null,Cs=!1;function Ct(e){const t=Re;return Re=e,t}const dp={version:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{}};function hp(e){if(!Ho(e)||e.dirty){if(!e.producerMustRecompute(e)&&!mp(e))return void(e.dirty=!1);e.producerRecomputeValue(e),e.dirty=!1}}function gp(e){e.dirty=!0,function pp(e){if(void 0===e.liveConsumerNode)return;const t=Cs;Cs=!0;try{for(const n of e.liveConsumerNode)n.dirty||gp(n)}finally{Cs=t}}(e),e.consumerMarkedDirty?.(e)}function dc(e){return e&&(e.nextProducerIndex=0),Ct(e)}function fc(e,t){if(Ct(t),e&&void 0!==e.producerNode&&void 0!==e.producerIndexOfThis&&void 0!==e.producerLastReadVersion){if(Ho(e))for(let n=e.nextProducerIndex;n<e.producerNode.length;n++)ws(e.producerNode[n],e.producerIndexOfThis[n]);for(;e.producerNode.length>e.nextProducerIndex;)e.producerNode.pop(),e.producerLastReadVersion.pop(),e.producerIndexOfThis.pop()}}function mp(e){_r(e);for(let t=0;t<e.producerNode.length;t++){const n=e.producerNode[t],r=e.producerLastReadVersion[t];if(r!==n.version||(hp(n),r!==n.version))return!0}return!1}function yp(e){if(_r(e),Ho(e))for(let t=0;t<e.producerNode.length;t++)ws(e.producerNode[t],e.producerIndexOfThis[t]);e.producerNode.length=e.producerLastReadVersion.length=e.producerIndexOfThis.length=0,e.liveConsumerNode&&(e.liveConsumerNode.length=e.liveConsumerIndexOfThis.length=0)}function ws(e,t){if(function Dp(e){e.liveConsumerNode??=[],e.liveConsumerIndexOfThis??=[]}(e),_r(e),1===e.liveConsumerNode.length)for(let r=0;r<e.producerNode.length;r++)ws(e.producerNode[r],e.producerIndexOfThis[r]);const n=e.liveConsumerNode.length-1;if(e.liveConsumerNode[t]=e.liveConsumerNode[n],e.liveConsumerIndexOfThis[t]=e.liveConsumerIndexOfThis[n],e.liveConsumerNode.length--,e.liveConsumerIndexOfThis.length--,t<e.liveConsumerNode.length){const r=e.liveConsumerIndexOfThis[t],o=e.liveConsumerNode[t];_r(o),o.producerIndexOfThis[r]=t}}function Ho(e){return e.consumerIsAlwaysLive||(e?.liveConsumerNode?.length??0)>0}function _r(e){e.producerNode??=[],e.producerIndexOfThis??=[],e.producerLastReadVersion??=[]}let Cp=null;const bp=()=>{},rI=(()=>({...dp,consumerIsAlwaysLive:!0,consumerAllowSignalWrites:!1,consumerMarkedDirty:e=>{e.schedule(e.ref)},hasRun:!1,cleanupFn:bp}))();class oI{constructor(t,n,r){this.previousValue=t,this.currentValue=n,this.firstChange=r}isFirstChange(){return this.firstChange}}function wt(){return Mp}function Mp(e){return e.type.prototype.ngOnChanges&&(e.setInput=sI),iI}function iI(){const e=Sp(this),t=e?.current;if(t){const n=e.previous;if(n===Bt)e.previous=t;else for(let r in t)n[r]=t[r];e.current=null,this.ngOnChanges(t)}}function sI(e,t,n,r){const o=this.declaredInputs[n],i=Sp(e)||function aI(e,t){return e[Ip]=t}(e,{previous:Bt,current:null}),s=i.current||(i.current={}),a=i.previous,u=a[o];s[o]=new oI(u&&u.currentValue,t,a===Bt),e[r]=t}wt.ngInherit=!0;const Ip="__ngSimpleChanges__";function Sp(e){return e[Ip]||null}const Ut=function(e,t,n){};function ie(e){for(;Array.isArray(e);)e=e[pe];return e}function _s(e,t){return ie(t[e])}function Je(e,t){return ie(t[e.index])}function Np(e,t){return e.data[t]}function at(e,t){const n=t[e];return Xe(n)?n:n[pe]}function An(e,t){return null==t?null:e[t]}function Rp(e){e[Cr]=0}function hI(e){1024&e[L]||(e[L]|=1024,Op(e,1))}function xp(e){1024&e[L]&&(e[L]&=-1025,Op(e,-1))}function Op(e,t){let n=e[ce];if(null===n)return;n[Po]+=t;let r=n;for(n=n[ce];null!==n&&(1===t&&1===r[Po]||-1===t&&0===r[Po]);)n[Po]+=t,r=n,n=n[ce]}const R={lFrame:zp(null),bindingsEnabled:!0,skipHydrationRootTNode:null};function kp(){return R.bindingsEnabled}function v(){return R.lFrame.lView}function q(){return R.lFrame.tView}function xe(){let e=Lp();for(;null!==e&&64===e.type;)e=e.parent;return e}function Lp(){return R.lFrame.currentTNode}function zt(e,t){const n=R.lFrame;n.currentTNode=e,n.isParent=t}function yc(){return R.lFrame.isParent}function Mr(){return R.lFrame.bindingIndex++}function SI(e,t){const n=R.lFrame;n.bindingIndex=n.bindingRootIndex=e,Dc(t)}function Dc(e){R.lFrame.currentDirectiveIndex=e}function wc(e){R.lFrame.currentQueryIndex=e}function TI(e){const t=e[_];return 2===t.type?t.declTNode:1===t.type?e[Pe]:null}function $p(e,t,n){if(n&$.SkipSelf){let o=t,i=e;for(;!(o=o.parent,null!==o||n&$.Host||(o=TI(i),null===o||(i=i[Dr],10&o.type))););if(null===o)return!1;t=o,e=i}const r=R.lFrame=Up();return r.currentTNode=t,r.lView=e,!0}function _c(e){const t=Up(),n=e[_];R.lFrame=t,t.currentTNode=n.firstChild,t.lView=e,t.tView=n,t.contextLView=e,t.bindingIndex=n.bindingStartIndex,t.inI18n=!1}function Up(){const e=R.lFrame,t=null===e?null:e.child;return null===t?zp(e):t}function zp(e){const t={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:e,child:null,inI18n:!1};return null!==e&&(e.child=t),t}function Gp(){const e=R.lFrame;return R.lFrame=e.parent,e.currentTNode=null,e.lView=null,e}const qp=Gp;function Ec(){const e=Gp();e.isParent=!0,e.tView=null,e.selectedIndex=-1,e.contextLView=null,e.elementDepthCount=0,e.currentDirectiveIndex=-1,e.currentNamespace=null,e.bindingRootIndex=-1,e.bindingIndex=-1,e.currentQueryIndex=0}function $e(){return R.lFrame.selectedIndex}function Zn(e){R.lFrame.selectedIndex=e}let Zp=!0;function Es(){return Zp}function Tn(e){Zp=e}function bs(e,t){for(let n=t.directiveStart,r=t.directiveEnd;n<r;n++){const i=e.data[n].type.prototype,{ngAfterContentInit:s,ngAfterContentChecked:a,ngAfterViewInit:u,ngAfterViewChecked:c,ngOnDestroy:l}=i;s&&(e.contentHooks??=[]).push(-n,s),a&&((e.contentHooks??=[]).push(n,a),(e.contentCheckHooks??=[]).push(n,a)),u&&(e.viewHooks??=[]).push(-n,u),c&&((e.viewHooks??=[]).push(n,c),(e.viewCheckHooks??=[]).push(n,c)),null!=l&&(e.destroyHooks??=[]).push(n,l)}}function Ms(e,t,n){Yp(e,t,3,n)}function Is(e,t,n,r){(3&e[L])===n&&Yp(e,t,n,r)}function bc(e,t){let n=e[L];(3&n)===t&&(n&=8191,n+=1,e[L]=n)}function Yp(e,t,n,r){const i=r??-1,s=t.length-1;let a=0;for(let u=void 0!==r?65535&e[Cr]:0;u<s;u++)if("number"==typeof t[u+1]){if(a=t[u],null!=r&&a>=r)break}else t[u]<0&&(e[Cr]+=65536),(a<i||-1==i)&&(LI(e,n,t,u),e[Cr]=(**********&e[Cr])+u+2),u++}function Qp(e,t){Ut(4,e,t);const n=Ct(null);try{t.call(e)}finally{Ct(n),Ut(5,e,t)}}function LI(e,t,n,r){const o=n[r]<0,i=n[r+1],a=e[o?-n[r]:n[r]];o?e[L]>>13<e[Cr]>>16&&(3&e[L])===t&&(e[L]+=8192,Qp(a,i)):Qp(a,i)}const Ir=-1;class Uo{constructor(t,n,r){this.factory=t,this.resolving=!1,this.canSeeViewProviders=n,this.injectImpl=r}}function Ic(e){return e!==Ir}function zo(e){return 32767&e}function Go(e,t){let n=function HI(e){return e>>16}(e),r=t;for(;n>0;)r=r[Dr],n--;return r}let Sc=!0;function Ss(e){const t=Sc;return Sc=e,t}const Xp=255,Jp=5;let $I=0;const Gt={};function As(e,t){const n=Kp(e,t);if(-1!==n)return n;const r=t[_];r.firstCreatePass&&(e.injectorIndex=t.length,Ac(r.data,e),Ac(t,null),Ac(r.blueprint,null));const o=Ts(e,t),i=e.injectorIndex;if(Ic(o)){const s=zo(o),a=Go(o,t),u=a[_].data;for(let c=0;c<8;c++)t[i+c]=a[s+c]|u[s+c]}return t[i+8]=o,i}function Ac(e,t){e.push(0,0,0,0,0,0,0,0,t)}function Kp(e,t){return-1===e.injectorIndex||e.parent&&e.parent.injectorIndex===e.injectorIndex||null===t[e.injectorIndex+8]?-1:e.injectorIndex}function Ts(e,t){if(e.parent&&-1!==e.parent.injectorIndex)return e.parent.injectorIndex;let n=0,r=null,o=t;for(;null!==o;){if(r=sg(o),null===r)return Ir;if(n++,o=o[Dr],-1!==r.injectorIndex)return r.injectorIndex|n<<16}return Ir}function Tc(e,t,n){!function UI(e,t,n){let r;"string"==typeof n?r=n.charCodeAt(0)||0:n.hasOwnProperty(xo)&&(r=n[xo]),null==r&&(r=n[xo]=$I++);const o=r&Xp;t.data[e+(o>>Jp)]|=1<<o}(e,t,n)}function eg(e,t,n){if(n&$.Optional||void 0!==e)return e;Yu()}function tg(e,t,n,r){if(n&$.Optional&&void 0===r&&(r=null),!(n&($.Self|$.Host))){const o=e[Mn],i=Ye(void 0);try{return o?o.get(t,r,n&$.Optional):Uh(t,r,n&$.Optional)}finally{Ye(i)}}return eg(r,0,n)}function ng(e,t,n,r=$.Default,o){if(null!==e){if(2048&t[L]&&!(r&$.Self)){const s=function YI(e,t,n,r,o){let i=e,s=t;for(;null!==i&&null!==s&&2048&s[L]&&!(512&s[L]);){const a=rg(i,s,n,r|$.Self,Gt);if(a!==Gt)return a;let u=i.parent;if(!u){const c=s[ap];if(c){const l=c.get(n,Gt,r);if(l!==Gt)return l}u=sg(s),s=s[Dr]}i=u}return o}(e,t,n,r,Gt);if(s!==Gt)return s}const i=rg(e,t,n,r,Gt);if(i!==Gt)return i}return tg(t,n,r,o)}function rg(e,t,n,r,o){const i=function qI(e){if("string"==typeof e)return e.charCodeAt(0)||0;const t=e.hasOwnProperty(xo)?e[xo]:void 0;return"number"==typeof t?t>=0?t&Xp:ZI:t}(n);if("function"==typeof i){if(!$p(t,e,r))return r&$.Host?eg(o,0,r):tg(t,n,r,o);try{let s;if(s=i(r),null!=s||r&$.Optional)return s;Yu()}finally{qp()}}else if("number"==typeof i){let s=null,a=Kp(e,t),u=Ir,c=r&$.Host?t[ve][Pe]:null;for((-1===a||r&$.SkipSelf)&&(u=-1===a?Ts(e,t):t[a+8],u!==Ir&&ig(r,!1)?(s=t[_],a=zo(u),t=Go(u,t)):a=-1);-1!==a;){const l=t[_];if(og(i,a,l.data)){const d=GI(a,t,n,s,r,c);if(d!==Gt)return d}u=t[a+8],u!==Ir&&ig(r,t[_].data[a+8]===c)&&og(i,a,t)?(s=l,a=zo(u),t=Go(u,t)):a=-1}}return o}function GI(e,t,n,r,o,i){const s=t[_],a=s.data[e+8],l=function Ns(e,t,n,r,o){const i=e.providerIndexes,s=t.data,a=1048575&i,u=e.directiveStart,l=i>>20,f=o?a+l:e.directiveEnd;for(let h=r?a:a+l;h<f;h++){const p=s[h];if(h<u&&n===p||h>=u&&p.type===n)return h}if(o){const h=s[u];if(h&&xt(h)&&h.type===n)return u}return null}(a,s,n,null==r?qn(a)&&Sc:r!=s&&0!=(3&a.type),o&$.Host&&i===a);return null!==l?Yn(t,s,l,a):Gt}function Yn(e,t,n,r){let o=e[n];const i=t.data;if(function VI(e){return e instanceof Uo}(o)){const s=o;s.resolving&&function dM(e,t){const n=t?`. Dependency path: ${t.join(" > ")} > ${e}`:"";throw new C(-200,`Circular dependency in DI detected for ${e}${n}`)}(function X(e){return"function"==typeof e?e.name||e.toString():"object"==typeof e&&null!=e&&"function"==typeof e.type?e.type.name||e.type.toString():P(e)}(i[n]));const a=Ss(s.canSeeViewProviders);s.resolving=!0;const c=s.injectImpl?Ye(s.injectImpl):null;$p(e,r,$.Default);try{o=e[n]=s.factory(void 0,i,e,r),t.firstCreatePass&&n>=r.directiveStart&&function kI(e,t,n){const{ngOnChanges:r,ngOnInit:o,ngDoCheck:i}=t.type.prototype;if(r){const s=Mp(t);(n.preOrderHooks??=[]).push(e,s),(n.preOrderCheckHooks??=[]).push(e,s)}o&&(n.preOrderHooks??=[]).push(0-e,o),i&&((n.preOrderHooks??=[]).push(e,i),(n.preOrderCheckHooks??=[]).push(e,i))}(n,i[n],t)}finally{null!==c&&Ye(c),Ss(a),s.resolving=!1,qp()}}return o}function og(e,t,n){return!!(n[t+(e>>Jp)]&1<<e)}function ig(e,t){return!(e&$.Self||e&$.Host&&t)}class Ue{constructor(t,n){this._tNode=t,this._lView=n}get(t,n,r){return ng(this._tNode,this._lView,t,hs(r),n)}}function ZI(){return new Ue(xe(),v())}function Oe(e){return rn(()=>{const t=e.prototype.constructor,n=t[on]||Nc(t),r=Object.prototype;let o=Object.getPrototypeOf(e.prototype).constructor;for(;o&&o!==r;){const i=o[on]||Nc(o);if(i&&i!==n)return i;o=Object.getPrototypeOf(o)}return i=>new i})}function Nc(e){return Wu(e)?()=>{const t=Nc(x(e));return t&&t()}:Wn(e)}function sg(e){const t=e[_],n=t.type;return 2===n?t.declTNode:1===n?e[Pe]:null}const Ar="__parameters__";function Nr(e,t,n){return rn(()=>{const r=function Rc(e){return function(...n){if(e){const r=e(...n);for(const o in r)this[o]=r[o]}}}(t);function o(...i){if(this instanceof o)return r.apply(this,i),this;const s=new o(...i);return a.annotation=s,a;function a(u,c,l){const d=u.hasOwnProperty(Ar)?u[Ar]:Object.defineProperty(u,Ar,{value:[]})[Ar];for(;d.length<=l;)d.push(null);return(d[l]=d[l]||[]).push(s),u}}return n&&(o.prototype=Object.create(n.prototype)),o.prototype.ngMetadataName=e,o.annotationCls=o,o})}function xr(e,t){e.forEach(n=>Array.isArray(n)?xr(n,t):t(n))}function ug(e,t,n){t>=e.length?e.push(n):e.splice(t,0,n)}function xs(e,t){return t>=e.length-1?e.pop():e.splice(t,1)[0]}function ut(e,t,n){let r=Or(e,t);return r>=0?e[1|r]=n:(r=~r,function nS(e,t,n,r){let o=e.length;if(o==t)e.push(n,r);else if(1===o)e.push(r,e[0]),e[0]=n;else{for(o--,e.push(e[o-1],e[o]);o>t;)e[o]=e[o-2],o--;e[t]=n,e[t+1]=r}}(e,r,t,n)),r}function xc(e,t){const n=Or(e,t);if(n>=0)return e[1|n]}function Or(e,t){return function cg(e,t,n){let r=0,o=e.length>>n;for(;o!==r;){const i=r+(o-r>>1),s=e[i<<n];if(t===s)return i<<n;s>t?o=i:r=i+1}return~(o<<n)}(e,t,1)}const Ps=Ro(Nr("Optional"),8),Fs=Ro(Nr("SkipSelf"),4);function Bs(e){return 128==(128&e.flags)}var Nn=function(e){return e[e.Important=1]="Important",e[e.DashCase=2]="DashCase",e}(Nn||{});const Lc=new Map;let IS=0;const jc="__ngContext__";function Fe(e,t){Xe(t)?(e[jc]=t[Lo],function AS(e){Lc.set(e[Lo],e)}(t)):e[jc]=t}let Bc;function Hc(e,t){return Bc(e,t)}function Xo(e){const t=e[ce];return Be(t)?t[ce]:t}function Tg(e){return Rg(e[Fo])}function Ng(e){return Rg(e[Rt])}function Rg(e){for(;null!==e&&!Be(e);)e=e[Rt];return e}function kr(e,t,n,r,o){if(null!=r){let i,s=!1;Be(r)?i=r:Xe(r)&&(s=!0,r=r[pe]);const a=ie(r);0===e&&null!==n?null==o?Fg(t,n,a):Qn(t,n,a,o||null,!0):1===e&&null!==n?Qn(t,n,a,o||null,!0):2===e?function Ws(e,t,n){const r=Gs(e,t);r&&function WS(e,t,n,r){e.removeChild(t,n,r)}(e,r,t,n)}(t,a,s):3===e&&t.destroyNode(a),null!=i&&function QS(e,t,n,r,o){const i=n[$t];i!==ie(n)&&kr(t,e,r,i,o);for(let a=Ne;a<n.length;a++){const u=n[a];Ko(u[_],u,e,t,r,i)}}(t,e,i,n,o)}}function Us(e,t,n){return e.createElement(t,n)}function Og(e,t){const n=e[wr],r=n.indexOf(t);xp(t),n.splice(r,1)}function zs(e,t){if(e.length<=Ne)return;const n=Ne+t,r=e[n];if(r){const o=r[ko];null!==o&&o!==e&&Og(o,r),t>0&&(e[n-1][Rt]=r[Rt]);const i=xs(e,Ne+t);!function jS(e,t){Ko(e,t,t[F],2,null,null),t[pe]=null,t[Pe]=null}(r[_],r);const s=i[Ht];null!==s&&s.detachView(i[_]),r[ce]=null,r[Rt]=null,r[L]&=-129}return r}function Uc(e,t){if(!(256&t[L])){const n=t[F];t[Vo]&&yp(t[Vo]),t[jo]&&yp(t[jo]),n.destroyNode&&Ko(e,t,n,3,null,null),function $S(e){let t=e[Fo];if(!t)return zc(e[_],e);for(;t;){let n=null;if(Xe(t))n=t[Fo];else{const r=t[Ne];r&&(n=r)}if(!n){for(;t&&!t[Rt]&&t!==e;)Xe(t)&&zc(t[_],t),t=t[ce];null===t&&(t=e),Xe(t)&&zc(t[_],t),n=t&&t[Rt]}t=n}}(t)}}function zc(e,t){if(!(256&t[L])){t[L]&=-129,t[L]|=256,function qS(e,t){let n;if(null!=e&&null!=(n=e.destroyHooks))for(let r=0;r<n.length;r+=2){const o=t[n[r]];if(!(o instanceof Uo)){const i=n[r+1];if(Array.isArray(i))for(let s=0;s<i.length;s+=2){const a=o[i[s]],u=i[s+1];Ut(4,a,u);try{u.call(a)}finally{Ut(5,a,u)}}else{Ut(4,o,i);try{i.call(o)}finally{Ut(5,o,i)}}}}}(e,t),function GS(e,t){const n=e.cleanup,r=t[yr];if(null!==n)for(let i=0;i<n.length-1;i+=2)if("string"==typeof n[i]){const s=n[i+3];s>=0?r[s]():r[-s].unsubscribe(),i+=2}else n[i].call(r[n[i+1]]);null!==r&&(t[yr]=null);const o=t[In];if(null!==o){t[In]=null;for(let i=0;i<o.length;i++)(0,o[i])()}}(e,t),1===t[_].type&&t[F].destroy();const n=t[ko];if(null!==n&&Be(t[ce])){n!==t[ce]&&Og(n,t);const r=t[Ht];null!==r&&r.detachView(e)}!function TS(e){Lc.delete(e[Lo])}(t)}}function Gc(e,t,n){return function Pg(e,t,n){let r=t;for(;null!==r&&40&r.type;)r=(t=r).parent;if(null===r)return n[pe];{const{componentOffset:o}=r;if(o>-1){const{encapsulation:i}=e.data[r.directiveStart+o];if(i===Tt.None||i===Tt.Emulated)return null}return Je(r,n)}}(e,t.parent,n)}function Qn(e,t,n,r,o){e.insertBefore(t,n,r,o)}function Fg(e,t,n){e.appendChild(t,n)}function kg(e,t,n,r,o){null!==r?Qn(e,t,n,r,o):Fg(e,t,n)}function Gs(e,t){return e.parentNode(t)}let qc,Qc,jg=function Vg(e,t,n){return 40&e.type?Je(e,n):null};function qs(e,t,n,r){const o=Gc(e,r,t),i=t[F],a=function Lg(e,t,n){return jg(e,t,n)}(r.parent||t[Pe],r,t);if(null!=o)if(Array.isArray(n))for(let u=0;u<n.length;u++)kg(i,o,n[u],a,!1);else kg(i,o,n,a,!1);void 0!==qc&&qc(i,r,t,n,o)}function Jo(e,t){if(null!==t){const n=t.type;if(3&n)return Je(t,e);if(4&n)return Wc(-1,e[t.index]);if(8&n){const r=t.child;if(null!==r)return Jo(e,r);{const o=e[t.index];return Be(o)?Wc(-1,o):ie(o)}}if(32&n)return Hc(t,e)()||ie(e[t.index]);{const r=Hg(e,t);return null!==r?Array.isArray(r)?r[0]:Jo(Xo(e[ve]),r):Jo(e,t.next)}}return null}function Hg(e,t){return null!==t?e[ve][Pe].projection[t.projection]:null}function Wc(e,t){const n=Ne+e+1;if(n<t.length){const r=t[n],o=r[_].firstChild;if(null!==o)return Jo(r,o)}return t[$t]}function Zc(e,t,n,r,o,i,s){for(;null!=n;){const a=r[n.index],u=n.type;if(s&&0===t&&(a&&Fe(ie(a),r),n.flags|=2),32!=(32&n.flags))if(8&u)Zc(e,t,n.child,r,o,i,!1),kr(t,e,o,a,i);else if(32&u){const c=Hc(n,r);let l;for(;l=c();)kr(t,e,o,l,i);kr(t,e,o,a,i)}else 16&u?Ug(e,t,r,n,o,i):kr(t,e,o,a,i);n=s?n.projectionNext:n.next}}function Ko(e,t,n,r,o,i){Zc(n,r,e.firstChild,t,o,i,!1)}function Ug(e,t,n,r,o,i){const s=n[ve],u=s[Pe].projection[r.projection];if(Array.isArray(u))for(let c=0;c<u.length;c++)kr(t,e,o,u[c],i);else{let c=u;const l=s[ce];Bs(r)&&(c.flags|=128),Zc(e,t,c,l,o,i,!0)}}function zg(e,t,n){""===n?e.removeAttribute(t,"class"):e.setAttribute(t,"class",n)}function Gg(e,t,n){const{mergedAttrs:r,classes:o,styles:i}=n;null!==r&&sc(e,t,r),null!==o&&zg(e,t,o),null!==i&&function JS(e,t,n){e.setAttribute(t,"style",n)}(e,t,i)}class Yg{constructor(t){this.changingThisBreaksApplicationSecurity=t}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see https://g.co/ng/security#xss)`}}const ri=new M("ENVIRONMENT_INITIALIZER"),im=new M("INJECTOR",-1),sm=new M("INJECTOR_DEF_TYPES");class nl{get(t,n=No){if(n===No){const r=new Error(`NullInjectorError: No provider for ${be(t)}!`);throw r.name="NullInjectorError",r}return n}}function I0(...e){return{\u0275providers:am(0,e),\u0275fromNgModule:!0}}function am(e,...t){const n=[],r=new Set;let o;const i=s=>{n.push(s)};return xr(t,s=>{const a=s;Xs(a,i,[],r)&&(o||=[],o.push(a))}),void 0!==o&&um(o,i),n}function um(e,t){for(let n=0;n<e.length;n++){const{ngModule:r,providers:o}=e[n];ol(o,i=>{t(i,r)})}}function Xs(e,t,n,r){if(!(e=x(e)))return!1;let o=null,i=ls(e);const s=!i&&G(e);if(i||s){if(s&&!s.standalone)return!1;o=e}else{const u=e.ngModule;if(i=ls(u),!i)return!1;o=u}const a=r.has(o);if(s){if(a)return!1;if(r.add(o),s.dependencies){const u="function"==typeof s.dependencies?s.dependencies():s.dependencies;for(const c of u)Xs(c,t,n,r)}}else{if(!i)return!1;{if(null!=i.imports&&!a){let c;r.add(o);try{xr(i.imports,l=>{Xs(l,t,n,r)&&(c||=[],c.push(l))})}finally{}void 0!==c&&um(c,t)}if(!a){const c=Wn(o)||(()=>new o);t({provide:o,useFactory:c,deps:W},o),t({provide:sm,useValue:o,multi:!0},o),t({provide:ri,useValue:()=>I(o),multi:!0},o)}const u=i.providers;if(null!=u&&!a){const c=e;ol(u,l=>{t(l,c)})}}}return o!==e&&void 0!==e.providers}function ol(e,t){for(let n of e)Zu(n)&&(n=n.\u0275providers),Array.isArray(n)?ol(n,t):t(n)}const S0=J({provide:String,useValue:J});function il(e){return null!==e&&"object"==typeof e&&S0 in e}function Xn(e){return"function"==typeof e}const sl=new M("Set Injector scope."),Js={},T0={};let al;function Ks(){return void 0===al&&(al=new nl),al}class ct{}class Br extends ct{get destroyed(){return this._destroyed}constructor(t,n,r,o){super(),this.parent=n,this.source=r,this.scopes=o,this.records=new Map,this._ngOnDestroyHooks=new Set,this._onDestroyHooks=[],this._destroyed=!1,cl(t,s=>this.processProvider(s)),this.records.set(im,Hr(void 0,this)),o.has("environment")&&this.records.set(ct,Hr(void 0,this));const i=this.records.get(sl);null!=i&&"string"==typeof i.value&&this.scopes.add(i.value),this.injectorDefTypes=new Set(this.get(sm.multi,W,$.Self))}destroy(){this.assertNotDestroyed(),this._destroyed=!0;try{for(const n of this._ngOnDestroyHooks)n.ngOnDestroy();const t=this._onDestroyHooks;this._onDestroyHooks=[];for(const n of t)n()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear()}}onDestroy(t){return this.assertNotDestroyed(),this._onDestroyHooks.push(t),()=>this.removeOnDestroy(t)}runInContext(t){this.assertNotDestroyed();const n=bn(this),r=Ye(void 0);try{return t()}finally{bn(n),Ye(r)}}get(t,n=No,r=$.Default){if(this.assertNotDestroyed(),t.hasOwnProperty(Zh))return t[Zh](this);r=hs(r);const i=bn(this),s=Ye(void 0);try{if(!(r&$.SkipSelf)){let u=this.records.get(t);if(void 0===u){const c=function P0(e){return"function"==typeof e||"object"==typeof e&&e instanceof M}(t)&&cs(t);u=c&&this.injectableDefInScope(c)?Hr(ul(t),Js):null,this.records.set(t,u)}if(null!=u)return this.hydrate(t,u)}return(r&$.Self?Ks():this.parent).get(t,n=r&$.Optional&&n===No?null:n)}catch(a){if("NullInjectorError"===a.name){if((a[fs]=a[fs]||[]).unshift(be(t)),i)throw a;return function IM(e,t,n,r){const o=e[fs];throw t[Gh]&&o.unshift(t[Gh]),e.message=function SM(e,t,n,r=null){e=e&&"\n"===e.charAt(0)&&"\u0275"==e.charAt(1)?e.slice(2):e;let o=be(t);if(Array.isArray(t))o=t.map(be).join(" -> ");else if("object"==typeof t){let i=[];for(let s in t)if(t.hasOwnProperty(s)){let a=t[s];i.push(s+":"+("string"==typeof a?JSON.stringify(a):be(a)))}o=`{${i.join(", ")}}`}return`${n}${r?"("+r+")":""}[${o}]: ${e.replace(wM,"\n  ")}`}("\n"+e.message,o,n,r),e.ngTokenPath=o,e[fs]=null,e}(a,t,"R3InjectorError",this.source)}throw a}finally{Ye(s),bn(i)}}resolveInjectorInitializers(){const t=bn(this),n=Ye(void 0);try{const o=this.get(ri.multi,W,$.Self);for(const i of o)i()}finally{bn(t),Ye(n)}}toString(){const t=[],n=this.records;for(const r of n.keys())t.push(be(r));return`R3Injector[${t.join(", ")}]`}assertNotDestroyed(){if(this._destroyed)throw new C(205,!1)}processProvider(t){let n=Xn(t=x(t))?t:x(t&&t.provide);const r=function R0(e){return il(e)?Hr(void 0,e.useValue):Hr(dm(e),Js)}(t);if(Xn(t)||!0!==t.multi)this.records.get(n);else{let o=this.records.get(n);o||(o=Hr(void 0,Js,!0),o.factory=()=>rc(o.multi),this.records.set(n,o)),n=t,o.multi.push(t)}this.records.set(n,r)}hydrate(t,n){return n.value===Js&&(n.value=T0,n.value=n.factory()),"object"==typeof n.value&&n.value&&function O0(e){return null!==e&&"object"==typeof e&&"function"==typeof e.ngOnDestroy}(n.value)&&this._ngOnDestroyHooks.add(n.value),n.value}injectableDefInScope(t){if(!t.providedIn)return!1;const n=x(t.providedIn);return"string"==typeof n?"any"===n||this.scopes.has(n):this.injectorDefTypes.has(n)}removeOnDestroy(t){const n=this._onDestroyHooks.indexOf(t);-1!==n&&this._onDestroyHooks.splice(n,1)}}function ul(e){const t=cs(e),n=null!==t?t.factory:Wn(e);if(null!==n)return n;if(e instanceof M)throw new C(204,!1);if(e instanceof Function)return function N0(e){const t=e.length;if(t>0)throw function Zo(e,t){const n=[];for(let r=0;r<e;r++)n.push(t);return n}(t,"?"),new C(204,!1);const n=function yM(e){return e&&(e[ds]||e[Hh])||null}(e);return null!==n?()=>n.factory(e):()=>new e}(e);throw new C(204,!1)}function dm(e,t,n){let r;if(Xn(e)){const o=x(e);return Wn(o)||ul(o)}if(il(e))r=()=>x(e.useValue);else if(function lm(e){return!(!e||!e.useFactory)}(e))r=()=>e.useFactory(...rc(e.deps||[]));else if(function cm(e){return!(!e||!e.useExisting)}(e))r=()=>I(x(e.useExisting));else{const o=x(e&&(e.useClass||e.provide));if(!function x0(e){return!!e.deps}(e))return Wn(o)||ul(o);r=()=>new o(...rc(e.deps))}return r}function Hr(e,t,n=!1){return{factory:e,value:t,multi:n?[]:void 0}}function cl(e,t){for(const n of e)Array.isArray(n)?cl(n,t):n&&Zu(n)?cl(n.\u0275providers,t):t(n)}const ea=new M("AppId",{providedIn:"root",factory:()=>F0}),F0="ng",fm=new M("Platform Initializer"),Jn=new M("Platform ID",{providedIn:"platform",factory:()=>"unknown"}),hm=new M("CSP nonce",{providedIn:"root",factory:()=>function Vr(){if(void 0!==Qc)return Qc;if(typeof document<"u")return document;throw new C(210,!1)}().body?.querySelector("[ngCspNonce]")?.getAttribute("ngCspNonce")||null});let pm=(e,t,n)=>null;function yl(e,t,n=!1){return pm(e,t,n)}class G0{}class ym{}class W0{resolveComponentFactory(t){throw function q0(e){const t=Error(`No component factory found for ${be(e)}.`);return t.ngComponent=e,t}(t)}}let sa=(()=>{class e{static{this.NULL=new W0}}return e})();function Z0(){return zr(xe(),v())}function zr(e,t){return new lt(Je(e,t))}let lt=(()=>{class e{constructor(n){this.nativeElement=n}static{this.__NG_ELEMENT_ID__=Z0}}return e})();class Dm{}let ln=(()=>{class e{constructor(){this.destroyNode=null}static{this.__NG_ELEMENT_ID__=()=>function Q0(){const e=v(),n=at(xe().index,e);return(Xe(n)?n:e)[F]}()}}return e})(),X0=(()=>{class e{static{this.\u0275prov=S({token:e,providedIn:"root",factory:()=>null})}}return e})();class si{constructor(t){this.full=t,this.major=t.split(".")[0],this.minor=t.split(".")[1],this.patch=t.split(".").slice(2).join(".")}}const J0=new si("16.2.12"),Cl={};function Em(e,t=null,n=null,r){const o=bm(e,t,n,r);return o.resolveInjectorInitializers(),o}function bm(e,t=null,n=null,r,o=new Set){const i=[n||W,I0(e)];return r=r||("object"==typeof e?void 0:be(e)),new Br(i,t||Ks(),r||null,o)}let dt=(()=>{class e{static{this.THROW_IF_NOT_FOUND=No}static{this.NULL=new nl}static create(n,r){if(Array.isArray(n))return Em({name:""},r,n,"");{const o=n.name??"";return Em({name:o},n.parent,n.providers,o)}}static{this.\u0275prov=S({token:e,providedIn:"any",factory:()=>I(im)})}static{this.__NG_ELEMENT_ID__=-1}}return e})();function _l(e){return e.ngOriginalError}class dn{constructor(){this._console=console}handleError(t){const n=this._findOriginalError(t);this._console.error("ERROR",t),n&&this._console.error("ORIGINAL ERROR",n)}_findOriginalError(t){let n=t&&_l(t);for(;n&&_l(n);)n=_l(n);return n||null}}function El(e){return t=>{setTimeout(e,void 0,t)}}const ge=class sA extends At{constructor(t=!1){super(),this.__isAsync=t}emit(t){super.next(t)}subscribe(t,n,r){let o=t,i=n||(()=>null),s=r;if(t&&"object"==typeof t){const u=t;o=u.next?.bind(u),i=u.error?.bind(u),s=u.complete?.bind(u)}this.__isAsync&&(i=El(i),o&&(o=El(o)),s&&(s=El(s)));const a=super.subscribe({next:o,error:i,complete:s});return t instanceof Ze&&t.add(a),a}};function Im(...e){}class se{constructor({enableLongStackTrace:t=!1,shouldCoalesceEventChangeDetection:n=!1,shouldCoalesceRunChangeDetection:r=!1}){if(this.hasPendingMacrotasks=!1,this.hasPendingMicrotasks=!1,this.isStable=!0,this.onUnstable=new ge(!1),this.onMicrotaskEmpty=new ge(!1),this.onStable=new ge(!1),this.onError=new ge(!1),typeof Zone>"u")throw new C(908,!1);Zone.assertZonePatched();const o=this;o._nesting=0,o._outer=o._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(o._inner=o._inner.fork(new Zone.TaskTrackingZoneSpec)),t&&Zone.longStackTraceZoneSpec&&(o._inner=o._inner.fork(Zone.longStackTraceZoneSpec)),o.shouldCoalesceEventChangeDetection=!r&&n,o.shouldCoalesceRunChangeDetection=r,o.lastRequestAnimationFrameId=-1,o.nativeRequestAnimationFrame=function aA(){const e="function"==typeof oe.requestAnimationFrame;let t=oe[e?"requestAnimationFrame":"setTimeout"],n=oe[e?"cancelAnimationFrame":"clearTimeout"];if(typeof Zone<"u"&&t&&n){const r=t[Zone.__symbol__("OriginalDelegate")];r&&(t=r);const o=n[Zone.__symbol__("OriginalDelegate")];o&&(n=o)}return{nativeRequestAnimationFrame:t,nativeCancelAnimationFrame:n}}().nativeRequestAnimationFrame,function lA(e){const t=()=>{!function cA(e){e.isCheckStableRunning||-1!==e.lastRequestAnimationFrameId||(e.lastRequestAnimationFrameId=e.nativeRequestAnimationFrame.call(oe,()=>{e.fakeTopEventTask||(e.fakeTopEventTask=Zone.root.scheduleEventTask("fakeTopEventTask",()=>{e.lastRequestAnimationFrameId=-1,Ml(e),e.isCheckStableRunning=!0,bl(e),e.isCheckStableRunning=!1},void 0,()=>{},()=>{})),e.fakeTopEventTask.invoke()}),Ml(e))}(e)};e._inner=e._inner.fork({name:"angular",properties:{isAngularZone:!0},onInvokeTask:(n,r,o,i,s,a)=>{if(function fA(e){return!(!Array.isArray(e)||1!==e.length)&&!0===e[0].data?.__ignore_ng_zone__}(a))return n.invokeTask(o,i,s,a);try{return Sm(e),n.invokeTask(o,i,s,a)}finally{(e.shouldCoalesceEventChangeDetection&&"eventTask"===i.type||e.shouldCoalesceRunChangeDetection)&&t(),Am(e)}},onInvoke:(n,r,o,i,s,a,u)=>{try{return Sm(e),n.invoke(o,i,s,a,u)}finally{e.shouldCoalesceRunChangeDetection&&t(),Am(e)}},onHasTask:(n,r,o,i)=>{n.hasTask(o,i),r===o&&("microTask"==i.change?(e._hasPendingMicrotasks=i.microTask,Ml(e),bl(e)):"macroTask"==i.change&&(e.hasPendingMacrotasks=i.macroTask))},onHandleError:(n,r,o,i)=>(n.handleError(o,i),e.runOutsideAngular(()=>e.onError.emit(i)),!1)})}(o)}static isInAngularZone(){return typeof Zone<"u"&&!0===Zone.current.get("isAngularZone")}static assertInAngularZone(){if(!se.isInAngularZone())throw new C(909,!1)}static assertNotInAngularZone(){if(se.isInAngularZone())throw new C(909,!1)}run(t,n,r){return this._inner.run(t,n,r)}runTask(t,n,r,o){const i=this._inner,s=i.scheduleEventTask("NgZoneEvent: "+o,t,uA,Im,Im);try{return i.runTask(s,n,r)}finally{i.cancelTask(s)}}runGuarded(t,n,r){return this._inner.runGuarded(t,n,r)}runOutsideAngular(t){return this._outer.run(t)}}const uA={};function bl(e){if(0==e._nesting&&!e.hasPendingMicrotasks&&!e.isStable)try{e._nesting++,e.onMicrotaskEmpty.emit(null)}finally{if(e._nesting--,!e.hasPendingMicrotasks)try{e.runOutsideAngular(()=>e.onStable.emit(null))}finally{e.isStable=!0}}}function Ml(e){e.hasPendingMicrotasks=!!(e._hasPendingMicrotasks||(e.shouldCoalesceEventChangeDetection||e.shouldCoalesceRunChangeDetection)&&-1!==e.lastRequestAnimationFrameId)}function Sm(e){e._nesting++,e.isStable&&(e.isStable=!1,e.onUnstable.emit(null))}function Am(e){e._nesting--,bl(e)}class dA{constructor(){this.hasPendingMicrotasks=!1,this.hasPendingMacrotasks=!1,this.isStable=!0,this.onUnstable=new ge,this.onMicrotaskEmpty=new ge,this.onStable=new ge,this.onError=new ge}run(t,n,r){return t.apply(n,r)}runGuarded(t,n,r){return t.apply(n,r)}runOutsideAngular(t){return t()}runTask(t,n,r,o){return t.apply(n,r)}}const Tm=new M("",{providedIn:"root",factory:Nm});function Nm(){const e=E(se);let t=!0;return function aM(...e){const t=To(e),n=function eM(e,t){return"number"==typeof zu(e)?e.pop():t}(e,1/0),r=e;return r.length?1===r.length?rt(r[0]):pr(n)(Ee(r,t)):jt}(new he(o=>{t=e.isStable&&!e.hasPendingMacrotasks&&!e.hasPendingMicrotasks,e.runOutsideAngular(()=>{o.next(t),o.complete()})}),new he(o=>{let i;e.runOutsideAngular(()=>{i=e.onStable.subscribe(()=>{se.assertNotInAngularZone(),queueMicrotask(()=>{!t&&!e.hasPendingMacrotasks&&!e.hasPendingMicrotasks&&(t=!0,o.next(!0))})})});const s=e.onUnstable.subscribe(()=>{se.assertInAngularZone(),t&&(t=!1,e.runOutsideAngular(()=>{o.next(!1)}))});return()=>{i.unsubscribe(),s.unsubscribe()}}).pipe(Vh()))}function fn(e){return e instanceof Function?e():e}let Il=(()=>{class e{constructor(){this.renderDepth=0,this.handler=null}begin(){this.handler?.validateBegin(),this.renderDepth++}end(){this.renderDepth--,0===this.renderDepth&&this.handler?.execute()}ngOnDestroy(){this.handler?.destroy(),this.handler=null}static{this.\u0275prov=S({token:e,providedIn:"root",factory:()=>new e})}}return e})();function ai(e){for(;e;){e[L]|=64;const t=Xo(e);if(cc(e)&&!t)return e;e=t}return null}const Fm=new M("",{providedIn:"root",factory:()=>!1});let ca=null;function jm(e,t){return e[t]??$m()}function Bm(e,t){const n=$m();n.producerNode?.length&&(e[t]=ca,n.lView=e,ca=Hm())}const _A={...dp,consumerIsAlwaysLive:!0,consumerMarkedDirty:e=>{ai(e.lView)},lView:null};function Hm(){return Object.create(_A)}function $m(){return ca??=Hm(),ca}const k={};function le(e){Um(q(),v(),$e()+e,!1)}function Um(e,t,n,r){if(!r)if(3==(3&t[L])){const i=e.preOrderCheckHooks;null!==i&&Ms(t,i,n)}else{const i=e.preOrderHooks;null!==i&&Is(t,i,0,n)}Zn(n)}function w(e,t=$.Default){const n=v();return null===n?I(e,t):ng(xe(),n,x(e),t)}function la(e,t,n,r,o,i,s,a,u,c,l){const d=t.blueprint.slice();return d[pe]=o,d[L]=140|r,(null!==c||e&&2048&e[L])&&(d[L]|=2048),Rp(d),d[ce]=d[Dr]=e,d[ye]=n,d[vr]=s||e&&e[vr],d[F]=a||e&&e[F],d[Mn]=u||e&&e[Mn]||null,d[Pe]=i,d[Lo]=function SS(){return IS++}(),d[sn]=l,d[ap]=c,d[ve]=2==t.type?e[ve]:d,d}function Wr(e,t,n,r,o){let i=e.data[t];if(null===i)i=function Sl(e,t,n,r,o){const i=Lp(),s=yc(),u=e.data[t]=function NA(e,t,n,r,o,i){let s=t?t.injectorIndex:-1,a=0;return function br(){return null!==R.skipHydrationRootTNode}()&&(a|=128),{type:n,index:r,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:a,providerIndexes:0,value:o,attrs:i,mergedAttrs:null,localNames:null,initialInputs:void 0,inputs:null,outputs:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:t,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}(0,s?i:i&&i.parent,n,t,r,o);return null===e.firstChild&&(e.firstChild=u),null!==i&&(s?null==i.child&&null!==u.parent&&(i.child=u):null===i.next&&(i.next=u,u.prev=i)),u}(e,t,n,r,o),function II(){return R.lFrame.inI18n}()&&(i.flags|=32);else if(64&i.type){i.type=n,i.value=r,i.attrs=o;const s=function $o(){const e=R.lFrame,t=e.currentTNode;return e.isParent?t:t.parent}();i.injectorIndex=null===s?-1:s.injectorIndex}return zt(i,!0),i}function ui(e,t,n,r){if(0===n)return-1;const o=t.length;for(let i=0;i<n;i++)t.push(r),e.blueprint.push(r),e.data.push(null);return o}function Gm(e,t,n,r,o){const i=jm(t,Vo),s=$e(),a=2&r;try{Zn(-1),a&&t.length>U&&Um(e,t,U,!1),Ut(a?2:0,o);const c=a?i:null,l=dc(c);try{null!==c&&(c.dirty=!1),n(r,o)}finally{fc(c,l)}}finally{a&&null===t[Vo]&&Bm(t,Vo),Zn(s),Ut(a?3:1,o)}}function Al(e,t,n){if(uc(t)){const r=Ct(null);try{const i=t.directiveEnd;for(let s=t.directiveStart;s<i;s++){const a=e.data[s];a.contentQueries&&a.contentQueries(1,n[s],s)}}finally{Ct(r)}}}function Tl(e,t,n){kp()&&(function LA(e,t,n,r){const o=n.directiveStart,i=n.directiveEnd;qn(n)&&function zA(e,t,n){const r=Je(t,e),o=qm(n);let s=16;n.signals?s=4096:n.onPush&&(s=64);const a=da(e,la(e,o,null,s,r,t,null,e[vr].rendererFactory.createRenderer(r,n),null,null,null));e[t.index]=a}(t,n,e.data[o+n.componentOffset]),e.firstCreatePass||As(n,t),Fe(r,t);const s=n.initialInputs;for(let a=o;a<i;a++){const u=e.data[a],c=Yn(t,e,a,n);Fe(c,t),null!==s&&GA(0,a-o,c,u,0,s),xt(u)&&(at(n.index,t)[ye]=Yn(t,e,a,n))}}(e,t,n,Je(n,t)),64==(64&n.flags)&&Xm(e,t,n))}function Nl(e,t,n=Je){const r=t.localNames;if(null!==r){let o=t.index+1;for(let i=0;i<r.length;i+=2){const s=r[i+1],a=-1===s?n(t,e):e[s];e[o++]=a}}}function qm(e){const t=e.tView;return null===t||t.incompleteFirstPass?e.tView=Rl(1,null,e.template,e.decls,e.vars,e.directiveDefs,e.pipeDefs,e.viewQuery,e.schemas,e.consts,e.id):t}function Rl(e,t,n,r,o,i,s,a,u,c,l){const d=U+r,f=d+o,h=function bA(e,t){const n=[];for(let r=0;r<t;r++)n.push(r<e?null:k);return n}(d,f),p="function"==typeof c?c():c;return h[_]={type:e,blueprint:h,template:n,queries:null,viewQuery:a,declTNode:t,data:h.slice().fill(null,d),bindingStartIndex:d,expandoStartIndex:f,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:"function"==typeof i?i():i,pipeRegistry:"function"==typeof s?s():s,firstChild:null,schemas:u,consts:p,incompleteFirstPass:!1,ssrId:l}}let Wm=e=>null;function Zm(e,t,n,r){for(let o in e)if(e.hasOwnProperty(o)){n=null===n?{}:n;const i=e[o];null===r?Ym(n,t,o,i):r.hasOwnProperty(o)&&Ym(n,t,r[o],i)}return n}function Ym(e,t,n,r){e.hasOwnProperty(n)?e[n].push(t,r):e[n]=[t,r]}function xl(e,t,n,r){if(kp()){const o=null===r?null:{"":-1},i=function jA(e,t){const n=e.directiveRegistry;let r=null,o=null;if(n)for(let i=0;i<n.length;i++){const s=n[i];if(tp(t,s.selectors,!1))if(r||(r=[]),xt(s))if(null!==s.findHostDirectiveDefs){const a=[];o=o||new Map,s.findHostDirectiveDefs(s,a,o),r.unshift(...a,s),Ol(e,t,a.length)}else r.unshift(s),Ol(e,t,0);else o=o||new Map,s.findHostDirectiveDefs?.(s,r,o),r.push(s)}return null===r?null:[r,o]}(e,n);let s,a;null===i?s=a=null:[s,a]=i,null!==s&&Qm(e,t,n,s,o,a),o&&function BA(e,t,n){if(t){const r=e.localNames=[];for(let o=0;o<t.length;o+=2){const i=n[t[o+1]];if(null==i)throw new C(-301,!1);r.push(t[o],i)}}}(n,r,o)}n.mergedAttrs=Oo(n.mergedAttrs,n.attrs)}function Qm(e,t,n,r,o,i){for(let c=0;c<r.length;c++)Tc(As(n,t),e,r[c].type);!function $A(e,t,n){e.flags|=1,e.directiveStart=t,e.directiveEnd=t+n,e.providerIndexes=t}(n,e.data.length,r.length);for(let c=0;c<r.length;c++){const l=r[c];l.providersResolver&&l.providersResolver(l)}let s=!1,a=!1,u=ui(e,t,r.length,null);for(let c=0;c<r.length;c++){const l=r[c];n.mergedAttrs=Oo(n.mergedAttrs,l.hostAttrs),UA(e,n,t,u,l),HA(u,l,o),null!==l.contentQueries&&(n.flags|=4),(null!==l.hostBindings||null!==l.hostAttrs||0!==l.hostVars)&&(n.flags|=64);const d=l.type.prototype;!s&&(d.ngOnChanges||d.ngOnInit||d.ngDoCheck)&&((e.preOrderHooks??=[]).push(n.index),s=!0),!a&&(d.ngOnChanges||d.ngDoCheck)&&((e.preOrderCheckHooks??=[]).push(n.index),a=!0),u++}!function RA(e,t,n){const o=t.directiveEnd,i=e.data,s=t.attrs,a=[];let u=null,c=null;for(let l=t.directiveStart;l<o;l++){const d=i[l],f=n?n.get(d):null,p=f?f.outputs:null;u=Zm(d.inputs,l,u,f?f.inputs:null),c=Zm(d.outputs,l,c,p);const g=null===u||null===s||ep(t)?null:qA(u,l,s);a.push(g)}null!==u&&(u.hasOwnProperty("class")&&(t.flags|=8),u.hasOwnProperty("style")&&(t.flags|=16)),t.initialInputs=a,t.inputs=u,t.outputs=c}(e,n,i)}function Xm(e,t,n){const r=n.directiveStart,o=n.directiveEnd,i=n.index,s=function AI(){return R.lFrame.currentDirectiveIndex}();try{Zn(i);for(let a=r;a<o;a++){const u=e.data[a],c=t[a];Dc(a),(null!==u.hostBindings||0!==u.hostVars||null!==u.hostAttrs)&&VA(u,c)}}finally{Zn(-1),Dc(s)}}function VA(e,t){null!==e.hostBindings&&e.hostBindings(1,t)}function Ol(e,t,n){t.componentOffset=n,(e.components??=[]).push(t.index)}function HA(e,t,n){if(n){if(t.exportAs)for(let r=0;r<t.exportAs.length;r++)n[t.exportAs[r]]=e;xt(t)&&(n[""]=e)}}function UA(e,t,n,r,o){e.data[r]=o;const i=o.factory||(o.factory=Wn(o.type)),s=new Uo(i,xt(o),w);e.blueprint[r]=s,n[r]=s,function FA(e,t,n,r,o){const i=o.hostBindings;if(i){let s=e.hostBindingOpCodes;null===s&&(s=e.hostBindingOpCodes=[]);const a=~t.index;(function kA(e){let t=e.length;for(;t>0;){const n=e[--t];if("number"==typeof n&&n<0)return n}return 0})(s)!=a&&s.push(a),s.push(n,r,i)}}(e,t,r,ui(e,n,o.hostVars,k),o)}function GA(e,t,n,r,o,i){const s=i[t];if(null!==s)for(let a=0;a<s.length;)Jm(r,n,s[a++],s[a++],s[a++])}function Jm(e,t,n,r,o){const i=Ct(null);try{const s=e.inputTransforms;null!==s&&s.hasOwnProperty(r)&&(o=s[r].call(t,o)),null!==e.setInput?e.setInput(t,o,n,r):t[r]=o}finally{Ct(i)}}function qA(e,t,n){let r=null,o=0;for(;o<n.length;){const i=n[o];if(0!==i)if(5!==i){if("number"==typeof i)break;if(e.hasOwnProperty(i)){null===r&&(r=[]);const s=e[i];for(let a=0;a<s.length;a+=2)if(s[a]===t){r.push(i,s[a+1],n[o+1]);break}}o+=2}else o+=2;else o+=4}return r}function Km(e,t,n,r){return[e,!0,!1,t,null,0,r,n,null,null,null]}function ey(e,t){const n=e.contentQueries;if(null!==n)for(let r=0;r<n.length;r+=2){const i=n[r+1];if(-1!==i){const s=e.data[i];wc(n[r]),s.contentQueries(2,t[i],i)}}}function da(e,t){return e[Fo]?e[sp][Rt]=t:e[Fo]=t,e[sp]=t,t}function Fl(e,t,n){wc(0);const r=Ct(null);try{t(e,n)}finally{Ct(r)}}function oy(e,t){const n=e[Mn],r=n?n.get(dn,null):null;r&&r.handleError(t)}function kl(e,t,n,r,o){for(let i=0;i<n.length;){const s=n[i++],a=n[i++];Jm(e.data[s],t[s],r,a,o)}}function WA(e,t){const n=at(t,e),r=n[_];!function ZA(e,t){for(let n=t.length;n<e.blueprint.length;n++)t.push(e.blueprint[n])}(r,n);const o=n[pe];null!==o&&null===n[sn]&&(n[sn]=yl(o,n[Mn])),Ll(r,n,n[ye])}function Ll(e,t,n){_c(t);try{const r=e.viewQuery;null!==r&&Fl(1,r,n);const o=e.template;null!==o&&Gm(e,t,o,1,n),e.firstCreatePass&&(e.firstCreatePass=!1),e.staticContentQueries&&ey(e,t),e.staticViewQueries&&Fl(2,e.viewQuery,n);const i=e.components;null!==i&&function YA(e,t){for(let n=0;n<t.length;n++)WA(e,t[n])}(t,i)}catch(r){throw e.firstCreatePass&&(e.incompleteFirstPass=!0,e.firstCreatePass=!1),r}finally{t[L]&=-5,Ec()}}let iy=(()=>{class e{constructor(){this.all=new Set,this.queue=new Map}create(n,r,o){const i=typeof Zone>"u"?null:Zone.current,s=function nI(e,t,n){const r=Object.create(rI);n&&(r.consumerAllowSignalWrites=!0),r.fn=e,r.schedule=t;const o=s=>{r.cleanupFn=s};return r.ref={notify:()=>gp(r),run:()=>{if(r.dirty=!1,r.hasRun&&!mp(r))return;r.hasRun=!0;const s=dc(r);try{r.cleanupFn(),r.cleanupFn=bp,r.fn(o)}finally{fc(r,s)}},cleanup:()=>r.cleanupFn()},r.ref}(n,c=>{this.all.has(c)&&this.queue.set(c,i)},o);let a;this.all.add(s),s.notify();const u=()=>{s.cleanup(),a?.(),this.all.delete(s),this.queue.delete(s)};return a=r?.onDestroy(u),{destroy:u}}flush(){if(0!==this.queue.size)for(const[n,r]of this.queue)this.queue.delete(n),r?r.run(()=>n.run()):n.run()}get isQueueEmpty(){return 0===this.queue.size}static{this.\u0275prov=S({token:e,providedIn:"root",factory:()=>new e})}}return e})();function fa(e,t,n){let r=n?e.styles:null,o=n?e.classes:null,i=0;if(null!==t)for(let s=0;s<t.length;s++){const a=t[s];"number"==typeof a?i=a:1==i?o=qu(o,a):2==i&&(r=qu(r,a+": "+t[++s]+";"))}n?e.styles=r:e.stylesWithoutHost=r,n?e.classes=o:e.classesWithoutHost=o}function ci(e,t,n,r,o=!1){for(;null!==n;){const i=t[n.index];null!==i&&r.push(ie(i)),Be(i)&&sy(i,r);const s=n.type;if(8&s)ci(e,t,n.child,r);else if(32&s){const a=Hc(n,t);let u;for(;u=a();)r.push(u)}else if(16&s){const a=Hg(t,n);if(Array.isArray(a))r.push(...a);else{const u=Xo(t[ve]);ci(u[_],u,a,r,!0)}}n=o?n.projectionNext:n.next}return r}function sy(e,t){for(let n=Ne;n<e.length;n++){const r=e[n],o=r[_].firstChild;null!==o&&ci(r[_],r,o,t)}e[$t]!==e[pe]&&t.push(e[$t])}function ha(e,t,n,r=!0){const o=t[vr],i=o.rendererFactory,s=o.afterRenderEventManager;i.begin?.(),s?.begin();try{ay(e,t,e.template,n)}catch(u){throw r&&oy(t,u),u}finally{i.end?.(),o.effectManager?.flush(),s?.end()}}function ay(e,t,n,r){const o=t[L];if(256!=(256&o)){t[vr].effectManager?.flush(),_c(t);try{Rp(t),function jp(e){return R.lFrame.bindingIndex=e}(e.bindingStartIndex),null!==n&&Gm(e,t,n,2,r);const s=3==(3&o);if(s){const c=e.preOrderCheckHooks;null!==c&&Ms(t,c,null)}else{const c=e.preOrderHooks;null!==c&&Is(t,c,0,null),bc(t,0)}if(function JA(e){for(let t=Tg(e);null!==t;t=Ng(t)){if(!t[up])continue;const n=t[wr];for(let r=0;r<n.length;r++){hI(n[r])}}}(t),uy(t,2),null!==e.contentQueries&&ey(e,t),s){const c=e.contentCheckHooks;null!==c&&Ms(t,c)}else{const c=e.contentHooks;null!==c&&Is(t,c,1),bc(t,1)}!function EA(e,t){const n=e.hostBindingOpCodes;if(null===n)return;const r=jm(t,jo);try{for(let o=0;o<n.length;o++){const i=n[o];if(i<0)Zn(~i);else{const s=i,a=n[++o],u=n[++o];SI(a,s),r.dirty=!1;const c=dc(r);try{u(2,t[s])}finally{fc(r,c)}}}}finally{null===t[jo]&&Bm(t,jo),Zn(-1)}}(e,t);const a=e.components;null!==a&&ly(t,a,0);const u=e.viewQuery;if(null!==u&&Fl(2,u,r),s){const c=e.viewCheckHooks;null!==c&&Ms(t,c)}else{const c=e.viewHooks;null!==c&&Is(t,c,2),bc(t,2)}!0===e.firstUpdatePass&&(e.firstUpdatePass=!1),t[L]&=-73,xp(t)}finally{Ec()}}}function uy(e,t){for(let n=Tg(e);null!==n;n=Ng(n))for(let r=Ne;r<n.length;r++)cy(n[r],t)}function KA(e,t,n){cy(at(t,e),n)}function cy(e,t){if(!function dI(e){return 128==(128&e[L])}(e))return;const n=e[_],r=e[L];if(80&r&&0===t||1024&r||2===t)ay(n,e,n.template,e[ye]);else if(e[Po]>0){uy(e,1);const o=n.components;null!==o&&ly(e,o,1)}}function ly(e,t,n){for(let r=0;r<t.length;r++)KA(e,t[r],n)}class li{get rootNodes(){const t=this._lView,n=t[_];return ci(n,t,n.firstChild,[])}constructor(t,n){this._lView=t,this._cdRefInjectingView=n,this._appRef=null,this._attachedToViewContainer=!1}get context(){return this._lView[ye]}set context(t){this._lView[ye]=t}get destroyed(){return 256==(256&this._lView[L])}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){const t=this._lView[ce];if(Be(t)){const n=t[8],r=n?n.indexOf(this):-1;r>-1&&(zs(t,r),xs(n,r))}this._attachedToViewContainer=!1}Uc(this._lView[_],this._lView)}onDestroy(t){!function Pp(e,t){if(256==(256&e[L]))throw new C(911,!1);null===e[In]&&(e[In]=[]),e[In].push(t)}(this._lView,t)}markForCheck(){ai(this._cdRefInjectingView||this._lView)}detach(){this._lView[L]&=-129}reattach(){this._lView[L]|=128}detectChanges(){ha(this._lView[_],this._lView,this.context)}checkNoChanges(){}attachToViewContainerRef(){if(this._appRef)throw new C(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null,function HS(e,t){Ko(e,t,t[F],2,null,null)}(this._lView[_],this._lView)}attachToAppRef(t){if(this._attachedToViewContainer)throw new C(902,!1);this._appRef=t}}class eT extends li{constructor(t){super(t),this._view=t}detectChanges(){const t=this._view;ha(t[_],t,t[ye],!1)}checkNoChanges(){}get context(){return null}}class dy extends sa{constructor(t){super(),this.ngModule=t}resolveComponentFactory(t){const n=G(t);return new di(n,this.ngModule)}}function fy(e){const t=[];for(let n in e)e.hasOwnProperty(n)&&t.push({propName:e[n],templateName:n});return t}class nT{constructor(t,n){this.injector=t,this.parentInjector=n}get(t,n,r){r=hs(r);const o=this.injector.get(t,Cl,r);return o!==Cl||n===Cl?o:this.parentInjector.get(t,n,r)}}class di extends ym{get inputs(){const t=this.componentDef,n=t.inputTransforms,r=fy(t.inputs);if(null!==n)for(const o of r)n.hasOwnProperty(o.propName)&&(o.transform=n[o.propName]);return r}get outputs(){return fy(this.componentDef.outputs)}constructor(t,n){super(),this.componentDef=t,this.ngModule=n,this.componentType=t.type,this.selector=function LM(e){return e.map(kM).join(",")}(t.selectors),this.ngContentSelectors=t.ngContentSelectors?t.ngContentSelectors:[],this.isBoundToModule=!!n}create(t,n,r,o){let i=(o=o||this.ngModule)instanceof ct?o:o?.injector;i&&null!==this.componentDef.getStandaloneInjector&&(i=this.componentDef.getStandaloneInjector(i)||i);const s=i?new nT(t,i):t,a=s.get(Dm,null);if(null===a)throw new C(407,!1);const d={rendererFactory:a,sanitizer:s.get(X0,null),effectManager:s.get(iy,null),afterRenderEventManager:s.get(Il,null)},f=a.createRenderer(null,this.componentDef),h=this.componentDef.selectors[0][0]||"div",p=r?function MA(e,t,n,r){const i=r.get(Fm,!1)||n===Tt.ShadowDom,s=e.selectRootElement(t,i);return function IA(e){Wm(e)}(s),s}(f,r,this.componentDef.encapsulation,s):Us(f,h,function tT(e){const t=e.toLowerCase();return"svg"===t?"svg":"math"===t?"math":null}(h)),D=this.componentDef.signals?4608:this.componentDef.onPush?576:528;let m=null;null!==p&&(m=yl(p,s,!0));const b=Rl(0,null,null,1,0,null,null,null,null,null,null),A=la(null,b,null,D,null,null,d,f,s,null,m);let H,Se;_c(A);try{const tn=this.componentDef;let fr,lh=null;tn.findHostDirectiveDefs?(fr=[],lh=new Map,tn.findHostDirectiveDefs(tn,fr,lh),fr.push(tn)):fr=[tn];const rB=function oT(e,t){const n=e[_],r=U;return e[r]=t,Wr(n,r,2,"#host",null)}(A,p),oB=function iT(e,t,n,r,o,i,s){const a=o[_];!function sT(e,t,n,r){for(const o of e)t.mergedAttrs=Oo(t.mergedAttrs,o.hostAttrs);null!==t.mergedAttrs&&(fa(t,t.mergedAttrs,!0),null!==n&&Gg(r,n,t))}(r,e,t,s);let u=null;null!==t&&(u=yl(t,o[Mn]));const c=i.rendererFactory.createRenderer(t,n);let l=16;n.signals?l=4096:n.onPush&&(l=64);const d=la(o,qm(n),null,l,o[e.index],e,i,c,null,null,u);return a.firstCreatePass&&Ol(a,e,r.length-1),da(o,d),o[e.index]=d}(rB,p,tn,fr,A,d,f);Se=Np(b,U),p&&function uT(e,t,n,r){if(r)sc(e,n,["ng-version",J0.full]);else{const{attrs:o,classes:i}=function VM(e){const t=[],n=[];let r=1,o=2;for(;r<e.length;){let i=e[r];if("string"==typeof i)2===o?""!==i&&t.push(i,e[++r]):8===o&&n.push(i);else{if(!Nt(o))break;o=i}r++}return{attrs:t,classes:n}}(t.selectors[0]);o&&sc(e,n,o),i&&i.length>0&&zg(e,n,i.join(" "))}}(f,tn,p,r),void 0!==n&&function cT(e,t,n){const r=e.projection=[];for(let o=0;o<t.length;o++){const i=n[o];r.push(null!=i?Array.from(i):null)}}(Se,this.ngContentSelectors,n),H=function aT(e,t,n,r,o,i){const s=xe(),a=o[_],u=Je(s,o);Qm(a,o,s,n,null,r);for(let l=0;l<n.length;l++)Fe(Yn(o,a,s.directiveStart+l,s),o);Xm(a,o,s),u&&Fe(u,o);const c=Yn(o,a,s.directiveStart+s.componentOffset,s);if(e[ye]=o[ye]=c,null!==i)for(const l of i)l(c,t);return Al(a,s,e),c}(oB,tn,fr,lh,A,[lT]),Ll(b,A,null)}finally{Ec()}return new rT(this.componentType,H,zr(Se,A),A,Se)}}class rT extends G0{constructor(t,n,r,o,i){super(),this.location=r,this._rootLView=o,this._tNode=i,this.previousInputValues=null,this.instance=n,this.hostView=this.changeDetectorRef=new eT(o),this.componentType=t}setInput(t,n){const r=this._tNode.inputs;let o;if(null!==r&&(o=r[t])){if(this.previousInputValues??=new Map,this.previousInputValues.has(t)&&Object.is(this.previousInputValues.get(t),n))return;const i=this._rootLView;kl(i[_],i,o,t,n),this.previousInputValues.set(t,n),ai(at(this._tNode.index,i))}}get injector(){return new Ue(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(t){this.hostView.onDestroy(t)}}function lT(){const e=xe();bs(v()[_],e)}function K(e){let t=function hy(e){return Object.getPrototypeOf(e.prototype).constructor}(e.type),n=!0;const r=[e];for(;t;){let o;if(xt(e))o=t.\u0275cmp||t.\u0275dir;else{if(t.\u0275cmp)throw new C(903,!1);o=t.\u0275dir}if(o){if(n){r.push(o);const s=e;s.inputs=pa(e.inputs),s.inputTransforms=pa(e.inputTransforms),s.declaredInputs=pa(e.declaredInputs),s.outputs=pa(e.outputs);const a=o.hostBindings;a&&pT(e,a);const u=o.viewQuery,c=o.contentQueries;if(u&&fT(e,u),c&&hT(e,c),as(e.inputs,o.inputs),as(e.declaredInputs,o.declaredInputs),as(e.outputs,o.outputs),null!==o.inputTransforms&&(null===s.inputTransforms&&(s.inputTransforms={}),as(s.inputTransforms,o.inputTransforms)),xt(o)&&o.data.animation){const l=e.data;l.animation=(l.animation||[]).concat(o.data.animation)}}const i=o.features;if(i)for(let s=0;s<i.length;s++){const a=i[s];a&&a.ngInherit&&a(e),a===K&&(n=!1)}}t=Object.getPrototypeOf(t)}!function dT(e){let t=0,n=null;for(let r=e.length-1;r>=0;r--){const o=e[r];o.hostVars=t+=o.hostVars,o.hostAttrs=Oo(o.hostAttrs,n=Oo(n,o.hostAttrs))}}(r)}function pa(e){return e===Bt?{}:e===W?[]:e}function fT(e,t){const n=e.viewQuery;e.viewQuery=n?(r,o)=>{t(r,o),n(r,o)}:t}function hT(e,t){const n=e.contentQueries;e.contentQueries=n?(r,o,i)=>{t(r,o,i),n(r,o,i)}:t}function pT(e,t){const n=e.hostBindings;e.hostBindings=n?(r,o)=>{t(r,o),n(r,o)}:t}function ga(e){return!!function Vl(e){return null!==e&&("function"==typeof e||"object"==typeof e)}(e)&&(Array.isArray(e)||!(e instanceof Map)&&Symbol.iterator in e)}function ke(e,t,n){return!Object.is(e[t],n)&&(e[t]=n,!0)}function zl(e,t,n,r,o,i,s,a){const u=v(),c=q(),l=e+U,d=c.firstCreatePass?function jT(e,t,n,r,o,i,s,a,u){const c=t.consts,l=Wr(t,e,4,s||null,An(c,a));xl(t,n,l,An(c,u)),bs(t,l);const d=l.tView=Rl(2,l,r,o,i,t.directiveRegistry,t.pipeRegistry,null,t.schemas,c,null);return null!==t.queries&&(t.queries.template(t,l),d.queries=t.queries.embeddedTView(l)),l}(l,c,u,t,n,r,o,i,s):c.data[l];zt(d,!1);const f=Ny(c,u,d,e);Es()&&qs(c,u,f,d),Fe(f,u),da(u,u[l]=Km(f,u,f,d)),Ds(d)&&Tl(c,u,d),null!=s&&Nl(u,d,a)}let Ny=function Ry(e,t,n,r){return Tn(!0),t[F].createComment("")};function mi(e,t,n){const r=v();return ke(r,Mr(),t)&&function ft(e,t,n,r,o,i,s,a){const u=Je(t,n);let l,c=t.inputs;!a&&null!=c&&(l=c[r])?(kl(e,n,l,r,o),qn(t)&&function OA(e,t){const n=at(t,e);16&n[L]||(n[L]|=64)}(n,t.index)):3&t.type&&(r=function xA(e){return"class"===e?"className":"for"===e?"htmlFor":"formaction"===e?"formAction":"innerHtml"===e?"innerHTML":"readonly"===e?"readOnly":"tabindex"===e?"tabIndex":e}(r),o=null!=s?s(o,t.value||"",r):o,i.setProperty(u,r,o))}(q(),function fe(){const e=R.lFrame;return Np(e.tView,e.selectedIndex)}(),r,e,t,r[F],n,!1),mi}function Gl(e,t,n,r,o){const s=o?"class":"style";kl(e,n,t.inputs[s],s,r)}function V(e,t,n,r){const o=v(),i=q(),s=U+e,a=o[F],u=i.firstCreatePass?function zT(e,t,n,r,o,i){const s=t.consts,u=Wr(t,e,2,r,An(s,o));return xl(t,n,u,An(s,i)),null!==u.attrs&&fa(u,u.attrs,!1),null!==u.mergedAttrs&&fa(u,u.mergedAttrs,!0),null!==t.queries&&t.queries.elementStart(t,u),u}(s,i,o,t,n,r):i.data[s],c=xy(i,o,u,a,t,e);o[s]=c;const l=Ds(u);return zt(u,!0),Gg(a,c,u),32!=(32&u.flags)&&Es()&&qs(i,o,c,u),0===function gI(){return R.lFrame.elementDepthCount}()&&Fe(c,o),function mI(){R.lFrame.elementDepthCount++}(),l&&(Tl(i,o,u),Al(i,u,o)),null!==r&&Nl(o,u),V}function j(){let e=xe();yc()?function vc(){R.lFrame.isParent=!1}():(e=e.parent,zt(e,!1));const t=e;(function vI(e){return R.skipHydrationRootTNode===e})(t)&&function _I(){R.skipHydrationRootTNode=null}(),function yI(){R.lFrame.elementDepthCount--}();const n=q();return n.firstCreatePass&&(bs(n,e),uc(e)&&n.queries.elementEnd(e)),null!=t.classesWithoutHost&&function jI(e){return 0!=(8&e.flags)}(t)&&Gl(n,t,v(),t.classesWithoutHost,!0),null!=t.stylesWithoutHost&&function BI(e){return 0!=(16&e.flags)}(t)&&Gl(n,t,v(),t.stylesWithoutHost,!1),j}function Ca(e,t,n,r){return V(e,t,n,r),j(),Ca}let xy=(e,t,n,r,o,i)=>(Tn(!0),Us(r,o,function Wp(){return R.lFrame.currentNamespace}()));function yi(e){return!!e&&"function"==typeof e.then}function Fy(e){return!!e&&"function"==typeof e.subscribe}function et(e,t,n,r){const o=v(),i=q(),s=xe();return function Ly(e,t,n,r,o,i,s){const a=Ds(r),c=e.firstCreatePass&&function ny(e){return e.cleanup||(e.cleanup=[])}(e),l=t[ye],d=function ty(e){return e[yr]||(e[yr]=[])}(t);let f=!0;if(3&r.type||s){const g=Je(r,t),y=s?s(g):g,D=d.length,m=s?A=>s(ie(A[r.index])):r.index;let b=null;if(!s&&a&&(b=function XT(e,t,n,r){const o=e.cleanup;if(null!=o)for(let i=0;i<o.length-1;i+=2){const s=o[i];if(s===n&&o[i+1]===r){const a=t[yr],u=o[i+2];return a.length>u?a[u]:null}"string"==typeof s&&(i+=2)}return null}(e,t,o,r.index)),null!==b)(b.__ngLastListenerFn__||b).__ngNextListenerFn__=i,b.__ngLastListenerFn__=i,f=!1;else{i=jy(r,t,l,i,!1);const A=n.listen(y,o,i);d.push(i,A),c&&c.push(o,m,D,D+1)}}else i=jy(r,t,l,i,!1);const h=r.outputs;let p;if(f&&null!==h&&(p=h[o])){const g=p.length;if(g)for(let y=0;y<g;y+=2){const H=t[p[y]][p[y+1]].subscribe(i),Se=d.length;d.push(i,H),c&&c.push(o,r.index,Se,-(Se+1))}}}(i,o,o[F],s,e,t,r),et}function Vy(e,t,n,r){try{return Ut(6,t,n),!1!==n(r)}catch(o){return oy(e,o),!1}finally{Ut(7,t,n)}}function jy(e,t,n,r,o){return function i(s){if(s===Function)return r;ai(e.componentOffset>-1?at(e.index,t):t);let u=Vy(t,n,r,s),c=i.__ngNextListenerFn__;for(;c;)u=Vy(t,n,c,s)&&u,c=c.__ngNextListenerFn__;return o&&!1===u&&s.preventDefault(),u}}function wa(e,t){return e<<17|t<<2}function xn(e){return e>>17&32767}function Yl(e){return 2|e}function er(e){return(131068&e)>>2}function Ql(e,t){return-131069&e|t<<2}function Xl(e){return 1|e}function Yy(e,t,n,r,o){const i=e[n+1],s=null===t;let a=r?xn(i):er(i),u=!1;for(;0!==a&&(!1===u||s);){const l=e[a+1];aN(e[a],t)&&(u=!0,e[a+1]=r?Xl(l):Yl(l)),a=r?xn(l):er(l)}u&&(e[n+1]=r?Yl(i):Xl(i))}function aN(e,t){return null===e||null==t||(Array.isArray(e)?e[1]:e)===t||!(!Array.isArray(e)||"string"!=typeof t)&&Or(e,t)>=0}function _a(e,t){return function Ot(e,t,n,r){const o=v(),i=q(),s=function un(e){const t=R.lFrame,n=t.bindingIndex;return t.bindingIndex=t.bindingIndex+e,n}(2);i.firstUpdatePass&&function ov(e,t,n,r){const o=e.data;if(null===o[n+1]){const i=o[$e()],s=function rv(e,t){return t>=e.expandoStartIndex}(e,n);(function uv(e,t){return 0!=(e.flags&(t?8:16))})(i,r)&&null===t&&!s&&(t=!1),t=function mN(e,t,n,r){const o=function Cc(e){const t=R.lFrame.currentDirectiveIndex;return-1===t?null:e[t]}(e);let i=r?t.residualClasses:t.residualStyles;if(null===o)0===(r?t.classBindings:t.styleBindings)&&(n=vi(n=Jl(null,e,t,n,r),t.attrs,r),i=null);else{const s=t.directiveStylingLast;if(-1===s||e[s]!==o)if(n=Jl(o,e,t,n,r),null===i){let u=function yN(e,t,n){const r=n?t.classBindings:t.styleBindings;if(0!==er(r))return e[xn(r)]}(e,t,r);void 0!==u&&Array.isArray(u)&&(u=Jl(null,e,t,u[1],r),u=vi(u,t.attrs,r),function vN(e,t,n,r){e[xn(n?t.classBindings:t.styleBindings)]=r}(e,t,r,u))}else i=function DN(e,t,n){let r;const o=t.directiveEnd;for(let i=1+t.directiveStylingLast;i<o;i++)r=vi(r,e[i].hostAttrs,n);return vi(r,t.attrs,n)}(e,t,r)}return void 0!==i&&(r?t.residualClasses=i:t.residualStyles=i),n}(o,i,t,r),function iN(e,t,n,r,o,i){let s=i?t.classBindings:t.styleBindings,a=xn(s),u=er(s);e[r]=n;let l,c=!1;if(Array.isArray(n)?(l=n[1],(null===l||Or(n,l)>0)&&(c=!0)):l=n,o)if(0!==u){const f=xn(e[a+1]);e[r+1]=wa(f,a),0!==f&&(e[f+1]=Ql(e[f+1],r)),e[a+1]=function rN(e,t){return 131071&e|t<<17}(e[a+1],r)}else e[r+1]=wa(a,0),0!==a&&(e[a+1]=Ql(e[a+1],r)),a=r;else e[r+1]=wa(u,0),0===a?a=r:e[u+1]=Ql(e[u+1],r),u=r;c&&(e[r+1]=Yl(e[r+1])),Yy(e,l,r,!0),Yy(e,l,r,!1),function sN(e,t,n,r,o){const i=o?e.residualClasses:e.residualStyles;null!=i&&"string"==typeof t&&Or(i,t)>=0&&(n[r+1]=Xl(n[r+1]))}(t,l,e,r,i),s=wa(a,u),i?t.classBindings=s:t.styleBindings=s}(o,i,t,n,s,r)}}(i,e,s,r),t!==k&&ke(o,s,t)&&function sv(e,t,n,r,o,i,s,a){if(!(3&t.type))return;const u=e.data,c=u[a+1],l=function oN(e){return 1==(1&e)}(c)?av(u,t,n,o,er(c),s):void 0;Ea(l)||(Ea(i)||function nN(e){return 2==(2&e)}(c)&&(i=av(u,null,n,o,a,s)),function XS(e,t,n,r,o){if(t)o?e.addClass(n,r):e.removeClass(n,r);else{let i=-1===r.indexOf("-")?void 0:Nn.DashCase;null==o?e.removeStyle(n,r,i):("string"==typeof o&&o.endsWith("!important")&&(o=o.slice(0,-10),i|=Nn.Important),e.setStyle(n,r,o,i))}}(r,s,_s($e(),n),o,i))}(i,i.data[$e()],o,o[F],e,o[s+1]=function EN(e,t){return null==e||""===e||("string"==typeof t?e+=t:"object"==typeof e&&(e=be(function Rn(e){return e instanceof Yg?e.changingThisBreaksApplicationSecurity:e}(e)))),e}(t,n),r,s)}(e,t,null,!0),_a}function Jl(e,t,n,r,o){let i=null;const s=n.directiveEnd;let a=n.directiveStylingLast;for(-1===a?a=n.directiveStart:a++;a<s&&(i=t[a],r=vi(r,i.hostAttrs,o),i!==e);)a++;return null!==e&&(n.directiveStylingLast=a),r}function vi(e,t,n){const r=n?1:2;let o=-1;if(null!==t)for(let i=0;i<t.length;i++){const s=t[i];"number"==typeof s?o=s:o===r&&(Array.isArray(e)||(e=void 0===e?[]:["",e]),ut(e,s,!!n||t[++i]))}return void 0===e?null:e}function av(e,t,n,r,o,i){const s=null===t;let a;for(;o>0;){const u=e[o],c=Array.isArray(u),l=c?u[1]:u,d=null===l;let f=n[o+1];f===k&&(f=d?W:void 0);let h=d?xc(f,r):l===r?f:void 0;if(c&&!Ea(h)&&(h=xc(u,r)),Ea(h)&&(a=h,s))return a;const p=e[o+1];o=s?xn(p):er(p)}if(null!==t){let u=i?t.residualClasses:t.residualStyles;null!=u&&(a=xc(u,r))}return a}function Ea(e){return void 0!==e}function z(e,t=""){const n=v(),r=q(),o=e+U,i=r.firstCreatePass?Wr(r,o,1,t,null):r.data[o],s=cv(r,n,i,t,e);n[o]=s,Es()&&qs(r,n,s,i),zt(i,!1)}let cv=(e,t,n,r,o)=>(Tn(!0),function $s(e,t){return e.createText(t)}(t[F],r));function ht(e){return bt("",e,""),ht}function bt(e,t,n){const r=v(),o=function Yr(e,t,n,r){return ke(e,Mr(),n)?t+P(n)+r:k}(r,e,t,n);return o!==k&&function hn(e,t,n){const r=_s(t,e);!function xg(e,t,n){e.setValue(t,n)}(e[F],r,n)}(r,$e(),o),bt}const tr=void 0;var GN=["en",[["a","p"],["AM","PM"],tr],[["AM","PM"],tr,tr],[["S","M","T","W","T","F","S"],["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],["Su","Mo","Tu","We","Th","Fr","Sa"]],tr,[["J","F","M","A","M","J","J","A","S","O","N","D"],["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],["January","February","March","April","May","June","July","August","September","October","November","December"]],tr,[["B","A"],["BC","AD"],["Before Christ","Anno Domini"]],0,[6,0],["M/d/yy","MMM d, y","MMMM d, y","EEEE, MMMM d, y"],["h:mm a","h:mm:ss a","h:mm:ss a z","h:mm:ss a zzzz"],["{1}, {0}",tr,"{1} 'at' {0}",tr],[".",",",";","%","+","-","E","\xd7","\u2030","\u221e","NaN",":"],["#,##0.###","#,##0%","\xa4#,##0.00","#E0"],"USD","$","US Dollar",{},"ltr",function zN(e){const n=Math.floor(Math.abs(e)),r=e.toString().replace(/^[^.]*\.?/,"").length;return 1===n&&0===r?1:5}];let oo={};function ze(e){const t=function qN(e){return e.toLowerCase().replace(/_/g,"-")}(e);let n=Nv(t);if(n)return n;const r=t.split("-")[0];if(n=Nv(r),n)return n;if("en"===r)return GN;throw new C(701,!1)}function Nv(e){return e in oo||(oo[e]=oe.ng&&oe.ng.common&&oe.ng.common.locales&&oe.ng.common.locales[e]),oo[e]}var ae=function(e){return e[e.LocaleId=0]="LocaleId",e[e.DayPeriodsFormat=1]="DayPeriodsFormat",e[e.DayPeriodsStandalone=2]="DayPeriodsStandalone",e[e.DaysFormat=3]="DaysFormat",e[e.DaysStandalone=4]="DaysStandalone",e[e.MonthsFormat=5]="MonthsFormat",e[e.MonthsStandalone=6]="MonthsStandalone",e[e.Eras=7]="Eras",e[e.FirstDayOfWeek=8]="FirstDayOfWeek",e[e.WeekendRange=9]="WeekendRange",e[e.DateFormat=10]="DateFormat",e[e.TimeFormat=11]="TimeFormat",e[e.DateTimeFormat=12]="DateTimeFormat",e[e.NumberSymbols=13]="NumberSymbols",e[e.NumberFormats=14]="NumberFormats",e[e.CurrencyCode=15]="CurrencyCode",e[e.CurrencySymbol=16]="CurrencySymbol",e[e.CurrencyName=17]="CurrencyName",e[e.Currencies=18]="Currencies",e[e.Directionality=19]="Directionality",e[e.PluralCase=20]="PluralCase",e[e.ExtraData=21]="ExtraData",e}(ae||{});const io="en-US";let Rv=io;function td(e,t,n,r,o){if(e=x(e),Array.isArray(e))for(let i=0;i<e.length;i++)td(e[i],t,n,r,o);else{const i=q(),s=v(),a=xe();let u=Xn(e)?e:x(e.provide);const c=dm(e),l=1048575&a.providerIndexes,d=a.directiveStart,f=a.providerIndexes>>20;if(Xn(e)||!e.multi){const h=new Uo(c,o,w),p=rd(u,t,o?l:l+f,d);-1===p?(Tc(As(a,s),i,u),nd(i,e,t.length),t.push(u),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(h),s.push(h)):(n[p]=h,s[p]=h)}else{const h=rd(u,t,l+f,d),p=rd(u,t,l,l+f),y=p>=0&&n[p];if(o&&!y||!o&&!(h>=0&&n[h])){Tc(As(a,s),i,u);const D=function zR(e,t,n,r,o){const i=new Uo(e,n,w);return i.multi=[],i.index=t,i.componentProviders=0,nD(i,o,r&&!n),i}(o?UR:$R,n.length,o,r,c);!o&&y&&(n[p].providerFactory=D),nd(i,e,t.length,0),t.push(u),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(D),s.push(D)}else nd(i,e,h>-1?h:p,nD(n[o?p:h],c,!o&&r));!o&&r&&y&&n[p].componentProviders++}}}function nd(e,t,n,r){const o=Xn(t),i=function A0(e){return!!e.useClass}(t);if(o||i){const u=(i?x(t.useClass):t).prototype.ngOnDestroy;if(u){const c=e.destroyHooks||(e.destroyHooks=[]);if(!o&&t.multi){const l=c.indexOf(n);-1===l?c.push(n,[r,u]):c[l+1].push(r,u)}else c.push(n,u)}}}function nD(e,t,n){return n&&e.componentProviders++,e.multi.push(t)-1}function rd(e,t,n,r){for(let o=n;o<r;o++)if(t[o]===e)return o;return-1}function $R(e,t,n,r){return od(this.multi,[])}function UR(e,t,n,r){const o=this.multi;let i;if(this.providerFactory){const s=this.providerFactory.componentProviders,a=Yn(n,n[_],this.providerFactory.index,r);i=a.slice(0,s),od(o,i);for(let u=s;u<a.length;u++)i.push(a[u])}else i=[],od(o,i);return i}function od(e,t){for(let n=0;n<e.length;n++)t.push((0,e[n])());return t}function de(e,t=[]){return n=>{n.providersResolver=(r,o)=>function HR(e,t,n){const r=q();if(r.firstCreatePass){const o=xt(e);td(n,r.data,r.blueprint,o,!0),td(t,r.data,r.blueprint,o,!1)}}(r,o?o(e):e,t)}}class nr{}class rD{}class id extends nr{constructor(t,n,r){super(),this._parent=n,this._bootstrapComponents=[],this.destroyCbs=[],this.componentFactoryResolver=new dy(this);const o=st(t);this._bootstrapComponents=fn(o.bootstrap),this._r3Injector=bm(t,n,[{provide:nr,useValue:this},{provide:sa,useValue:this.componentFactoryResolver},...r],be(t),new Set(["environment"])),this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(t)}get injector(){return this._r3Injector}destroy(){const t=this._r3Injector;!t.destroyed&&t.destroy(),this.destroyCbs.forEach(n=>n()),this.destroyCbs=null}onDestroy(t){this.destroyCbs.push(t)}}class sd extends rD{constructor(t){super(),this.moduleType=t}create(t){return new id(this.moduleType,t,[])}}class oD extends nr{constructor(t){super(),this.componentFactoryResolver=new dy(this),this.instance=null;const n=new Br([...t.providers,{provide:nr,useValue:this},{provide:sa,useValue:this.componentFactoryResolver}],t.parent||Ks(),t.debugName,new Set(["environment"]));this.injector=n,t.runEnvironmentInitializers&&n.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(t){this.injector.onDestroy(t)}}function ad(e,t,n=null){return new oD({providers:e,parent:t,debugName:n,runEnvironmentInitializers:!0}).injector}let WR=(()=>{class e{constructor(n){this._injector=n,this.cachedInjectors=new Map}getOrCreateStandaloneInjector(n){if(!n.standalone)return null;if(!this.cachedInjectors.has(n)){const r=am(0,n.type),o=r.length>0?ad([r],this._injector,`Standalone[${n.type.name}]`):null;this.cachedInjectors.set(n,o)}return this.cachedInjectors.get(n)}ngOnDestroy(){try{for(const n of this.cachedInjectors.values())null!==n&&n.destroy()}finally{this.cachedInjectors.clear()}}static{this.\u0275prov=S({token:e,providedIn:"environment",factory:()=>new e(I(ct))})}}return e})();function iD(e){e.getStandaloneInjector=t=>t.get(WR).getOrCreateStandaloneInjector(e)}function hD(e,t,n,r,o,i,s){const a=t+n;return function Kn(e,t,n,r){const o=ke(e,t,n);return ke(e,t+1,r)||o}(e,a,o,i)?function Wt(e,t,n){return e[t]=n}(e,a+2,s?r.call(s,o,i):r(o,i)):function bi(e,t){const n=e[t];return n===k?void 0:n}(e,a+2)}function vD(e,t,n,r){const o=e+U,i=v(),s=function Er(e,t){return e[t]}(i,o);return function Mi(e,t){return e[_].data[t].pure}(i,o)?hD(i,function He(){const e=R.lFrame;let t=e.bindingRootIndex;return-1===t&&(t=e.bindingRootIndex=e.tView.bindingStartIndex),t}(),t,s.transform,n,r,s):s.transform(n,r)}function Dx(e,t,n,r=!0){const o=t[_];if(function US(e,t,n,r){const o=Ne+r,i=n.length;r>0&&(n[o-1][Rt]=t),r<i-Ne?(t[Rt]=n[o],ug(n,Ne+r,t)):(n.push(t),t[Rt]=null),t[ce]=n;const s=t[ko];null!==s&&n!==s&&function zS(e,t){const n=e[wr];t[ve]!==t[ce][ce][ve]&&(e[up]=!0),null===n?e[wr]=[t]:n.push(t)}(s,t);const a=t[Ht];null!==a&&a.insertView(e),t[L]|=128}(o,t,e,n),r){const i=Wc(n,e),s=t[F],a=Gs(s,e[$t]);null!==a&&function BS(e,t,n,r,o,i){r[pe]=o,r[Pe]=t,Ko(e,r,n,1,o,i)}(o,e[Pe],s,t,a,i)}}let pn=(()=>{class e{static{this.__NG_ELEMENT_ID__=_x}}return e})();const Cx=pn,wx=class extends Cx{constructor(t,n,r){super(),this._declarationLView=t,this._declarationTContainer=n,this.elementRef=r}get ssrId(){return this._declarationTContainer.tView?.ssrId||null}createEmbeddedView(t,n){return this.createEmbeddedViewImpl(t,n)}createEmbeddedViewImpl(t,n,r){const o=function vx(e,t,n,r){const o=t.tView,a=la(e,o,n,4096&e[L]?4096:16,null,t,null,null,null,r?.injector??null,r?.hydrationInfo??null);a[ko]=e[t.index];const c=e[Ht];return null!==c&&(a[Ht]=c.createEmbeddedView(o)),Ll(o,a,n),a}(this._declarationLView,this._declarationTContainer,t,{injector:n,hydrationInfo:r});return new li(o)}};function _x(){return function Aa(e,t){return 4&e.type?new wx(t,e,zr(e,t)):null}(xe(),v())}let Ft=(()=>{class e{static{this.__NG_ELEMENT_ID__=Ax}}return e})();function Ax(){return function MD(e,t){let n;const r=t[e.index];return Be(r)?n=r:(n=Km(r,t,null,e),t[e.index]=n,da(t,n)),ID(n,t,e,r),new ED(n,e,t)}(xe(),v())}const Tx=Ft,ED=class extends Tx{constructor(t,n,r){super(),this._lContainer=t,this._hostTNode=n,this._hostLView=r}get element(){return zr(this._hostTNode,this._hostLView)}get injector(){return new Ue(this._hostTNode,this._hostLView)}get parentInjector(){const t=Ts(this._hostTNode,this._hostLView);if(Ic(t)){const n=Go(t,this._hostLView),r=zo(t);return new Ue(n[_].data[r+8],n)}return new Ue(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(t){const n=bD(this._lContainer);return null!==n&&n[t]||null}get length(){return this._lContainer.length-Ne}createEmbeddedView(t,n,r){let o,i;"number"==typeof r?o=r:null!=r&&(o=r.index,i=r.injector);const a=t.createEmbeddedViewImpl(n||{},i,null);return this.insertImpl(a,o,false),a}createComponent(t,n,r,o,i){const s=t&&!function Wo(e){return"function"==typeof e}(t);let a;if(s)a=n;else{const g=n||{};a=g.index,r=g.injector,o=g.projectableNodes,i=g.environmentInjector||g.ngModuleRef}const u=s?t:new di(G(t)),c=r||this.parentInjector;if(!i&&null==u.ngModule){const y=(s?c:this.parentInjector).get(ct,null);y&&(i=y)}G(u.componentType??{});const h=u.create(c,o,null,i);return this.insertImpl(h.hostView,a,false),h}insert(t,n){return this.insertImpl(t,n,!1)}insertImpl(t,n,r){const o=t._lView;if(function fI(e){return Be(e[ce])}(o)){const u=this.indexOf(t);if(-1!==u)this.detach(u);else{const c=o[ce],l=new ED(c,c[Pe],c[ce]);l.detach(l.indexOf(t))}}const s=this._adjustIndex(n),a=this._lContainer;return Dx(a,o,s,!r),t.attachToViewContainerRef(),ug(ld(a),s,t),t}move(t,n){return this.insert(t,n)}indexOf(t){const n=bD(this._lContainer);return null!==n?n.indexOf(t):-1}remove(t){const n=this._adjustIndex(t,-1),r=zs(this._lContainer,n);r&&(xs(ld(this._lContainer),n),Uc(r[_],r))}detach(t){const n=this._adjustIndex(t,-1),r=zs(this._lContainer,n);return r&&null!=xs(ld(this._lContainer),n)?new li(r):null}_adjustIndex(t,n=0){return t??this.length+n}};function bD(e){return e[8]}function ld(e){return e[8]||(e[8]=[])}let ID=function SD(e,t,n,r){if(e[$t])return;let o;o=8&n.type?ie(r):function Nx(e,t){const n=e[F],r=n.createComment(""),o=Je(t,e);return Qn(n,Gs(n,o),r,function ZS(e,t){return e.nextSibling(t)}(n,o),!1),r}(t,n),e[$t]=o};const wd=new M("Application Initializer");let _d=(()=>{class e{constructor(){this.initialized=!1,this.done=!1,this.donePromise=new Promise((n,r)=>{this.resolve=n,this.reject=r}),this.appInits=E(wd,{optional:!0})??[]}runInitializers(){if(this.initialized)return;const n=[];for(const o of this.appInits){const i=o();if(yi(i))n.push(i);else if(Fy(i)){const s=new Promise((a,u)=>{i.subscribe({complete:a,error:u})});n.push(s)}}const r=()=>{this.done=!0,this.resolve()};Promise.all(n).then(()=>{r()}).catch(o=>{this.reject(o)}),0===n.length&&r(),this.initialized=!0}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=S({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),JD=(()=>{class e{log(n){console.log(n)}warn(n){console.warn(n)}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=S({token:e,factory:e.\u0275fac,providedIn:"platform"})}}return e})();const gn=new M("LocaleId",{providedIn:"root",factory:()=>E(gn,$.Optional|$.SkipSelf)||function aO(){return typeof $localize<"u"&&$localize.locale||io}()});let Ra=(()=>{class e{constructor(){this.taskId=0,this.pendingTasks=new Set,this.hasPendingTasks=new mt(!1)}add(){this.hasPendingTasks.next(!0);const n=this.taskId++;return this.pendingTasks.add(n),n}remove(n){this.pendingTasks.delete(n),0===this.pendingTasks.size&&this.hasPendingTasks.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this.hasPendingTasks.next(!1)}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=S({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();class lO{constructor(t,n){this.ngModuleFactory=t,this.componentFactories=n}}let KD=(()=>{class e{compileModuleSync(n){return new sd(n)}compileModuleAsync(n){return Promise.resolve(this.compileModuleSync(n))}compileModuleAndAllComponentsSync(n){const r=this.compileModuleSync(n),i=fn(st(n).declarations).reduce((s,a)=>{const u=G(a);return u&&s.push(new di(u)),s},[]);return new lO(r,i)}compileModuleAndAllComponentsAsync(n){return Promise.resolve(this.compileModuleAndAllComponentsSync(n))}clearCache(){}clearCacheFor(n){}getModuleId(n){}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=S({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();const rC=new M(""),Oa=new M("");let Sd,Md=(()=>{class e{constructor(n,r,o){this._ngZone=n,this.registry=r,this._pendingCount=0,this._isZoneStable=!0,this._didWork=!1,this._callbacks=[],this.taskTrackingZone=null,Sd||(function xO(e){Sd=e}(o),o.addToWindow(r)),this._watchAngularEvents(),n.run(()=>{this.taskTrackingZone=typeof Zone>"u"?null:Zone.current.get("TaskTrackingZone")})}_watchAngularEvents(){this._ngZone.onUnstable.subscribe({next:()=>{this._didWork=!0,this._isZoneStable=!1}}),this._ngZone.runOutsideAngular(()=>{this._ngZone.onStable.subscribe({next:()=>{se.assertNotInAngularZone(),queueMicrotask(()=>{this._isZoneStable=!0,this._runCallbacksIfReady()})}})})}increasePendingRequestCount(){return this._pendingCount+=1,this._didWork=!0,this._pendingCount}decreasePendingRequestCount(){if(this._pendingCount-=1,this._pendingCount<0)throw new Error("pending async requests below zero");return this._runCallbacksIfReady(),this._pendingCount}isStable(){return this._isZoneStable&&0===this._pendingCount&&!this._ngZone.hasPendingMacrotasks}_runCallbacksIfReady(){if(this.isStable())queueMicrotask(()=>{for(;0!==this._callbacks.length;){let n=this._callbacks.pop();clearTimeout(n.timeoutId),n.doneCb(this._didWork)}this._didWork=!1});else{let n=this.getPendingTasks();this._callbacks=this._callbacks.filter(r=>!r.updateCb||!r.updateCb(n)||(clearTimeout(r.timeoutId),!1)),this._didWork=!0}}getPendingTasks(){return this.taskTrackingZone?this.taskTrackingZone.macroTasks.map(n=>({source:n.source,creationLocation:n.creationLocation,data:n.data})):[]}addCallback(n,r,o){let i=-1;r&&r>0&&(i=setTimeout(()=>{this._callbacks=this._callbacks.filter(s=>s.timeoutId!==i),n(this._didWork,this.getPendingTasks())},r)),this._callbacks.push({doneCb:n,timeoutId:i,updateCb:o})}whenStable(n,r,o){if(o&&!this.taskTrackingZone)throw new Error('Task tracking zone is required when passing an update callback to whenStable(). Is "zone.js/plugins/task-tracking" loaded?');this.addCallback(n,r,o),this._runCallbacksIfReady()}getPendingRequestCount(){return this._pendingCount}registerApplication(n){this.registry.registerApplication(n,this)}unregisterApplication(n){this.registry.unregisterApplication(n)}findProviders(n,r,o){return[]}static{this.\u0275fac=function(r){return new(r||e)(I(se),I(Id),I(Oa))}}static{this.\u0275prov=S({token:e,factory:e.\u0275fac})}}return e})(),Id=(()=>{class e{constructor(){this._applications=new Map}registerApplication(n,r){this._applications.set(n,r)}unregisterApplication(n){this._applications.delete(n)}unregisterAllApplications(){this._applications.clear()}getTestability(n){return this._applications.get(n)||null}getAllTestabilities(){return Array.from(this._applications.values())}getAllRootElements(){return Array.from(this._applications.keys())}findTestabilityInTree(n,r=!0){return Sd?.findTestabilityInTree(this,n,r)??null}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=S({token:e,factory:e.\u0275fac,providedIn:"platform"})}}return e})(),On=null;const oC=new M("AllowMultipleToken"),Ad=new M("PlatformDestroyListeners"),Td=new M("appBootstrapListener");class sC{constructor(t,n){this.name=t,this.token=n}}function uC(e,t,n=[]){const r=`Platform: ${t}`,o=new M(r);return(i=[])=>{let s=Nd();if(!s||s.injector.get(oC,!1)){const a=[...n,...i,{provide:o,useValue:!0}];e?e(a):function FO(e){if(On&&!On.get(oC,!1))throw new C(400,!1);(function iC(){!function XM(e){Cp=e}(()=>{throw new C(600,!1)})})(),On=e;const t=e.get(lC);(function aC(e){e.get(fm,null)?.forEach(n=>n())})(e)}(function cC(e=[],t){return dt.create({name:t,providers:[{provide:sl,useValue:"platform"},{provide:Ad,useValue:new Set([()=>On=null])},...e]})}(a,r))}return function LO(e){const t=Nd();if(!t)throw new C(401,!1);return t}()}}function Nd(){return On?.get(lC)??null}let lC=(()=>{class e{constructor(n){this._injector=n,this._modules=[],this._destroyListeners=[],this._destroyed=!1}bootstrapModuleFactory(n,r){const o=function VO(e="zone.js",t){return"noop"===e?new dA:"zone.js"===e?new se(t):e}(r?.ngZone,function dC(e){return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:e?.eventCoalescing??!1,shouldCoalesceRunChangeDetection:e?.runCoalescing??!1}}({eventCoalescing:r?.ngZoneEventCoalescing,runCoalescing:r?.ngZoneRunCoalescing}));return o.run(()=>{const i=function qR(e,t,n){return new id(e,t,n)}(n.moduleType,this.injector,function mC(e){return[{provide:se,useFactory:e},{provide:ri,multi:!0,useFactory:()=>{const t=E(BO,{optional:!0});return()=>t.initialize()}},{provide:gC,useFactory:jO},{provide:Tm,useFactory:Nm}]}(()=>o)),s=i.injector.get(dn,null);return o.runOutsideAngular(()=>{const a=o.onError.subscribe({next:u=>{s.handleError(u)}});i.onDestroy(()=>{Pa(this._modules,i),a.unsubscribe()})}),function fC(e,t,n){try{const r=n();return yi(r)?r.catch(o=>{throw t.runOutsideAngular(()=>e.handleError(o)),o}):r}catch(r){throw t.runOutsideAngular(()=>e.handleError(r)),r}}(s,o,()=>{const a=i.injector.get(_d);return a.runInitializers(),a.donePromise.then(()=>(function xv(e){vt(e,"Expected localeId to be defined"),"string"==typeof e&&(Rv=e.toLowerCase().replace(/_/g,"-"))}(i.injector.get(gn,io)||io),this._moduleDoBootstrap(i),i))})})}bootstrapModule(n,r=[]){const o=hC({},r);return function OO(e,t,n){const r=new sd(n);return Promise.resolve(r)}(0,0,n).then(i=>this.bootstrapModuleFactory(i,o))}_moduleDoBootstrap(n){const r=n.injector.get(uo);if(n._bootstrapComponents.length>0)n._bootstrapComponents.forEach(o=>r.bootstrap(o));else{if(!n.instance.ngDoBootstrap)throw new C(-403,!1);n.instance.ngDoBootstrap(r)}this._modules.push(n)}onDestroy(n){this._destroyListeners.push(n)}get injector(){return this._injector}destroy(){if(this._destroyed)throw new C(404,!1);this._modules.slice().forEach(r=>r.destroy()),this._destroyListeners.forEach(r=>r());const n=this._injector.get(Ad,null);n&&(n.forEach(r=>r()),n.clear()),this._destroyed=!0}get destroyed(){return this._destroyed}static{this.\u0275fac=function(r){return new(r||e)(I(dt))}}static{this.\u0275prov=S({token:e,factory:e.\u0275fac,providedIn:"platform"})}}return e})();function hC(e,t){return Array.isArray(t)?t.reduce(hC,e):{...e,...t}}let uo=(()=>{class e{constructor(){this._bootstrapListeners=[],this._runningTick=!1,this._destroyed=!1,this._destroyListeners=[],this._views=[],this.internalErrorHandler=E(gC),this.zoneIsStable=E(Tm),this.componentTypes=[],this.components=[],this.isStable=E(Ra).hasPendingTasks.pipe(yt(n=>n?T(!1):this.zoneIsStable),function uM(e,t=_n){return e=e??cM,_e((n,r)=>{let o,i=!0;n.subscribe(Ce(r,s=>{const a=t(s);(i||!e(o,a))&&(i=!1,o=a,r.next(s))}))})}(),Vh()),this._injector=E(ct)}get destroyed(){return this._destroyed}get injector(){return this._injector}bootstrap(n,r){const o=n instanceof ym;if(!this._injector.get(_d).done)throw!o&&function mr(e){const t=G(e)||Te(e)||je(e);return null!==t&&t.standalone}(n),new C(405,!1);let s;s=o?n:this._injector.get(sa).resolveComponentFactory(n),this.componentTypes.push(s.componentType);const a=function PO(e){return e.isBoundToModule}(s)?void 0:this._injector.get(nr),c=s.create(dt.NULL,[],r||s.selector,a),l=c.location.nativeElement,d=c.injector.get(rC,null);return d?.registerApplication(l),c.onDestroy(()=>{this.detachView(c.hostView),Pa(this.components,c),d?.unregisterApplication(l)}),this._loadComponent(c),c}tick(){if(this._runningTick)throw new C(101,!1);try{this._runningTick=!0;for(let n of this._views)n.detectChanges()}catch(n){this.internalErrorHandler(n)}finally{this._runningTick=!1}}attachView(n){const r=n;this._views.push(r),r.attachToAppRef(this)}detachView(n){const r=n;Pa(this._views,r),r.detachFromAppRef()}_loadComponent(n){this.attachView(n.hostView),this.tick(),this.components.push(n);const r=this._injector.get(Td,[]);r.push(...this._bootstrapListeners),r.forEach(o=>o(n))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(n=>n()),this._views.slice().forEach(n=>n.destroy())}finally{this._destroyed=!0,this._views=[],this._bootstrapListeners=[],this._destroyListeners=[]}}onDestroy(n){return this._destroyListeners.push(n),()=>Pa(this._destroyListeners,n)}destroy(){if(this._destroyed)throw new C(406,!1);const n=this._injector;n.destroy&&!n.destroyed&&n.destroy()}get viewCount(){return this._views.length}warnIfDestroyed(){}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=S({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function Pa(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}const gC=new M("",{providedIn:"root",factory:()=>E(dn).handleError.bind(void 0)});function jO(){const e=E(se),t=E(dn);return n=>e.runOutsideAngular(()=>t.handleError(n))}let BO=(()=>{class e{constructor(){this.zone=E(se),this.applicationRef=E(uo)}initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.zone.run(()=>{this.applicationRef.tick()})}}))}ngOnDestroy(){this._onMicrotaskEmptySubscription?.unsubscribe()}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=S({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();let Fa=(()=>{class e{static{this.__NG_ELEMENT_ID__=$O}}return e})();function $O(e){return function UO(e,t,n){if(qn(e)&&!n){const r=at(e.index,t);return new li(r,r)}return 47&e.type?new li(t[ve],t):null}(xe(),v(),16==(16&e))}class wC{constructor(){}supports(t){return ga(t)}create(t){return new ZO(t)}}const WO=(e,t)=>t;class ZO{constructor(t){this.length=0,this._linkedRecords=null,this._unlinkedRecords=null,this._previousItHead=null,this._itHead=null,this._itTail=null,this._additionsHead=null,this._additionsTail=null,this._movesHead=null,this._movesTail=null,this._removalsHead=null,this._removalsTail=null,this._identityChangesHead=null,this._identityChangesTail=null,this._trackByFn=t||WO}forEachItem(t){let n;for(n=this._itHead;null!==n;n=n._next)t(n)}forEachOperation(t){let n=this._itHead,r=this._removalsHead,o=0,i=null;for(;n||r;){const s=!r||n&&n.currentIndex<EC(r,o,i)?n:r,a=EC(s,o,i),u=s.currentIndex;if(s===r)o--,r=r._nextRemoved;else if(n=n._next,null==s.previousIndex)o++;else{i||(i=[]);const c=a-o,l=u-o;if(c!=l){for(let f=0;f<c;f++){const h=f<i.length?i[f]:i[f]=0,p=h+f;l<=p&&p<c&&(i[f]=h+1)}i[s.previousIndex]=l-c}}a!==u&&t(s,a,u)}}forEachPreviousItem(t){let n;for(n=this._previousItHead;null!==n;n=n._nextPrevious)t(n)}forEachAddedItem(t){let n;for(n=this._additionsHead;null!==n;n=n._nextAdded)t(n)}forEachMovedItem(t){let n;for(n=this._movesHead;null!==n;n=n._nextMoved)t(n)}forEachRemovedItem(t){let n;for(n=this._removalsHead;null!==n;n=n._nextRemoved)t(n)}forEachIdentityChange(t){let n;for(n=this._identityChangesHead;null!==n;n=n._nextIdentityChange)t(n)}diff(t){if(null==t&&(t=[]),!ga(t))throw new C(900,!1);return this.check(t)?this:null}onDestroy(){}check(t){this._reset();let o,i,s,n=this._itHead,r=!1;if(Array.isArray(t)){this.length=t.length;for(let a=0;a<this.length;a++)i=t[a],s=this._trackByFn(a,i),null!==n&&Object.is(n.trackById,s)?(r&&(n=this._verifyReinsertion(n,i,s,a)),Object.is(n.item,i)||this._addIdentityChange(n,i)):(n=this._mismatch(n,i,s,a),r=!0),n=n._next}else o=0,function wT(e,t){if(Array.isArray(e))for(let n=0;n<e.length;n++)t(e[n]);else{const n=e[Symbol.iterator]();let r;for(;!(r=n.next()).done;)t(r.value)}}(t,a=>{s=this._trackByFn(o,a),null!==n&&Object.is(n.trackById,s)?(r&&(n=this._verifyReinsertion(n,a,s,o)),Object.is(n.item,a)||this._addIdentityChange(n,a)):(n=this._mismatch(n,a,s,o),r=!0),n=n._next,o++}),this.length=o;return this._truncate(n),this.collection=t,this.isDirty}get isDirty(){return null!==this._additionsHead||null!==this._movesHead||null!==this._removalsHead||null!==this._identityChangesHead}_reset(){if(this.isDirty){let t;for(t=this._previousItHead=this._itHead;null!==t;t=t._next)t._nextPrevious=t._next;for(t=this._additionsHead;null!==t;t=t._nextAdded)t.previousIndex=t.currentIndex;for(this._additionsHead=this._additionsTail=null,t=this._movesHead;null!==t;t=t._nextMoved)t.previousIndex=t.currentIndex;this._movesHead=this._movesTail=null,this._removalsHead=this._removalsTail=null,this._identityChangesHead=this._identityChangesTail=null}}_mismatch(t,n,r,o){let i;return null===t?i=this._itTail:(i=t._prev,this._remove(t)),null!==(t=null===this._unlinkedRecords?null:this._unlinkedRecords.get(r,null))?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._reinsertAfter(t,i,o)):null!==(t=null===this._linkedRecords?null:this._linkedRecords.get(r,o))?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._moveAfter(t,i,o)):t=this._addAfter(new YO(n,r),i,o),t}_verifyReinsertion(t,n,r,o){let i=null===this._unlinkedRecords?null:this._unlinkedRecords.get(r,null);return null!==i?t=this._reinsertAfter(i,t._prev,o):t.currentIndex!=o&&(t.currentIndex=o,this._addToMoves(t,o)),t}_truncate(t){for(;null!==t;){const n=t._next;this._addToRemovals(this._unlink(t)),t=n}null!==this._unlinkedRecords&&this._unlinkedRecords.clear(),null!==this._additionsTail&&(this._additionsTail._nextAdded=null),null!==this._movesTail&&(this._movesTail._nextMoved=null),null!==this._itTail&&(this._itTail._next=null),null!==this._removalsTail&&(this._removalsTail._nextRemoved=null),null!==this._identityChangesTail&&(this._identityChangesTail._nextIdentityChange=null)}_reinsertAfter(t,n,r){null!==this._unlinkedRecords&&this._unlinkedRecords.remove(t);const o=t._prevRemoved,i=t._nextRemoved;return null===o?this._removalsHead=i:o._nextRemoved=i,null===i?this._removalsTail=o:i._prevRemoved=o,this._insertAfter(t,n,r),this._addToMoves(t,r),t}_moveAfter(t,n,r){return this._unlink(t),this._insertAfter(t,n,r),this._addToMoves(t,r),t}_addAfter(t,n,r){return this._insertAfter(t,n,r),this._additionsTail=null===this._additionsTail?this._additionsHead=t:this._additionsTail._nextAdded=t,t}_insertAfter(t,n,r){const o=null===n?this._itHead:n._next;return t._next=o,t._prev=n,null===o?this._itTail=t:o._prev=t,null===n?this._itHead=t:n._next=t,null===this._linkedRecords&&(this._linkedRecords=new _C),this._linkedRecords.put(t),t.currentIndex=r,t}_remove(t){return this._addToRemovals(this._unlink(t))}_unlink(t){null!==this._linkedRecords&&this._linkedRecords.remove(t);const n=t._prev,r=t._next;return null===n?this._itHead=r:n._next=r,null===r?this._itTail=n:r._prev=n,t}_addToMoves(t,n){return t.previousIndex===n||(this._movesTail=null===this._movesTail?this._movesHead=t:this._movesTail._nextMoved=t),t}_addToRemovals(t){return null===this._unlinkedRecords&&(this._unlinkedRecords=new _C),this._unlinkedRecords.put(t),t.currentIndex=null,t._nextRemoved=null,null===this._removalsTail?(this._removalsTail=this._removalsHead=t,t._prevRemoved=null):(t._prevRemoved=this._removalsTail,this._removalsTail=this._removalsTail._nextRemoved=t),t}_addIdentityChange(t,n){return t.item=n,this._identityChangesTail=null===this._identityChangesTail?this._identityChangesHead=t:this._identityChangesTail._nextIdentityChange=t,t}}class YO{constructor(t,n){this.item=t,this.trackById=n,this.currentIndex=null,this.previousIndex=null,this._nextPrevious=null,this._prev=null,this._next=null,this._prevDup=null,this._nextDup=null,this._prevRemoved=null,this._nextRemoved=null,this._nextAdded=null,this._nextMoved=null,this._nextIdentityChange=null}}class QO{constructor(){this._head=null,this._tail=null}add(t){null===this._head?(this._head=this._tail=t,t._nextDup=null,t._prevDup=null):(this._tail._nextDup=t,t._prevDup=this._tail,t._nextDup=null,this._tail=t)}get(t,n){let r;for(r=this._head;null!==r;r=r._nextDup)if((null===n||n<=r.currentIndex)&&Object.is(r.trackById,t))return r;return null}remove(t){const n=t._prevDup,r=t._nextDup;return null===n?this._head=r:n._nextDup=r,null===r?this._tail=n:r._prevDup=n,null===this._head}}class _C{constructor(){this.map=new Map}put(t){const n=t.trackById;let r=this.map.get(n);r||(r=new QO,this.map.set(n,r)),r.add(t)}get(t,n){const o=this.map.get(t);return o?o.get(t,n):null}remove(t){const n=t.trackById;return this.map.get(n).remove(t)&&this.map.delete(n),t}get isEmpty(){return 0===this.map.size}clear(){this.map.clear()}}function EC(e,t,n){const r=e.previousIndex;if(null===r)return r;let o=0;return n&&r<n.length&&(o=n[r]),r+t+o}function MC(){return new Va([new wC])}let Va=(()=>{class e{static{this.\u0275prov=S({token:e,providedIn:"root",factory:MC})}constructor(n){this.factories=n}static create(n,r){if(null!=r){const o=r.factories.slice();n=n.concat(o)}return new e(n)}static extend(n){return{provide:e,useFactory:r=>e.create(n,r||MC()),deps:[[e,new Fs,new Ps]]}}find(n){const r=this.factories.find(o=>o.supports(n));if(null!=r)return r;throw new C(901,!1)}}return e})();const tP=uC(null,"core",[]);let nP=(()=>{class e{constructor(n){}static{this.\u0275fac=function(r){return new(r||e)(I(uo))}}static{this.\u0275mod=Dt({type:e})}static{this.\u0275inj=it({})}}return e})();let kd=null;function Pn(){return kd}class mP{}const pt=new M("DocumentToken");let Ld=(()=>{class e{historyGo(n){throw new Error("Not implemented")}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=S({token:e,factory:function(){return E(vP)},providedIn:"platform"})}}return e})();const yP=new M("Location Initialized");let vP=(()=>{class e extends Ld{constructor(){super(),this._doc=E(pt),this._location=window.location,this._history=window.history}getBaseHrefFromDOM(){return Pn().getBaseHref(this._doc)}onPopState(n){const r=Pn().getGlobalEventTarget(this._doc,"window");return r.addEventListener("popstate",n,!1),()=>r.removeEventListener("popstate",n)}onHashChange(n){const r=Pn().getGlobalEventTarget(this._doc,"window");return r.addEventListener("hashchange",n,!1),()=>r.removeEventListener("hashchange",n)}get href(){return this._location.href}get protocol(){return this._location.protocol}get hostname(){return this._location.hostname}get port(){return this._location.port}get pathname(){return this._location.pathname}get search(){return this._location.search}get hash(){return this._location.hash}set pathname(n){this._location.pathname=n}pushState(n,r,o){this._history.pushState(n,r,o)}replaceState(n,r,o){this._history.replaceState(n,r,o)}forward(){this._history.forward()}back(){this._history.back()}historyGo(n=0){this._history.go(n)}getState(){return this._history.state}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=S({token:e,factory:function(){return new e},providedIn:"platform"})}}return e})();function Vd(e,t){if(0==e.length)return t;if(0==t.length)return e;let n=0;return e.endsWith("/")&&n++,t.startsWith("/")&&n++,2==n?e+t.substring(1):1==n?e+t:e+"/"+t}function FC(e){const t=e.match(/#|\?|$/),n=t&&t.index||e.length;return e.slice(0,n-("/"===e[n-1]?1:0))+e.slice(n)}function mn(e){return e&&"?"!==e[0]?"?"+e:e}let or=(()=>{class e{historyGo(n){throw new Error("Not implemented")}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=S({token:e,factory:function(){return E(LC)},providedIn:"root"})}}return e})();const kC=new M("appBaseHref");let LC=(()=>{class e extends or{constructor(n,r){super(),this._platformLocation=n,this._removeListenerFns=[],this._baseHref=r??this._platformLocation.getBaseHrefFromDOM()??E(pt).location?.origin??""}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(n){this._removeListenerFns.push(this._platformLocation.onPopState(n),this._platformLocation.onHashChange(n))}getBaseHref(){return this._baseHref}prepareExternalUrl(n){return Vd(this._baseHref,n)}path(n=!1){const r=this._platformLocation.pathname+mn(this._platformLocation.search),o=this._platformLocation.hash;return o&&n?`${r}${o}`:r}pushState(n,r,o,i){const s=this.prepareExternalUrl(o+mn(i));this._platformLocation.pushState(n,r,s)}replaceState(n,r,o,i){const s=this.prepareExternalUrl(o+mn(i));this._platformLocation.replaceState(n,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(n=0){this._platformLocation.historyGo?.(n)}static{this.\u0275fac=function(r){return new(r||e)(I(Ld),I(kC,8))}}static{this.\u0275prov=S({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),DP=(()=>{class e extends or{constructor(n,r){super(),this._platformLocation=n,this._baseHref="",this._removeListenerFns=[],null!=r&&(this._baseHref=r)}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(n){this._removeListenerFns.push(this._platformLocation.onPopState(n),this._platformLocation.onHashChange(n))}getBaseHref(){return this._baseHref}path(n=!1){let r=this._platformLocation.hash;return null==r&&(r="#"),r.length>0?r.substring(1):r}prepareExternalUrl(n){const r=Vd(this._baseHref,n);return r.length>0?"#"+r:r}pushState(n,r,o,i){let s=this.prepareExternalUrl(o+mn(i));0==s.length&&(s=this._platformLocation.pathname),this._platformLocation.pushState(n,r,s)}replaceState(n,r,o,i){let s=this.prepareExternalUrl(o+mn(i));0==s.length&&(s=this._platformLocation.pathname),this._platformLocation.replaceState(n,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(n=0){this._platformLocation.historyGo?.(n)}static{this.\u0275fac=function(r){return new(r||e)(I(Ld),I(kC,8))}}static{this.\u0275prov=S({token:e,factory:e.\u0275fac})}}return e})(),jd=(()=>{class e{constructor(n){this._subject=new ge,this._urlChangeListeners=[],this._urlChangeSubscription=null,this._locationStrategy=n;const r=this._locationStrategy.getBaseHref();this._basePath=function _P(e){if(new RegExp("^(https?:)?//").test(e)){const[,n]=e.split(/\/\/[^\/]+/);return n}return e}(FC(VC(r))),this._locationStrategy.onPopState(o=>{this._subject.emit({url:this.path(!0),pop:!0,state:o.state,type:o.type})})}ngOnDestroy(){this._urlChangeSubscription?.unsubscribe(),this._urlChangeListeners=[]}path(n=!1){return this.normalize(this._locationStrategy.path(n))}getState(){return this._locationStrategy.getState()}isCurrentPathEqualTo(n,r=""){return this.path()==this.normalize(n+mn(r))}normalize(n){return e.stripTrailingSlash(function wP(e,t){if(!e||!t.startsWith(e))return t;const n=t.substring(e.length);return""===n||["/",";","?","#"].includes(n[0])?n:t}(this._basePath,VC(n)))}prepareExternalUrl(n){return n&&"/"!==n[0]&&(n="/"+n),this._locationStrategy.prepareExternalUrl(n)}go(n,r="",o=null){this._locationStrategy.pushState(o,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+mn(r)),o)}replaceState(n,r="",o=null){this._locationStrategy.replaceState(o,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+mn(r)),o)}forward(){this._locationStrategy.forward()}back(){this._locationStrategy.back()}historyGo(n=0){this._locationStrategy.historyGo?.(n)}onUrlChange(n){return this._urlChangeListeners.push(n),this._urlChangeSubscription||(this._urlChangeSubscription=this.subscribe(r=>{this._notifyUrlChangeListeners(r.url,r.state)})),()=>{const r=this._urlChangeListeners.indexOf(n);this._urlChangeListeners.splice(r,1),0===this._urlChangeListeners.length&&(this._urlChangeSubscription?.unsubscribe(),this._urlChangeSubscription=null)}}_notifyUrlChangeListeners(n="",r){this._urlChangeListeners.forEach(o=>o(n,r))}subscribe(n,r,o){return this._subject.subscribe({next:n,error:r,complete:o})}static{this.normalizeQueryParams=mn}static{this.joinWithSlash=Vd}static{this.stripTrailingSlash=FC}static{this.\u0275fac=function(r){return new(r||e)(I(or))}}static{this.\u0275prov=S({token:e,factory:function(){return function CP(){return new jd(I(or))}()},providedIn:"root"})}}return e})();function VC(e){return e.replace(/\/index.html$/,"")}var qe=function(e){return e[e.Format=0]="Format",e[e.Standalone=1]="Standalone",e}(qe||{}),ne=function(e){return e[e.Narrow=0]="Narrow",e[e.Abbreviated=1]="Abbreviated",e[e.Wide=2]="Wide",e[e.Short=3]="Short",e}(ne||{}),gt=function(e){return e[e.Short=0]="Short",e[e.Medium=1]="Medium",e[e.Long=2]="Long",e[e.Full=3]="Full",e}(gt||{}),De=function(e){return e[e.Decimal=0]="Decimal",e[e.Group=1]="Group",e[e.List=2]="List",e[e.PercentSign=3]="PercentSign",e[e.PlusSign=4]="PlusSign",e[e.MinusSign=5]="MinusSign",e[e.Exponential=6]="Exponential",e[e.SuperscriptingExponent=7]="SuperscriptingExponent",e[e.PerMille=8]="PerMille",e[e.Infinity=9]="Infinity",e[e.NaN=10]="NaN",e[e.TimeSeparator=11]="TimeSeparator",e[e.CurrencyDecimal=12]="CurrencyDecimal",e[e.CurrencyGroup=13]="CurrencyGroup",e}(De||{});function Ha(e,t){return It(ze(e)[ae.DateFormat],t)}function $a(e,t){return It(ze(e)[ae.TimeFormat],t)}function Ua(e,t){return It(ze(e)[ae.DateTimeFormat],t)}function Mt(e,t){const n=ze(e),r=n[ae.NumberSymbols][t];if(typeof r>"u"){if(t===De.CurrencyDecimal)return n[ae.NumberSymbols][De.Decimal];if(t===De.CurrencyGroup)return n[ae.NumberSymbols][De.Group]}return r}function BC(e){if(!e[ae.ExtraData])throw new Error(`Missing extra locale data for the locale "${e[ae.LocaleId]}". Use "registerLocaleData" to load new data. See the "I18n guide" on angular.io to know more.`)}function It(e,t){for(let n=t;n>-1;n--)if(typeof e[n]<"u")return e[n];throw new Error("Locale data API: locale data undefined")}function Hd(e){const[t,n]=e.split(":");return{hours:+t,minutes:+n}}const kP=/^(\d{4,})-?(\d\d)-?(\d\d)(?:T(\d\d)(?::?(\d\d)(?::?(\d\d)(?:\.(\d+))?)?)?(Z|([+-])(\d\d):?(\d\d))?)?$/,Ti={},LP=/((?:[^BEGHLMOSWYZabcdhmswyz']+)|(?:'(?:[^']|'')*')|(?:G{1,5}|y{1,4}|Y{1,4}|M{1,5}|L{1,5}|w{1,2}|W{1}|d{1,2}|E{1,6}|c{1,6}|a{1,5}|b{1,5}|B{1,5}|h{1,2}|H{1,2}|m{1,2}|s{1,2}|S{1,3}|z{1,4}|Z{1,5}|O{1,4}))([\s\S]*)/;var yn=function(e){return e[e.Short=0]="Short",e[e.ShortGMT=1]="ShortGMT",e[e.Long=2]="Long",e[e.Extended=3]="Extended",e}(yn||{}),Y=function(e){return e[e.FullYear=0]="FullYear",e[e.Month=1]="Month",e[e.Date=2]="Date",e[e.Hours=3]="Hours",e[e.Minutes=4]="Minutes",e[e.Seconds=5]="Seconds",e[e.FractionalSeconds=6]="FractionalSeconds",e[e.Day=7]="Day",e}(Y||{}),Q=function(e){return e[e.DayPeriods=0]="DayPeriods",e[e.Days=1]="Days",e[e.Months=2]="Months",e[e.Eras=3]="Eras",e}(Q||{});function VP(e,t,n,r){let o=function WP(e){if(UC(e))return e;if("number"==typeof e&&!isNaN(e))return new Date(e);if("string"==typeof e){if(e=e.trim(),/^(\d{4}(-\d{1,2}(-\d{1,2})?)?)$/.test(e)){const[o,i=1,s=1]=e.split("-").map(a=>+a);return za(o,i-1,s)}const n=parseFloat(e);if(!isNaN(e-n))return new Date(n);let r;if(r=e.match(kP))return function ZP(e){const t=new Date(0);let n=0,r=0;const o=e[8]?t.setUTCFullYear:t.setFullYear,i=e[8]?t.setUTCHours:t.setHours;e[9]&&(n=Number(e[9]+e[10]),r=Number(e[9]+e[11])),o.call(t,Number(e[1]),Number(e[2])-1,Number(e[3]));const s=Number(e[4]||0)-n,a=Number(e[5]||0)-r,u=Number(e[6]||0),c=Math.floor(1e3*parseFloat("0."+(e[7]||0)));return i.call(t,s,a,u,c),t}(r)}const t=new Date(e);if(!UC(t))throw new Error(`Unable to convert "${e}" into a date`);return t}(e);t=vn(n,t)||t;let a,s=[];for(;t;){if(a=LP.exec(t),!a){s.push(t);break}{s=s.concat(a.slice(1));const l=s.pop();if(!l)break;t=l}}let u=o.getTimezoneOffset();r&&(u=$C(r,u),o=function qP(e,t,n){const r=n?-1:1,o=e.getTimezoneOffset();return function GP(e,t){return(e=new Date(e.getTime())).setMinutes(e.getMinutes()+t),e}(e,r*($C(t,o)-o))}(o,r,!0));let c="";return s.forEach(l=>{const d=function zP(e){if(Ud[e])return Ud[e];let t;switch(e){case"G":case"GG":case"GGG":t=ue(Q.Eras,ne.Abbreviated);break;case"GGGG":t=ue(Q.Eras,ne.Wide);break;case"GGGGG":t=ue(Q.Eras,ne.Narrow);break;case"y":t=we(Y.FullYear,1,0,!1,!0);break;case"yy":t=we(Y.FullYear,2,0,!0,!0);break;case"yyy":t=we(Y.FullYear,3,0,!1,!0);break;case"yyyy":t=we(Y.FullYear,4,0,!1,!0);break;case"Y":t=Za(1);break;case"YY":t=Za(2,!0);break;case"YYY":t=Za(3);break;case"YYYY":t=Za(4);break;case"M":case"L":t=we(Y.Month,1,1);break;case"MM":case"LL":t=we(Y.Month,2,1);break;case"MMM":t=ue(Q.Months,ne.Abbreviated);break;case"MMMM":t=ue(Q.Months,ne.Wide);break;case"MMMMM":t=ue(Q.Months,ne.Narrow);break;case"LLL":t=ue(Q.Months,ne.Abbreviated,qe.Standalone);break;case"LLLL":t=ue(Q.Months,ne.Wide,qe.Standalone);break;case"LLLLL":t=ue(Q.Months,ne.Narrow,qe.Standalone);break;case"w":t=$d(1);break;case"ww":t=$d(2);break;case"W":t=$d(1,!0);break;case"d":t=we(Y.Date,1);break;case"dd":t=we(Y.Date,2);break;case"c":case"cc":t=we(Y.Day,1);break;case"ccc":t=ue(Q.Days,ne.Abbreviated,qe.Standalone);break;case"cccc":t=ue(Q.Days,ne.Wide,qe.Standalone);break;case"ccccc":t=ue(Q.Days,ne.Narrow,qe.Standalone);break;case"cccccc":t=ue(Q.Days,ne.Short,qe.Standalone);break;case"E":case"EE":case"EEE":t=ue(Q.Days,ne.Abbreviated);break;case"EEEE":t=ue(Q.Days,ne.Wide);break;case"EEEEE":t=ue(Q.Days,ne.Narrow);break;case"EEEEEE":t=ue(Q.Days,ne.Short);break;case"a":case"aa":case"aaa":t=ue(Q.DayPeriods,ne.Abbreviated);break;case"aaaa":t=ue(Q.DayPeriods,ne.Wide);break;case"aaaaa":t=ue(Q.DayPeriods,ne.Narrow);break;case"b":case"bb":case"bbb":t=ue(Q.DayPeriods,ne.Abbreviated,qe.Standalone,!0);break;case"bbbb":t=ue(Q.DayPeriods,ne.Wide,qe.Standalone,!0);break;case"bbbbb":t=ue(Q.DayPeriods,ne.Narrow,qe.Standalone,!0);break;case"B":case"BB":case"BBB":t=ue(Q.DayPeriods,ne.Abbreviated,qe.Format,!0);break;case"BBBB":t=ue(Q.DayPeriods,ne.Wide,qe.Format,!0);break;case"BBBBB":t=ue(Q.DayPeriods,ne.Narrow,qe.Format,!0);break;case"h":t=we(Y.Hours,1,-12);break;case"hh":t=we(Y.Hours,2,-12);break;case"H":t=we(Y.Hours,1);break;case"HH":t=we(Y.Hours,2);break;case"m":t=we(Y.Minutes,1);break;case"mm":t=we(Y.Minutes,2);break;case"s":t=we(Y.Seconds,1);break;case"ss":t=we(Y.Seconds,2);break;case"S":t=we(Y.FractionalSeconds,1);break;case"SS":t=we(Y.FractionalSeconds,2);break;case"SSS":t=we(Y.FractionalSeconds,3);break;case"Z":case"ZZ":case"ZZZ":t=qa(yn.Short);break;case"ZZZZZ":t=qa(yn.Extended);break;case"O":case"OO":case"OOO":case"z":case"zz":case"zzz":t=qa(yn.ShortGMT);break;case"OOOO":case"ZZZZ":case"zzzz":t=qa(yn.Long);break;default:return null}return Ud[e]=t,t}(l);c+=d?d(o,n,u):"''"===l?"'":l.replace(/(^'|'$)/g,"").replace(/''/g,"'")}),c}function za(e,t,n){const r=new Date(0);return r.setFullYear(e,t,n),r.setHours(0,0,0),r}function vn(e,t){const n=function bP(e){return ze(e)[ae.LocaleId]}(e);if(Ti[n]=Ti[n]||{},Ti[n][t])return Ti[n][t];let r="";switch(t){case"shortDate":r=Ha(e,gt.Short);break;case"mediumDate":r=Ha(e,gt.Medium);break;case"longDate":r=Ha(e,gt.Long);break;case"fullDate":r=Ha(e,gt.Full);break;case"shortTime":r=$a(e,gt.Short);break;case"mediumTime":r=$a(e,gt.Medium);break;case"longTime":r=$a(e,gt.Long);break;case"fullTime":r=$a(e,gt.Full);break;case"short":const o=vn(e,"shortTime"),i=vn(e,"shortDate");r=Ga(Ua(e,gt.Short),[o,i]);break;case"medium":const s=vn(e,"mediumTime"),a=vn(e,"mediumDate");r=Ga(Ua(e,gt.Medium),[s,a]);break;case"long":const u=vn(e,"longTime"),c=vn(e,"longDate");r=Ga(Ua(e,gt.Long),[u,c]);break;case"full":const l=vn(e,"fullTime"),d=vn(e,"fullDate");r=Ga(Ua(e,gt.Full),[l,d])}return r&&(Ti[n][t]=r),r}function Ga(e,t){return t&&(e=e.replace(/\{([^}]+)}/g,function(n,r){return null!=t&&r in t?t[r]:n})),e}function kt(e,t,n="-",r,o){let i="";(e<0||o&&e<=0)&&(o?e=1-e:(e=-e,i=n));let s=String(e);for(;s.length<t;)s="0"+s;return r&&(s=s.slice(s.length-t)),i+s}function we(e,t,n=0,r=!1,o=!1){return function(i,s){let a=function BP(e,t){switch(e){case Y.FullYear:return t.getFullYear();case Y.Month:return t.getMonth();case Y.Date:return t.getDate();case Y.Hours:return t.getHours();case Y.Minutes:return t.getMinutes();case Y.Seconds:return t.getSeconds();case Y.FractionalSeconds:return t.getMilliseconds();case Y.Day:return t.getDay();default:throw new Error(`Unknown DateType value "${e}".`)}}(e,i);if((n>0||a>-n)&&(a+=n),e===Y.Hours)0===a&&-12===n&&(a=12);else if(e===Y.FractionalSeconds)return function jP(e,t){return kt(e,3).substring(0,t)}(a,t);const u=Mt(s,De.MinusSign);return kt(a,t,u,r,o)}}function ue(e,t,n=qe.Format,r=!1){return function(o,i){return function HP(e,t,n,r,o,i){switch(n){case Q.Months:return function SP(e,t,n){const r=ze(e),i=It([r[ae.MonthsFormat],r[ae.MonthsStandalone]],t);return It(i,n)}(t,o,r)[e.getMonth()];case Q.Days:return function IP(e,t,n){const r=ze(e),i=It([r[ae.DaysFormat],r[ae.DaysStandalone]],t);return It(i,n)}(t,o,r)[e.getDay()];case Q.DayPeriods:const s=e.getHours(),a=e.getMinutes();if(i){const c=function RP(e){const t=ze(e);return BC(t),(t[ae.ExtraData][2]||[]).map(r=>"string"==typeof r?Hd(r):[Hd(r[0]),Hd(r[1])])}(t),l=function xP(e,t,n){const r=ze(e);BC(r);const i=It([r[ae.ExtraData][0],r[ae.ExtraData][1]],t)||[];return It(i,n)||[]}(t,o,r),d=c.findIndex(f=>{if(Array.isArray(f)){const[h,p]=f,g=s>=h.hours&&a>=h.minutes,y=s<p.hours||s===p.hours&&a<p.minutes;if(h.hours<p.hours){if(g&&y)return!0}else if(g||y)return!0}else if(f.hours===s&&f.minutes===a)return!0;return!1});if(-1!==d)return l[d]}return function MP(e,t,n){const r=ze(e),i=It([r[ae.DayPeriodsFormat],r[ae.DayPeriodsStandalone]],t);return It(i,n)}(t,o,r)[s<12?0:1];case Q.Eras:return function AP(e,t){return It(ze(e)[ae.Eras],t)}(t,r)[e.getFullYear()<=0?0:1];default:throw new Error(`unexpected translation type ${n}`)}}(o,i,e,t,n,r)}}function qa(e){return function(t,n,r){const o=-1*r,i=Mt(n,De.MinusSign),s=o>0?Math.floor(o/60):Math.ceil(o/60);switch(e){case yn.Short:return(o>=0?"+":"")+kt(s,2,i)+kt(Math.abs(o%60),2,i);case yn.ShortGMT:return"GMT"+(o>=0?"+":"")+kt(s,1,i);case yn.Long:return"GMT"+(o>=0?"+":"")+kt(s,2,i)+":"+kt(Math.abs(o%60),2,i);case yn.Extended:return 0===r?"Z":(o>=0?"+":"")+kt(s,2,i)+":"+kt(Math.abs(o%60),2,i);default:throw new Error(`Unknown zone width "${e}"`)}}}const $P=0,Wa=4;function HC(e){return za(e.getFullYear(),e.getMonth(),e.getDate()+(Wa-e.getDay()))}function $d(e,t=!1){return function(n,r){let o;if(t){const i=new Date(n.getFullYear(),n.getMonth(),1).getDay()-1,s=n.getDate();o=1+Math.floor((s+i)/7)}else{const i=HC(n),s=function UP(e){const t=za(e,$P,1).getDay();return za(e,0,1+(t<=Wa?Wa:Wa+7)-t)}(i.getFullYear()),a=i.getTime()-s.getTime();o=1+Math.round(a/6048e5)}return kt(o,e,Mt(r,De.MinusSign))}}function Za(e,t=!1){return function(n,r){return kt(HC(n).getFullYear(),e,Mt(r,De.MinusSign),t)}}const Ud={};function $C(e,t){e=e.replace(/:/g,"");const n=Date.parse("Jan 01, 1970 00:00:00 "+e)/6e4;return isNaN(n)?t:n}function UC(e){return e instanceof Date&&!isNaN(e.valueOf())}function WC(e,t){t=encodeURIComponent(t);for(const n of e.split(";")){const r=n.indexOf("="),[o,i]=-1==r?[n,""]:[n.slice(0,r),n.slice(r+1)];if(o.trim()===t)return decodeURIComponent(i)}return null}class uF{constructor(t,n,r,o){this.$implicit=t,this.ngForOf=n,this.index=r,this.count=o}get first(){return 0===this.index}get last(){return this.index===this.count-1}get even(){return this.index%2==0}get odd(){return!this.even}}let QC=(()=>{class e{set ngForOf(n){this._ngForOf=n,this._ngForOfDirty=!0}set ngForTrackBy(n){this._trackByFn=n}get ngForTrackBy(){return this._trackByFn}constructor(n,r,o){this._viewContainer=n,this._template=r,this._differs=o,this._ngForOf=null,this._ngForOfDirty=!0,this._differ=null}set ngForTemplate(n){n&&(this._template=n)}ngDoCheck(){if(this._ngForOfDirty){this._ngForOfDirty=!1;const n=this._ngForOf;!this._differ&&n&&(this._differ=this._differs.find(n).create(this.ngForTrackBy))}if(this._differ){const n=this._differ.diff(this._ngForOf);n&&this._applyChanges(n)}}_applyChanges(n){const r=this._viewContainer;n.forEachOperation((o,i,s)=>{if(null==o.previousIndex)r.createEmbeddedView(this._template,new uF(o.item,this._ngForOf,-1,-1),null===s?void 0:s);else if(null==s)r.remove(null===i?void 0:i);else if(null!==i){const a=r.get(i);r.move(a,s),XC(a,o)}});for(let o=0,i=r.length;o<i;o++){const a=r.get(o).context;a.index=o,a.count=i,a.ngForOf=this._ngForOf}n.forEachIdentityChange(o=>{XC(r.get(o.currentIndex),o)})}static ngTemplateContextGuard(n,r){return!0}static{this.\u0275fac=function(r){return new(r||e)(w(Ft),w(pn),w(Va))}}static{this.\u0275dir=O({type:e,selectors:[["","ngFor","","ngForOf",""]],inputs:{ngForOf:"ngForOf",ngForTrackBy:"ngForTrackBy",ngForTemplate:"ngForTemplate"},standalone:!0})}}return e})();function XC(e,t){e.context.$implicit=t.item}const EF=new M("DATE_PIPE_DEFAULT_TIMEZONE"),bF=new M("DATE_PIPE_DEFAULT_OPTIONS");let ew=(()=>{class e{constructor(n,r,o){this.locale=n,this.defaultTimezone=r,this.defaultOptions=o}transform(n,r,o,i){if(null==n||""===n||n!=n)return null;try{return VP(n,r??this.defaultOptions?.dateFormat??"mediumDate",i||this.locale,o??this.defaultOptions?.timezone??this.defaultTimezone??void 0)}catch(s){throw function Lt(e,t){return new C(2100,!1)}()}}static{this.\u0275fac=function(r){return new(r||e)(w(gn,16),w(EF,24),w(bF,24))}}static{this.\u0275pipe=Qe({name:"date",type:e,pure:!0,standalone:!0})}}return e})(),nw=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275mod=Dt({type:e})}static{this.\u0275inj=it({})}}return e})();function ow(e){return"server"===e}let LF=(()=>{class e{static{this.\u0275prov=S({token:e,providedIn:"root",factory:()=>new VF(I(pt),window)})}}return e})();class VF{constructor(t,n){this.document=t,this.window=n,this.offset=()=>[0,0]}setOffset(t){this.offset=Array.isArray(t)?()=>t:t}getScrollPosition(){return this.supportsScrolling()?[this.window.pageXOffset,this.window.pageYOffset]:[0,0]}scrollToPosition(t){this.supportsScrolling()&&this.window.scrollTo(t[0],t[1])}scrollToAnchor(t){if(!this.supportsScrolling())return;const n=function jF(e,t){const n=e.getElementById(t)||e.getElementsByName(t)[0];if(n)return n;if("function"==typeof e.createTreeWalker&&e.body&&"function"==typeof e.body.attachShadow){const r=e.createTreeWalker(e.body,NodeFilter.SHOW_ELEMENT);let o=r.currentNode;for(;o;){const i=o.shadowRoot;if(i){const s=i.getElementById(t)||i.querySelector(`[name="${t}"]`);if(s)return s}o=r.nextNode()}}return null}(this.document,t);n&&(this.scrollToElement(n),n.focus())}setHistoryScrollRestoration(t){this.supportsScrolling()&&(this.window.history.scrollRestoration=t)}scrollToElement(t){const n=t.getBoundingClientRect(),r=n.left+this.window.pageXOffset,o=n.top+this.window.pageYOffset,i=this.offset();this.window.scrollTo(r-i[0],o-i[1])}supportsScrolling(){try{return!!this.window&&!!this.window.scrollTo&&"pageXOffset"in this.window}catch{return!1}}}class iw{}class u1 extends mP{constructor(){super(...arguments),this.supportsDOMEvents=!0}}class nf extends u1{static makeCurrent(){!function gP(e){kd||(kd=e)}(new nf)}onAndCancel(t,n,r){return t.addEventListener(n,r),()=>{t.removeEventListener(n,r)}}dispatchEvent(t,n){t.dispatchEvent(n)}remove(t){t.parentNode&&t.parentNode.removeChild(t)}createElement(t,n){return(n=n||this.getDefaultDocument()).createElement(t)}createHtmlDocument(){return document.implementation.createHTMLDocument("fakeTitle")}getDefaultDocument(){return document}isElementNode(t){return t.nodeType===Node.ELEMENT_NODE}isShadowRoot(t){return t instanceof DocumentFragment}getGlobalEventTarget(t,n){return"window"===n?window:"document"===n?t:"body"===n?t.body:null}getBaseHref(t){const n=function c1(){return xi=xi||document.querySelector("base"),xi?xi.getAttribute("href"):null}();return null==n?null:function l1(e){Ja=Ja||document.createElement("a"),Ja.setAttribute("href",e);const t=Ja.pathname;return"/"===t.charAt(0)?t:`/${t}`}(n)}resetBaseElement(){xi=null}getUserAgent(){return window.navigator.userAgent}getCookie(t){return WC(document.cookie,t)}}let Ja,xi=null,f1=(()=>{class e{build(){return new XMLHttpRequest}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=S({token:e,factory:e.\u0275fac})}}return e})();const rf=new M("EventManagerPlugins");let lw=(()=>{class e{constructor(n,r){this._zone=r,this._eventNameToPlugin=new Map,n.forEach(o=>{o.manager=this}),this._plugins=n.slice().reverse()}addEventListener(n,r,o){return this._findPluginFor(r).addEventListener(n,r,o)}getZone(){return this._zone}_findPluginFor(n){let r=this._eventNameToPlugin.get(n);if(r)return r;if(r=this._plugins.find(i=>i.supports(n)),!r)throw new C(5101,!1);return this._eventNameToPlugin.set(n,r),r}static{this.\u0275fac=function(r){return new(r||e)(I(rf),I(se))}}static{this.\u0275prov=S({token:e,factory:e.\u0275fac})}}return e})();class dw{constructor(t){this._doc=t}}const sf="ng-app-id";let fw=(()=>{class e{constructor(n,r,o,i={}){this.doc=n,this.appId=r,this.nonce=o,this.platformId=i,this.styleRef=new Map,this.hostNodes=new Set,this.styleNodesInDOM=this.collectServerRenderedStyles(),this.platformIsServer=ow(i),this.resetHostNodes()}addStyles(n){for(const r of n)1===this.changeUsageCount(r,1)&&this.onStyleAdded(r)}removeStyles(n){for(const r of n)this.changeUsageCount(r,-1)<=0&&this.onStyleRemoved(r)}ngOnDestroy(){const n=this.styleNodesInDOM;n&&(n.forEach(r=>r.remove()),n.clear());for(const r of this.getAllStyles())this.onStyleRemoved(r);this.resetHostNodes()}addHost(n){this.hostNodes.add(n);for(const r of this.getAllStyles())this.addStyleToHost(n,r)}removeHost(n){this.hostNodes.delete(n)}getAllStyles(){return this.styleRef.keys()}onStyleAdded(n){for(const r of this.hostNodes)this.addStyleToHost(r,n)}onStyleRemoved(n){const r=this.styleRef;r.get(n)?.elements?.forEach(o=>o.remove()),r.delete(n)}collectServerRenderedStyles(){const n=this.doc.head?.querySelectorAll(`style[${sf}="${this.appId}"]`);if(n?.length){const r=new Map;return n.forEach(o=>{null!=o.textContent&&r.set(o.textContent,o)}),r}return null}changeUsageCount(n,r){const o=this.styleRef;if(o.has(n)){const i=o.get(n);return i.usage+=r,i.usage}return o.set(n,{usage:r,elements:[]}),r}getStyleElement(n,r){const o=this.styleNodesInDOM,i=o?.get(r);if(i?.parentNode===n)return o.delete(r),i.removeAttribute(sf),i;{const s=this.doc.createElement("style");return this.nonce&&s.setAttribute("nonce",this.nonce),s.textContent=r,this.platformIsServer&&s.setAttribute(sf,this.appId),s}}addStyleToHost(n,r){const o=this.getStyleElement(n,r);n.appendChild(o);const i=this.styleRef,s=i.get(r)?.elements;s?s.push(o):i.set(r,{elements:[o],usage:1})}resetHostNodes(){const n=this.hostNodes;n.clear(),n.add(this.doc.head)}static{this.\u0275fac=function(r){return new(r||e)(I(pt),I(ea),I(hm,8),I(Jn))}}static{this.\u0275prov=S({token:e,factory:e.\u0275fac})}}return e})();const af={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/",math:"http://www.w3.org/1998/MathML/"},uf=/%COMP%/g,m1=new M("RemoveStylesOnCompDestroy",{providedIn:"root",factory:()=>!1});function pw(e,t){return t.map(n=>n.replace(uf,e))}let gw=(()=>{class e{constructor(n,r,o,i,s,a,u,c=null){this.eventManager=n,this.sharedStylesHost=r,this.appId=o,this.removeStylesOnCompDestroy=i,this.doc=s,this.platformId=a,this.ngZone=u,this.nonce=c,this.rendererByCompId=new Map,this.platformIsServer=ow(a),this.defaultRenderer=new cf(n,s,u,this.platformIsServer)}createRenderer(n,r){if(!n||!r)return this.defaultRenderer;this.platformIsServer&&r.encapsulation===Tt.ShadowDom&&(r={...r,encapsulation:Tt.Emulated});const o=this.getOrCreateRenderer(n,r);return o instanceof yw?o.applyToHost(n):o instanceof lf&&o.applyStyles(),o}getOrCreateRenderer(n,r){const o=this.rendererByCompId;let i=o.get(r.id);if(!i){const s=this.doc,a=this.ngZone,u=this.eventManager,c=this.sharedStylesHost,l=this.removeStylesOnCompDestroy,d=this.platformIsServer;switch(r.encapsulation){case Tt.Emulated:i=new yw(u,c,r,this.appId,l,s,a,d);break;case Tt.ShadowDom:return new C1(u,c,n,r,s,a,this.nonce,d);default:i=new lf(u,c,r,l,s,a,d)}o.set(r.id,i)}return i}ngOnDestroy(){this.rendererByCompId.clear()}static{this.\u0275fac=function(r){return new(r||e)(I(lw),I(fw),I(ea),I(m1),I(pt),I(Jn),I(se),I(hm))}}static{this.\u0275prov=S({token:e,factory:e.\u0275fac})}}return e})();class cf{constructor(t,n,r,o){this.eventManager=t,this.doc=n,this.ngZone=r,this.platformIsServer=o,this.data=Object.create(null),this.destroyNode=null}destroy(){}createElement(t,n){return n?this.doc.createElementNS(af[n]||n,t):this.doc.createElement(t)}createComment(t){return this.doc.createComment(t)}createText(t){return this.doc.createTextNode(t)}appendChild(t,n){(mw(t)?t.content:t).appendChild(n)}insertBefore(t,n,r){t&&(mw(t)?t.content:t).insertBefore(n,r)}removeChild(t,n){t&&t.removeChild(n)}selectRootElement(t,n){let r="string"==typeof t?this.doc.querySelector(t):t;if(!r)throw new C(-5104,!1);return n||(r.textContent=""),r}parentNode(t){return t.parentNode}nextSibling(t){return t.nextSibling}setAttribute(t,n,r,o){if(o){n=o+":"+n;const i=af[o];i?t.setAttributeNS(i,n,r):t.setAttribute(n,r)}else t.setAttribute(n,r)}removeAttribute(t,n,r){if(r){const o=af[r];o?t.removeAttributeNS(o,n):t.removeAttribute(`${r}:${n}`)}else t.removeAttribute(n)}addClass(t,n){t.classList.add(n)}removeClass(t,n){t.classList.remove(n)}setStyle(t,n,r,o){o&(Nn.DashCase|Nn.Important)?t.style.setProperty(n,r,o&Nn.Important?"important":""):t.style[n]=r}removeStyle(t,n,r){r&Nn.DashCase?t.style.removeProperty(n):t.style[n]=""}setProperty(t,n,r){t[n]=r}setValue(t,n){t.nodeValue=n}listen(t,n,r){if("string"==typeof t&&!(t=Pn().getGlobalEventTarget(this.doc,t)))throw new Error(`Unsupported event target ${t} for event ${n}`);return this.eventManager.addEventListener(t,n,this.decoratePreventDefault(r))}decoratePreventDefault(t){return n=>{if("__ngUnwrap__"===n)return t;!1===(this.platformIsServer?this.ngZone.runGuarded(()=>t(n)):t(n))&&n.preventDefault()}}}function mw(e){return"TEMPLATE"===e.tagName&&void 0!==e.content}class C1 extends cf{constructor(t,n,r,o,i,s,a,u){super(t,i,s,u),this.sharedStylesHost=n,this.hostEl=r,this.shadowRoot=r.attachShadow({mode:"open"}),this.sharedStylesHost.addHost(this.shadowRoot);const c=pw(o.id,o.styles);for(const l of c){const d=document.createElement("style");a&&d.setAttribute("nonce",a),d.textContent=l,this.shadowRoot.appendChild(d)}}nodeOrShadowRoot(t){return t===this.hostEl?this.shadowRoot:t}appendChild(t,n){return super.appendChild(this.nodeOrShadowRoot(t),n)}insertBefore(t,n,r){return super.insertBefore(this.nodeOrShadowRoot(t),n,r)}removeChild(t,n){return super.removeChild(this.nodeOrShadowRoot(t),n)}parentNode(t){return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(t)))}destroy(){this.sharedStylesHost.removeHost(this.shadowRoot)}}class lf extends cf{constructor(t,n,r,o,i,s,a,u){super(t,i,s,a),this.sharedStylesHost=n,this.removeStylesOnCompDestroy=o,this.styles=u?pw(u,r.styles):r.styles}applyStyles(){this.sharedStylesHost.addStyles(this.styles)}destroy(){this.removeStylesOnCompDestroy&&this.sharedStylesHost.removeStyles(this.styles)}}class yw extends lf{constructor(t,n,r,o,i,s,a,u){const c=o+"-"+r.id;super(t,n,r,i,s,a,u,c),this.contentAttr=function y1(e){return"_ngcontent-%COMP%".replace(uf,e)}(c),this.hostAttr=function v1(e){return"_nghost-%COMP%".replace(uf,e)}(c)}applyToHost(t){this.applyStyles(),this.setAttribute(t,this.hostAttr,"")}createElement(t,n){const r=super.createElement(t,n);return super.setAttribute(r,this.contentAttr,""),r}}let w1=(()=>{class e extends dw{constructor(n){super(n)}supports(n){return!0}addEventListener(n,r,o){return n.addEventListener(r,o,!1),()=>this.removeEventListener(n,r,o)}removeEventListener(n,r,o){return n.removeEventListener(r,o)}static{this.\u0275fac=function(r){return new(r||e)(I(pt))}}static{this.\u0275prov=S({token:e,factory:e.\u0275fac})}}return e})();const vw=["alt","control","meta","shift"],_1={"\b":"Backspace","\t":"Tab","\x7f":"Delete","\x1b":"Escape",Del:"Delete",Esc:"Escape",Left:"ArrowLeft",Right:"ArrowRight",Up:"ArrowUp",Down:"ArrowDown",Menu:"ContextMenu",Scroll:"ScrollLock",Win:"OS"},E1={alt:e=>e.altKey,control:e=>e.ctrlKey,meta:e=>e.metaKey,shift:e=>e.shiftKey};let b1=(()=>{class e extends dw{constructor(n){super(n)}supports(n){return null!=e.parseEventName(n)}addEventListener(n,r,o){const i=e.parseEventName(r),s=e.eventCallback(i.fullKey,o,this.manager.getZone());return this.manager.getZone().runOutsideAngular(()=>Pn().onAndCancel(n,i.domEventName,s))}static parseEventName(n){const r=n.toLowerCase().split("."),o=r.shift();if(0===r.length||"keydown"!==o&&"keyup"!==o)return null;const i=e._normalizeKey(r.pop());let s="",a=r.indexOf("code");if(a>-1&&(r.splice(a,1),s="code."),vw.forEach(c=>{const l=r.indexOf(c);l>-1&&(r.splice(l,1),s+=c+".")}),s+=i,0!=r.length||0===i.length)return null;const u={};return u.domEventName=o,u.fullKey=s,u}static matchEventFullKeyCode(n,r){let o=_1[n.key]||n.key,i="";return r.indexOf("code.")>-1&&(o=n.code,i="code."),!(null==o||!o)&&(o=o.toLowerCase()," "===o?o="space":"."===o&&(o="dot"),vw.forEach(s=>{s!==o&&(0,E1[s])(n)&&(i+=s+".")}),i+=o,i===r)}static eventCallback(n,r,o){return i=>{e.matchEventFullKeyCode(i,n)&&o.runGuarded(()=>r(i))}}static _normalizeKey(n){return"esc"===n?"escape":n}static{this.\u0275fac=function(r){return new(r||e)(I(pt))}}static{this.\u0275prov=S({token:e,factory:e.\u0275fac})}}return e})();const A1=uC(tP,"browser",[{provide:Jn,useValue:"browser"},{provide:fm,useValue:function M1(){nf.makeCurrent()},multi:!0},{provide:pt,useFactory:function S1(){return function n0(e){Qc=e}(document),document},deps:[]}]),T1=new M(""),ww=[{provide:Oa,useClass:class d1{addToWindow(t){oe.getAngularTestability=(r,o=!0)=>{const i=t.findTestabilityInTree(r,o);if(null==i)throw new C(5103,!1);return i},oe.getAllAngularTestabilities=()=>t.getAllTestabilities(),oe.getAllAngularRootElements=()=>t.getAllRootElements(),oe.frameworkStabilizers||(oe.frameworkStabilizers=[]),oe.frameworkStabilizers.push(r=>{const o=oe.getAllAngularTestabilities();let i=o.length,s=!1;const a=function(u){s=s||u,i--,0==i&&r(s)};o.forEach(u=>{u.whenStable(a)})})}findTestabilityInTree(t,n,r){return null==n?null:t.getTestability(n)??(r?Pn().isShadowRoot(n)?this.findTestabilityInTree(t,n.host,!0):this.findTestabilityInTree(t,n.parentElement,!0):null)}},deps:[]},{provide:rC,useClass:Md,deps:[se,Id,Oa]},{provide:Md,useClass:Md,deps:[se,Id,Oa]}],_w=[{provide:sl,useValue:"root"},{provide:dn,useFactory:function I1(){return new dn},deps:[]},{provide:rf,useClass:w1,multi:!0,deps:[pt,se,Jn]},{provide:rf,useClass:b1,multi:!0,deps:[pt]},gw,fw,lw,{provide:Dm,useExisting:gw},{provide:iw,useClass:f1,deps:[]},[]];let N1=(()=>{class e{constructor(n){}static withServerTransition(n){return{ngModule:e,providers:[{provide:ea,useValue:n.appId}]}}static{this.\u0275fac=function(r){return new(r||e)(I(T1,12))}}static{this.\u0275mod=Dt({type:e})}static{this.\u0275inj=it({providers:[..._w,...ww],imports:[nw,nP]})}}return e})(),Ew=(()=>{class e{constructor(n){this._doc=n}getTitle(){return this._doc.title}setTitle(n){this._doc.title=n||""}static{this.\u0275fac=function(r){return new(r||e)(I(pt))}}static{this.\u0275prov=S({token:e,factory:function(r){let o=null;return o=r?new r:function x1(){return new Ew(I(pt))}(),o},providedIn:"root"})}}return e})();function fo(e,t){return te(t)?Ae(e,t,1):Ae(e,1)}function Dn(e,t){return _e((n,r)=>{let o=0;n.subscribe(Ce(r,i=>e.call(t,i,o++)&&r.next(i)))})}function Oi(e){return _e((t,n)=>{try{t.subscribe(n)}finally{n.add(e)}})}typeof window<"u"&&window;class Ka{}class eu{}class Xt{constructor(t){this.normalizedNames=new Map,this.lazyUpdate=null,t?"string"==typeof t?this.lazyInit=()=>{this.headers=new Map,t.split("\n").forEach(n=>{const r=n.indexOf(":");if(r>0){const o=n.slice(0,r),i=o.toLowerCase(),s=n.slice(r+1).trim();this.maybeSetNormalizedName(o,i),this.headers.has(i)?this.headers.get(i).push(s):this.headers.set(i,[s])}})}:typeof Headers<"u"&&t instanceof Headers?(this.headers=new Map,t.forEach((n,r)=>{this.setHeaderEntries(r,n)})):this.lazyInit=()=>{this.headers=new Map,Object.entries(t).forEach(([n,r])=>{this.setHeaderEntries(n,r)})}:this.headers=new Map}has(t){return this.init(),this.headers.has(t.toLowerCase())}get(t){this.init();const n=this.headers.get(t.toLowerCase());return n&&n.length>0?n[0]:null}keys(){return this.init(),Array.from(this.normalizedNames.values())}getAll(t){return this.init(),this.headers.get(t.toLowerCase())||null}append(t,n){return this.clone({name:t,value:n,op:"a"})}set(t,n){return this.clone({name:t,value:n,op:"s"})}delete(t,n){return this.clone({name:t,value:n,op:"d"})}maybeSetNormalizedName(t,n){this.normalizedNames.has(n)||this.normalizedNames.set(n,t)}init(){this.lazyInit&&(this.lazyInit instanceof Xt?this.copyFrom(this.lazyInit):this.lazyInit(),this.lazyInit=null,this.lazyUpdate&&(this.lazyUpdate.forEach(t=>this.applyUpdate(t)),this.lazyUpdate=null))}copyFrom(t){t.init(),Array.from(t.headers.keys()).forEach(n=>{this.headers.set(n,t.headers.get(n)),this.normalizedNames.set(n,t.normalizedNames.get(n))})}clone(t){const n=new Xt;return n.lazyInit=this.lazyInit&&this.lazyInit instanceof Xt?this.lazyInit:this,n.lazyUpdate=(this.lazyUpdate||[]).concat([t]),n}applyUpdate(t){const n=t.name.toLowerCase();switch(t.op){case"a":case"s":let r=t.value;if("string"==typeof r&&(r=[r]),0===r.length)return;this.maybeSetNormalizedName(t.name,n);const o=("a"===t.op?this.headers.get(n):void 0)||[];o.push(...r),this.headers.set(n,o);break;case"d":const i=t.value;if(i){let s=this.headers.get(n);if(!s)return;s=s.filter(a=>-1===i.indexOf(a)),0===s.length?(this.headers.delete(n),this.normalizedNames.delete(n)):this.headers.set(n,s)}else this.headers.delete(n),this.normalizedNames.delete(n)}}setHeaderEntries(t,n){const r=(Array.isArray(n)?n:[n]).map(i=>i.toString()),o=t.toLowerCase();this.headers.set(o,r),this.maybeSetNormalizedName(t,o)}forEach(t){this.init(),Array.from(this.normalizedNames.keys()).forEach(n=>t(this.normalizedNames.get(n),this.headers.get(n)))}}class L1{encodeKey(t){return Sw(t)}encodeValue(t){return Sw(t)}decodeKey(t){return decodeURIComponent(t)}decodeValue(t){return decodeURIComponent(t)}}const j1=/%(\d[a-f0-9])/gi,B1={40:"@","3A":":",24:"$","2C":",","3B":";","3D":"=","3F":"?","2F":"/"};function Sw(e){return encodeURIComponent(e).replace(j1,(t,n)=>B1[n]??t)}function tu(e){return`${e}`}class kn{constructor(t={}){if(this.updates=null,this.cloneFrom=null,this.encoder=t.encoder||new L1,t.fromString){if(t.fromObject)throw new Error("Cannot specify both fromString and fromObject.");this.map=function V1(e,t){const n=new Map;return e.length>0&&e.replace(/^\?/,"").split("&").forEach(o=>{const i=o.indexOf("="),[s,a]=-1==i?[t.decodeKey(o),""]:[t.decodeKey(o.slice(0,i)),t.decodeValue(o.slice(i+1))],u=n.get(s)||[];u.push(a),n.set(s,u)}),n}(t.fromString,this.encoder)}else t.fromObject?(this.map=new Map,Object.keys(t.fromObject).forEach(n=>{const r=t.fromObject[n],o=Array.isArray(r)?r.map(tu):[tu(r)];this.map.set(n,o)})):this.map=null}has(t){return this.init(),this.map.has(t)}get(t){this.init();const n=this.map.get(t);return n?n[0]:null}getAll(t){return this.init(),this.map.get(t)||null}keys(){return this.init(),Array.from(this.map.keys())}append(t,n){return this.clone({param:t,value:n,op:"a"})}appendAll(t){const n=[];return Object.keys(t).forEach(r=>{const o=t[r];Array.isArray(o)?o.forEach(i=>{n.push({param:r,value:i,op:"a"})}):n.push({param:r,value:o,op:"a"})}),this.clone(n)}set(t,n){return this.clone({param:t,value:n,op:"s"})}delete(t,n){return this.clone({param:t,value:n,op:"d"})}toString(){return this.init(),this.keys().map(t=>{const n=this.encoder.encodeKey(t);return this.map.get(t).map(r=>n+"="+this.encoder.encodeValue(r)).join("&")}).filter(t=>""!==t).join("&")}clone(t){const n=new kn({encoder:this.encoder});return n.cloneFrom=this.cloneFrom||this,n.updates=(this.updates||[]).concat(t),n}init(){null===this.map&&(this.map=new Map),null!==this.cloneFrom&&(this.cloneFrom.init(),this.cloneFrom.keys().forEach(t=>this.map.set(t,this.cloneFrom.map.get(t))),this.updates.forEach(t=>{switch(t.op){case"a":case"s":const n=("a"===t.op?this.map.get(t.param):void 0)||[];n.push(tu(t.value)),this.map.set(t.param,n);break;case"d":if(void 0===t.value){this.map.delete(t.param);break}{let r=this.map.get(t.param)||[];const o=r.indexOf(tu(t.value));-1!==o&&r.splice(o,1),r.length>0?this.map.set(t.param,r):this.map.delete(t.param)}}}),this.cloneFrom=this.updates=null)}}class H1{constructor(){this.map=new Map}set(t,n){return this.map.set(t,n),this}get(t){return this.map.has(t)||this.map.set(t,t.defaultValue()),this.map.get(t)}delete(t){return this.map.delete(t),this}has(t){return this.map.has(t)}keys(){return this.map.keys()}}function Aw(e){return typeof ArrayBuffer<"u"&&e instanceof ArrayBuffer}function Tw(e){return typeof Blob<"u"&&e instanceof Blob}function Nw(e){return typeof FormData<"u"&&e instanceof FormData}class Pi{constructor(t,n,r,o){let i;if(this.url=n,this.body=null,this.reportProgress=!1,this.withCredentials=!1,this.responseType="json",this.method=t.toUpperCase(),function $1(e){switch(e){case"DELETE":case"GET":case"HEAD":case"OPTIONS":case"JSONP":return!1;default:return!0}}(this.method)||o?(this.body=void 0!==r?r:null,i=o):i=r,i&&(this.reportProgress=!!i.reportProgress,this.withCredentials=!!i.withCredentials,i.responseType&&(this.responseType=i.responseType),i.headers&&(this.headers=i.headers),i.context&&(this.context=i.context),i.params&&(this.params=i.params)),this.headers||(this.headers=new Xt),this.context||(this.context=new H1),this.params){const s=this.params.toString();if(0===s.length)this.urlWithParams=n;else{const a=n.indexOf("?");this.urlWithParams=n+(-1===a?"?":a<n.length-1?"&":"")+s}}else this.params=new kn,this.urlWithParams=n}serializeBody(){return null===this.body?null:Aw(this.body)||Tw(this.body)||Nw(this.body)||function U1(e){return typeof URLSearchParams<"u"&&e instanceof URLSearchParams}(this.body)||"string"==typeof this.body?this.body:this.body instanceof kn?this.body.toString():"object"==typeof this.body||"boolean"==typeof this.body||Array.isArray(this.body)?JSON.stringify(this.body):this.body.toString()}detectContentTypeHeader(){return null===this.body||Nw(this.body)?null:Tw(this.body)?this.body.type||null:Aw(this.body)?null:"string"==typeof this.body?"text/plain":this.body instanceof kn?"application/x-www-form-urlencoded;charset=UTF-8":"object"==typeof this.body||"number"==typeof this.body||"boolean"==typeof this.body?"application/json":null}clone(t={}){const n=t.method||this.method,r=t.url||this.url,o=t.responseType||this.responseType,i=void 0!==t.body?t.body:this.body,s=void 0!==t.withCredentials?t.withCredentials:this.withCredentials,a=void 0!==t.reportProgress?t.reportProgress:this.reportProgress;let u=t.headers||this.headers,c=t.params||this.params;const l=t.context??this.context;return void 0!==t.setHeaders&&(u=Object.keys(t.setHeaders).reduce((d,f)=>d.set(f,t.setHeaders[f]),u)),t.setParams&&(c=Object.keys(t.setParams).reduce((d,f)=>d.set(f,t.setParams[f]),c)),new Pi(n,r,i,{params:c,headers:u,context:l,reportProgress:a,responseType:o,withCredentials:s})}}var ho=function(e){return e[e.Sent=0]="Sent",e[e.UploadProgress=1]="UploadProgress",e[e.ResponseHeader=2]="ResponseHeader",e[e.DownloadProgress=3]="DownloadProgress",e[e.Response=4]="Response",e[e.User=5]="User",e}(ho||{});class ff{constructor(t,n=200,r="OK"){this.headers=t.headers||new Xt,this.status=void 0!==t.status?t.status:n,this.statusText=t.statusText||r,this.url=t.url||null,this.ok=this.status>=200&&this.status<300}}class hf extends ff{constructor(t={}){super(t),this.type=ho.ResponseHeader}clone(t={}){return new hf({headers:t.headers||this.headers,status:void 0!==t.status?t.status:this.status,statusText:t.statusText||this.statusText,url:t.url||this.url||void 0})}}class po extends ff{constructor(t={}){super(t),this.type=ho.Response,this.body=void 0!==t.body?t.body:null}clone(t={}){return new po({body:void 0!==t.body?t.body:this.body,headers:t.headers||this.headers,status:void 0!==t.status?t.status:this.status,statusText:t.statusText||this.statusText,url:t.url||this.url||void 0})}}class Rw extends ff{constructor(t){super(t,0,"Unknown Error"),this.name="HttpErrorResponse",this.ok=!1,this.message=this.status>=200&&this.status<300?`Http failure during parsing for ${t.url||"(unknown url)"}`:`Http failure response for ${t.url||"(unknown url)"}: ${t.status} ${t.statusText}`,this.error=t.error||null}}function pf(e,t){return{body:t,headers:e.headers,context:e.context,observe:e.observe,params:e.params,reportProgress:e.reportProgress,responseType:e.responseType,withCredentials:e.withCredentials}}let xw=(()=>{class e{constructor(n){this.handler=n}request(n,r,o={}){let i;if(n instanceof Pi)i=n;else{let u,c;u=o.headers instanceof Xt?o.headers:new Xt(o.headers),o.params&&(c=o.params instanceof kn?o.params:new kn({fromObject:o.params})),i=new Pi(n,r,void 0!==o.body?o.body:null,{headers:u,context:o.context,params:c,reportProgress:o.reportProgress,responseType:o.responseType||"json",withCredentials:o.withCredentials})}const s=T(i).pipe(fo(u=>this.handler.handle(u)));if(n instanceof Pi||"events"===o.observe)return s;const a=s.pipe(Dn(u=>u instanceof po));switch(o.observe||"body"){case"body":switch(i.responseType){case"arraybuffer":return a.pipe(Z(u=>{if(null!==u.body&&!(u.body instanceof ArrayBuffer))throw new Error("Response is not an ArrayBuffer.");return u.body}));case"blob":return a.pipe(Z(u=>{if(null!==u.body&&!(u.body instanceof Blob))throw new Error("Response is not a Blob.");return u.body}));case"text":return a.pipe(Z(u=>{if(null!==u.body&&"string"!=typeof u.body)throw new Error("Response is not a string.");return u.body}));default:return a.pipe(Z(u=>u.body))}case"response":return a;default:throw new Error(`Unreachable: unhandled observe type ${o.observe}}`)}}delete(n,r={}){return this.request("DELETE",n,r)}get(n,r={}){return this.request("GET",n,r)}head(n,r={}){return this.request("HEAD",n,r)}jsonp(n,r){return this.request("JSONP",n,{params:(new kn).append(r,"JSONP_CALLBACK"),observe:"body",responseType:"json"})}options(n,r={}){return this.request("OPTIONS",n,r)}patch(n,r,o={}){return this.request("PATCH",n,pf(o,r))}post(n,r,o={}){return this.request("POST",n,pf(o,r))}put(n,r,o={}){return this.request("PUT",n,pf(o,r))}static{this.\u0275fac=function(r){return new(r||e)(I(Ka))}}static{this.\u0275prov=S({token:e,factory:e.\u0275fac})}}return e})();function Fw(e,t){return t(e)}function G1(e,t){return(n,r)=>t.intercept(n,{handle:o=>e(o,r)})}const W1=new M(""),Fi=new M(""),kw=new M("");function Z1(){let e=null;return(t,n)=>{null===e&&(e=(E(W1,{optional:!0})??[]).reduceRight(G1,Fw));const r=E(Ra),o=r.add();return e(t,n).pipe(Oi(()=>r.remove(o)))}}let Lw=(()=>{class e extends Ka{constructor(n,r){super(),this.backend=n,this.injector=r,this.chain=null,this.pendingTasks=E(Ra)}handle(n){if(null===this.chain){const o=Array.from(new Set([...this.injector.get(Fi),...this.injector.get(kw,[])]));this.chain=o.reduceRight((i,s)=>function q1(e,t,n){return(r,o)=>n.runInContext(()=>t(r,i=>e(i,o)))}(i,s,this.injector),Fw)}const r=this.pendingTasks.add();return this.chain(n,o=>this.backend.handle(o)).pipe(Oi(()=>this.pendingTasks.remove(r)))}static{this.\u0275fac=function(r){return new(r||e)(I(eu),I(ct))}}static{this.\u0275prov=S({token:e,factory:e.\u0275fac})}}return e})();const J1=/^\)\]\}',?\n/;let jw=(()=>{class e{constructor(n){this.xhrFactory=n}handle(n){if("JSONP"===n.method)throw new C(-2800,!1);const r=this.xhrFactory;return(r.\u0275loadImpl?Ee(r.\u0275loadImpl()):T(null)).pipe(yt(()=>new he(i=>{const s=r.build();if(s.open(n.method,n.urlWithParams),n.withCredentials&&(s.withCredentials=!0),n.headers.forEach((g,y)=>s.setRequestHeader(g,y.join(","))),n.headers.has("Accept")||s.setRequestHeader("Accept","application/json, text/plain, */*"),!n.headers.has("Content-Type")){const g=n.detectContentTypeHeader();null!==g&&s.setRequestHeader("Content-Type",g)}if(n.responseType){const g=n.responseType.toLowerCase();s.responseType="json"!==g?g:"text"}const a=n.serializeBody();let u=null;const c=()=>{if(null!==u)return u;const g=s.statusText||"OK",y=new Xt(s.getAllResponseHeaders()),D=function K1(e){return"responseURL"in e&&e.responseURL?e.responseURL:/^X-Request-URL:/m.test(e.getAllResponseHeaders())?e.getResponseHeader("X-Request-URL"):null}(s)||n.url;return u=new hf({headers:y,status:s.status,statusText:g,url:D}),u},l=()=>{let{headers:g,status:y,statusText:D,url:m}=c(),b=null;204!==y&&(b=typeof s.response>"u"?s.responseText:s.response),0===y&&(y=b?200:0);let A=y>=200&&y<300;if("json"===n.responseType&&"string"==typeof b){const H=b;b=b.replace(J1,"");try{b=""!==b?JSON.parse(b):null}catch(Se){b=H,A&&(A=!1,b={error:Se,text:b})}}A?(i.next(new po({body:b,headers:g,status:y,statusText:D,url:m||void 0})),i.complete()):i.error(new Rw({error:b,headers:g,status:y,statusText:D,url:m||void 0}))},d=g=>{const{url:y}=c(),D=new Rw({error:g,status:s.status||0,statusText:s.statusText||"Unknown Error",url:y||void 0});i.error(D)};let f=!1;const h=g=>{f||(i.next(c()),f=!0);let y={type:ho.DownloadProgress,loaded:g.loaded};g.lengthComputable&&(y.total=g.total),"text"===n.responseType&&s.responseText&&(y.partialText=s.responseText),i.next(y)},p=g=>{let y={type:ho.UploadProgress,loaded:g.loaded};g.lengthComputable&&(y.total=g.total),i.next(y)};return s.addEventListener("load",l),s.addEventListener("error",d),s.addEventListener("timeout",d),s.addEventListener("abort",d),n.reportProgress&&(s.addEventListener("progress",h),null!==a&&s.upload&&s.upload.addEventListener("progress",p)),s.send(a),i.next({type:ho.Sent}),()=>{s.removeEventListener("error",d),s.removeEventListener("abort",d),s.removeEventListener("load",l),s.removeEventListener("timeout",d),n.reportProgress&&(s.removeEventListener("progress",h),null!==a&&s.upload&&s.upload.removeEventListener("progress",p)),s.readyState!==s.DONE&&s.abort()}})))}static{this.\u0275fac=function(r){return new(r||e)(I(iw))}}static{this.\u0275prov=S({token:e,factory:e.\u0275fac})}}return e})();const gf=new M("XSRF_ENABLED"),Bw=new M("XSRF_COOKIE_NAME",{providedIn:"root",factory:()=>"XSRF-TOKEN"}),Hw=new M("XSRF_HEADER_NAME",{providedIn:"root",factory:()=>"X-XSRF-TOKEN"});class $w{}let nk=(()=>{class e{constructor(n,r,o){this.doc=n,this.platform=r,this.cookieName=o,this.lastCookieString="",this.lastToken=null,this.parseCount=0}getToken(){if("server"===this.platform)return null;const n=this.doc.cookie||"";return n!==this.lastCookieString&&(this.parseCount++,this.lastToken=WC(n,this.cookieName),this.lastCookieString=n),this.lastToken}static{this.\u0275fac=function(r){return new(r||e)(I(pt),I(Jn),I(Bw))}}static{this.\u0275prov=S({token:e,factory:e.\u0275fac})}}return e})();function rk(e,t){const n=e.url.toLowerCase();if(!E(gf)||"GET"===e.method||"HEAD"===e.method||n.startsWith("http://")||n.startsWith("https://"))return t(e);const r=E($w).getToken(),o=E(Hw);return null!=r&&!e.headers.has(o)&&(e=e.clone({headers:e.headers.set(o,r)})),t(e)}var Ln=function(e){return e[e.Interceptors=0]="Interceptors",e[e.LegacyInterceptors=1]="LegacyInterceptors",e[e.CustomXsrfConfiguration=2]="CustomXsrfConfiguration",e[e.NoXsrfProtection=3]="NoXsrfProtection",e[e.JsonpSupport=4]="JsonpSupport",e[e.RequestsMadeViaParent=5]="RequestsMadeViaParent",e[e.Fetch=6]="Fetch",e}(Ln||{});function ir(e,t){return{\u0275kind:e,\u0275providers:t}}function ok(...e){const t=[xw,jw,Lw,{provide:Ka,useExisting:Lw},{provide:eu,useExisting:jw},{provide:Fi,useValue:rk,multi:!0},{provide:gf,useValue:!0},{provide:$w,useClass:nk}];for(const n of e)t.push(...n.\u0275providers);return function rl(e){return{\u0275providers:e}}(t)}const Uw=new M("LEGACY_INTERCEPTOR_FN");let zw=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275mod=Dt({type:e})}static{this.\u0275inj=it({providers:[ok(ir(Ln.LegacyInterceptors,[{provide:Uw,useFactory:Z1},{provide:Fi,useExisting:Uw,multi:!0}]))]})}}return e})();const{isArray:fk}=Array,{getPrototypeOf:hk,prototype:pk,keys:gk}=Object;function Gw(e){if(1===e.length){const t=e[0];if(fk(t))return{args:t,keys:null};if(function mk(e){return e&&"object"==typeof e&&hk(e)===pk}(t)){const n=gk(t);return{args:n.map(r=>t[r]),keys:n}}}return{args:e,keys:null}}const{isArray:yk}=Array;function qw(e){return Z(t=>function vk(e,t){return yk(t)?e(...t):e(t)}(e,t))}function Ww(e,t){return e.reduce((n,r,o)=>(n[r]=t[o],n),{})}function mf(...e){const t=To(e),n=Ph(e),{args:r,keys:o}=Gw(e);if(0===r.length)return Ee([],t);const i=new he(function Dk(e,t,n=_n){return r=>{Zw(t,()=>{const{length:o}=e,i=new Array(o);let s=o,a=o;for(let u=0;u<o;u++)Zw(t,()=>{const c=Ee(e[u],t);let l=!1;c.subscribe(Ce(r,d=>{i[u]=d,l||(l=!0,a--),a||r.next(n(i.slice()))},()=>{--s||r.complete()}))},r)},r)}}(r,t,o?s=>Ww(o,s):_n));return n?i.pipe(qw(n)):i}function Zw(e,t,n){e?nn(n,e,t):t()}const ru=So(e=>function(){e(this),this.name="EmptyError",this.message="no elements in sequence"});function yf(...e){return function Ck(){return pr(1)}()(Ee(e,To(e)))}function Yw(e){return new he(t=>{rt(e()).subscribe(t)})}function ki(e,t){const n=te(e)?e:()=>e,r=o=>o.error(n());return new he(t?o=>t.schedule(r,0,o):r)}function vf(){return _e((e,t)=>{let n=null;e._refCount++;const r=Ce(t,void 0,void 0,void 0,()=>{if(!e||e._refCount<=0||0<--e._refCount)return void(n=null);const o=e._connection,i=n;n=null,o&&(!i||o===i)&&o.unsubscribe(),t.unsubscribe()});e.subscribe(r),r.closed||(n=e.connect())})}class Qw extends he{constructor(t,n){super(),this.source=t,this.subjectFactory=n,this._subject=null,this._refCount=0,this._connection=null,vh(t)&&(this.lift=t.lift)}_subscribe(t){return this.getSubject().subscribe(t)}getSubject(){const t=this._subject;return(!t||t.isStopped)&&(this._subject=this.subjectFactory()),this._subject}_teardown(){this._refCount=0;const{_connection:t}=this;this._subject=this._connection=null,t?.unsubscribe()}connect(){let t=this._connection;if(!t){t=this._connection=new Ze;const n=this.getSubject();t.add(this.source.subscribe(Ce(n,void 0,()=>{this._teardown(),n.complete()},r=>{this._teardown(),n.error(r)},()=>this._teardown()))),t.closed&&(this._connection=null,t=Ze.EMPTY)}return t}refCount(){return vf()(this)}}function go(e){return e<=0?()=>jt:_e((t,n)=>{let r=0;t.subscribe(Ce(n,o=>{++r<=e&&(n.next(o),e<=r&&n.complete())}))})}function ou(e){return _e((t,n)=>{let r=!1;t.subscribe(Ce(n,o=>{r=!0,n.next(o)},()=>{r||n.next(e),n.complete()}))})}function Xw(e=_k){return _e((t,n)=>{let r=!1;t.subscribe(Ce(n,o=>{r=!0,n.next(o)},()=>r?n.complete():n.error(e())))})}function _k(){return new ru}function sr(e,t){const n=arguments.length>=2;return r=>r.pipe(e?Dn((o,i)=>e(o,i,r)):_n,go(1),n?ou(t):Xw(()=>new ru))}function Le(e,t,n){const r=te(e)||t||n?{next:e,error:t,complete:n}:e;return r?_e((o,i)=>{var s;null===(s=r.subscribe)||void 0===s||s.call(r);let a=!0;o.subscribe(Ce(i,u=>{var c;null===(c=r.next)||void 0===c||c.call(r,u),i.next(u)},()=>{var u;a=!1,null===(u=r.complete)||void 0===u||u.call(r),i.complete()},u=>{var c;a=!1,null===(c=r.error)||void 0===c||c.call(r,u),i.error(u)},()=>{var u,c;a&&(null===(u=r.unsubscribe)||void 0===u||u.call(r)),null===(c=r.finalize)||void 0===c||c.call(r)}))}):_n}function ar(e){return _e((t,n)=>{let i,r=null,o=!1;r=t.subscribe(Ce(n,void 0,void 0,s=>{i=rt(e(s,ar(e)(t))),r?(r.unsubscribe(),r=null,i.subscribe(n)):o=!0})),o&&(r.unsubscribe(),r=null,i.subscribe(n))})}function Df(e){return e<=0?()=>jt:_e((t,n)=>{let r=[];t.subscribe(Ce(n,o=>{r.push(o),e<r.length&&r.shift()},()=>{for(const o of r)n.next(o);n.complete()},void 0,()=>{r=null}))})}const B="primary",Li=Symbol("RouteTitle");class Ak{constructor(t){this.params=t||{}}has(t){return Object.prototype.hasOwnProperty.call(this.params,t)}get(t){if(this.has(t)){const n=this.params[t];return Array.isArray(n)?n[0]:n}return null}getAll(t){if(this.has(t)){const n=this.params[t];return Array.isArray(n)?n:[n]}return[]}get keys(){return Object.keys(this.params)}}function mo(e){return new Ak(e)}function Tk(e,t,n){const r=n.path.split("/");if(r.length>e.length||"full"===n.pathMatch&&(t.hasChildren()||r.length<e.length))return null;const o={};for(let i=0;i<r.length;i++){const s=r[i],a=e[i];if(s.startsWith(":"))o[s.substring(1)]=a;else if(s!==a.path)return null}return{consumed:e.slice(0,r.length),posParams:o}}function Jt(e,t){const n=e?Object.keys(e):void 0,r=t?Object.keys(t):void 0;if(!n||!r||n.length!=r.length)return!1;let o;for(let i=0;i<n.length;i++)if(o=n[i],!Jw(e[o],t[o]))return!1;return!0}function Jw(e,t){if(Array.isArray(e)&&Array.isArray(t)){if(e.length!==t.length)return!1;const n=[...e].sort(),r=[...t].sort();return n.every((o,i)=>r[i]===o)}return e===t}function Kw(e){return e.length>0?e[e.length-1]:null}function Vn(e){return function dk(e){return!!e&&(e instanceof he||te(e.lift)&&te(e.subscribe))}(e)?e:yi(e)?Ee(Promise.resolve(e)):T(e)}const Rk={exact:function n_(e,t,n){if(!ur(e.segments,t.segments)||!iu(e.segments,t.segments,n)||e.numberOfChildren!==t.numberOfChildren)return!1;for(const r in t.children)if(!e.children[r]||!n_(e.children[r],t.children[r],n))return!1;return!0},subset:r_},e_={exact:function xk(e,t){return Jt(e,t)},subset:function Ok(e,t){return Object.keys(t).length<=Object.keys(e).length&&Object.keys(t).every(n=>Jw(e[n],t[n]))},ignored:()=>!0};function t_(e,t,n){return Rk[n.paths](e.root,t.root,n.matrixParams)&&e_[n.queryParams](e.queryParams,t.queryParams)&&!("exact"===n.fragment&&e.fragment!==t.fragment)}function r_(e,t,n){return o_(e,t,t.segments,n)}function o_(e,t,n,r){if(e.segments.length>n.length){const o=e.segments.slice(0,n.length);return!(!ur(o,n)||t.hasChildren()||!iu(o,n,r))}if(e.segments.length===n.length){if(!ur(e.segments,n)||!iu(e.segments,n,r))return!1;for(const o in t.children)if(!e.children[o]||!r_(e.children[o],t.children[o],r))return!1;return!0}{const o=n.slice(0,e.segments.length),i=n.slice(e.segments.length);return!!(ur(e.segments,o)&&iu(e.segments,o,r)&&e.children[B])&&o_(e.children[B],t,i,r)}}function iu(e,t,n){return t.every((r,o)=>e_[n](e[o].parameters,r.parameters))}class yo{constructor(t=new ee([],{}),n={},r=null){this.root=t,this.queryParams=n,this.fragment=r}get queryParamMap(){return this._queryParamMap||(this._queryParamMap=mo(this.queryParams)),this._queryParamMap}toString(){return kk.serialize(this)}}class ee{constructor(t,n){this.segments=t,this.children=n,this.parent=null,Object.values(n).forEach(r=>r.parent=this)}hasChildren(){return this.numberOfChildren>0}get numberOfChildren(){return Object.keys(this.children).length}toString(){return su(this)}}class Vi{constructor(t,n){this.path=t,this.parameters=n}get parameterMap(){return this._parameterMap||(this._parameterMap=mo(this.parameters)),this._parameterMap}toString(){return a_(this)}}function ur(e,t){return e.length===t.length&&e.every((n,r)=>n.path===t[r].path)}let ji=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=S({token:e,factory:function(){return new Cf},providedIn:"root"})}}return e})();class Cf{parse(t){const n=new Wk(t);return new yo(n.parseRootSegment(),n.parseQueryParams(),n.parseFragment())}serialize(t){const n=`/${Bi(t.root,!0)}`,r=function jk(e){const t=Object.keys(e).map(n=>{const r=e[n];return Array.isArray(r)?r.map(o=>`${au(n)}=${au(o)}`).join("&"):`${au(n)}=${au(r)}`}).filter(n=>!!n);return t.length?`?${t.join("&")}`:""}(t.queryParams);return`${n}${r}${"string"==typeof t.fragment?`#${function Lk(e){return encodeURI(e)}(t.fragment)}`:""}`}}const kk=new Cf;function su(e){return e.segments.map(t=>a_(t)).join("/")}function Bi(e,t){if(!e.hasChildren())return su(e);if(t){const n=e.children[B]?Bi(e.children[B],!1):"",r=[];return Object.entries(e.children).forEach(([o,i])=>{o!==B&&r.push(`${o}:${Bi(i,!1)}`)}),r.length>0?`${n}(${r.join("//")})`:n}{const n=function Fk(e,t){let n=[];return Object.entries(e.children).forEach(([r,o])=>{r===B&&(n=n.concat(t(o,r)))}),Object.entries(e.children).forEach(([r,o])=>{r!==B&&(n=n.concat(t(o,r)))}),n}(e,(r,o)=>o===B?[Bi(e.children[B],!1)]:[`${o}:${Bi(r,!1)}`]);return 1===Object.keys(e.children).length&&null!=e.children[B]?`${su(e)}/${n[0]}`:`${su(e)}/(${n.join("//")})`}}function i_(e){return encodeURIComponent(e).replace(/%40/g,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",")}function au(e){return i_(e).replace(/%3B/gi,";")}function wf(e){return i_(e).replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/%26/gi,"&")}function uu(e){return decodeURIComponent(e)}function s_(e){return uu(e.replace(/\+/g,"%20"))}function a_(e){return`${wf(e.path)}${function Vk(e){return Object.keys(e).map(t=>`;${wf(t)}=${wf(e[t])}`).join("")}(e.parameters)}`}const Bk=/^[^\/()?;#]+/;function _f(e){const t=e.match(Bk);return t?t[0]:""}const Hk=/^[^\/()?;=#]+/,Uk=/^[^=?&#]+/,Gk=/^[^&#]+/;class Wk{constructor(t){this.url=t,this.remaining=t}parseRootSegment(){return this.consumeOptional("/"),""===this.remaining||this.peekStartsWith("?")||this.peekStartsWith("#")?new ee([],{}):new ee([],this.parseChildren())}parseQueryParams(){const t={};if(this.consumeOptional("?"))do{this.parseQueryParam(t)}while(this.consumeOptional("&"));return t}parseFragment(){return this.consumeOptional("#")?decodeURIComponent(this.remaining):null}parseChildren(){if(""===this.remaining)return{};this.consumeOptional("/");const t=[];for(this.peekStartsWith("(")||t.push(this.parseSegment());this.peekStartsWith("/")&&!this.peekStartsWith("//")&&!this.peekStartsWith("/(");)this.capture("/"),t.push(this.parseSegment());let n={};this.peekStartsWith("/(")&&(this.capture("/"),n=this.parseParens(!0));let r={};return this.peekStartsWith("(")&&(r=this.parseParens(!1)),(t.length>0||Object.keys(n).length>0)&&(r[B]=new ee(t,n)),r}parseSegment(){const t=_f(this.remaining);if(""===t&&this.peekStartsWith(";"))throw new C(4009,!1);return this.capture(t),new Vi(uu(t),this.parseMatrixParams())}parseMatrixParams(){const t={};for(;this.consumeOptional(";");)this.parseParam(t);return t}parseParam(t){const n=function $k(e){const t=e.match(Hk);return t?t[0]:""}(this.remaining);if(!n)return;this.capture(n);let r="";if(this.consumeOptional("=")){const o=_f(this.remaining);o&&(r=o,this.capture(r))}t[uu(n)]=uu(r)}parseQueryParam(t){const n=function zk(e){const t=e.match(Uk);return t?t[0]:""}(this.remaining);if(!n)return;this.capture(n);let r="";if(this.consumeOptional("=")){const s=function qk(e){const t=e.match(Gk);return t?t[0]:""}(this.remaining);s&&(r=s,this.capture(r))}const o=s_(n),i=s_(r);if(t.hasOwnProperty(o)){let s=t[o];Array.isArray(s)||(s=[s],t[o]=s),s.push(i)}else t[o]=i}parseParens(t){const n={};for(this.capture("(");!this.consumeOptional(")")&&this.remaining.length>0;){const r=_f(this.remaining),o=this.remaining[r.length];if("/"!==o&&")"!==o&&";"!==o)throw new C(4010,!1);let i;r.indexOf(":")>-1?(i=r.slice(0,r.indexOf(":")),this.capture(i),this.capture(":")):t&&(i=B);const s=this.parseChildren();n[i]=1===Object.keys(s).length?s[B]:new ee([],s),this.consumeOptional("//")}return n}peekStartsWith(t){return this.remaining.startsWith(t)}consumeOptional(t){return!!this.peekStartsWith(t)&&(this.remaining=this.remaining.substring(t.length),!0)}capture(t){if(!this.consumeOptional(t))throw new C(4011,!1)}}function u_(e){return e.segments.length>0?new ee([],{[B]:e}):e}function c_(e){const t={};for(const r of Object.keys(e.children)){const i=c_(e.children[r]);if(r===B&&0===i.segments.length&&i.hasChildren())for(const[s,a]of Object.entries(i.children))t[s]=a;else(i.segments.length>0||i.hasChildren())&&(t[r]=i)}return function Zk(e){if(1===e.numberOfChildren&&e.children[B]){const t=e.children[B];return new ee(e.segments.concat(t.segments),t.children)}return e}(new ee(e.segments,t))}function cr(e){return e instanceof yo}function l_(e){let t;const o=u_(function n(i){const s={};for(const u of i.children){const c=n(u);s[u.outlet]=c}const a=new ee(i.url,s);return i===e&&(t=a),a}(e.root));return t??o}function d_(e,t,n,r){let o=e;for(;o.parent;)o=o.parent;if(0===t.length)return Ef(o,o,o,n,r);const i=function Qk(e){if("string"==typeof e[0]&&1===e.length&&"/"===e[0])return new h_(!0,0,e);let t=0,n=!1;const r=e.reduce((o,i,s)=>{if("object"==typeof i&&null!=i){if(i.outlets){const a={};return Object.entries(i.outlets).forEach(([u,c])=>{a[u]="string"==typeof c?c.split("/"):c}),[...o,{outlets:a}]}if(i.segmentPath)return[...o,i.segmentPath]}return"string"!=typeof i?[...o,i]:0===s?(i.split("/").forEach((a,u)=>{0==u&&"."===a||(0==u&&""===a?n=!0:".."===a?t++:""!=a&&o.push(a))}),o):[...o,i]},[]);return new h_(n,t,r)}(t);if(i.toRoot())return Ef(o,o,new ee([],{}),n,r);const s=function Xk(e,t,n){if(e.isAbsolute)return new lu(t,!0,0);if(!n)return new lu(t,!1,NaN);if(null===n.parent)return new lu(n,!0,0);const r=cu(e.commands[0])?0:1;return function Jk(e,t,n){let r=e,o=t,i=n;for(;i>o;){if(i-=o,r=r.parent,!r)throw new C(4005,!1);o=r.segments.length}return new lu(r,!1,o-i)}(n,n.segments.length-1+r,e.numberOfDoubleDots)}(i,o,e),a=s.processChildren?$i(s.segmentGroup,s.index,i.commands):p_(s.segmentGroup,s.index,i.commands);return Ef(o,s.segmentGroup,a,n,r)}function cu(e){return"object"==typeof e&&null!=e&&!e.outlets&&!e.segmentPath}function Hi(e){return"object"==typeof e&&null!=e&&e.outlets}function Ef(e,t,n,r,o){let s,i={};r&&Object.entries(r).forEach(([u,c])=>{i[u]=Array.isArray(c)?c.map(l=>`${l}`):`${c}`}),s=e===t?n:f_(e,t,n);const a=u_(c_(s));return new yo(a,i,o)}function f_(e,t,n){const r={};return Object.entries(e.children).forEach(([o,i])=>{r[o]=i===t?n:f_(i,t,n)}),new ee(e.segments,r)}class h_{constructor(t,n,r){if(this.isAbsolute=t,this.numberOfDoubleDots=n,this.commands=r,t&&r.length>0&&cu(r[0]))throw new C(4003,!1);const o=r.find(Hi);if(o&&o!==Kw(r))throw new C(4004,!1)}toRoot(){return this.isAbsolute&&1===this.commands.length&&"/"==this.commands[0]}}class lu{constructor(t,n,r){this.segmentGroup=t,this.processChildren=n,this.index=r}}function p_(e,t,n){if(e||(e=new ee([],{})),0===e.segments.length&&e.hasChildren())return $i(e,t,n);const r=function eL(e,t,n){let r=0,o=t;const i={match:!1,pathIndex:0,commandIndex:0};for(;o<e.segments.length;){if(r>=n.length)return i;const s=e.segments[o],a=n[r];if(Hi(a))break;const u=`${a}`,c=r<n.length-1?n[r+1]:null;if(o>0&&void 0===u)break;if(u&&c&&"object"==typeof c&&void 0===c.outlets){if(!m_(u,c,s))return i;r+=2}else{if(!m_(u,{},s))return i;r++}o++}return{match:!0,pathIndex:o,commandIndex:r}}(e,t,n),o=n.slice(r.commandIndex);if(r.match&&r.pathIndex<e.segments.length){const i=new ee(e.segments.slice(0,r.pathIndex),{});return i.children[B]=new ee(e.segments.slice(r.pathIndex),e.children),$i(i,0,o)}return r.match&&0===o.length?new ee(e.segments,{}):r.match&&!e.hasChildren()?bf(e,t,n):r.match?$i(e,0,o):bf(e,t,n)}function $i(e,t,n){if(0===n.length)return new ee(e.segments,{});{const r=function Kk(e){return Hi(e[0])?e[0].outlets:{[B]:e}}(n),o={};if(Object.keys(r).some(i=>i!==B)&&e.children[B]&&1===e.numberOfChildren&&0===e.children[B].segments.length){const i=$i(e.children[B],t,n);return new ee(e.segments,i.children)}return Object.entries(r).forEach(([i,s])=>{"string"==typeof s&&(s=[s]),null!==s&&(o[i]=p_(e.children[i],t,s))}),Object.entries(e.children).forEach(([i,s])=>{void 0===r[i]&&(o[i]=s)}),new ee(e.segments,o)}}function bf(e,t,n){const r=e.segments.slice(0,t);let o=0;for(;o<n.length;){const i=n[o];if(Hi(i)){const u=tL(i.outlets);return new ee(r,u)}if(0===o&&cu(n[0])){r.push(new Vi(e.segments[t].path,g_(n[0]))),o++;continue}const s=Hi(i)?i.outlets[B]:`${i}`,a=o<n.length-1?n[o+1]:null;s&&a&&cu(a)?(r.push(new Vi(s,g_(a))),o+=2):(r.push(new Vi(s,{})),o++)}return new ee(r,{})}function tL(e){const t={};return Object.entries(e).forEach(([n,r])=>{"string"==typeof r&&(r=[r]),null!==r&&(t[n]=bf(new ee([],{}),0,r))}),t}function g_(e){const t={};return Object.entries(e).forEach(([n,r])=>t[n]=`${r}`),t}function m_(e,t,n){return e==n.path&&Jt(t,n.parameters)}const Ui="imperative";class Kt{constructor(t,n){this.id=t,this.url=n}}class du extends Kt{constructor(t,n,r="imperative",o=null){super(t,n),this.type=0,this.navigationTrigger=r,this.restoredState=o}toString(){return`NavigationStart(id: ${this.id}, url: '${this.url}')`}}class jn extends Kt{constructor(t,n,r){super(t,n),this.urlAfterRedirects=r,this.type=1}toString(){return`NavigationEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}')`}}class zi extends Kt{constructor(t,n,r,o){super(t,n),this.reason=r,this.code=o,this.type=2}toString(){return`NavigationCancel(id: ${this.id}, url: '${this.url}')`}}class vo extends Kt{constructor(t,n,r,o){super(t,n),this.reason=r,this.code=o,this.type=16}}class fu extends Kt{constructor(t,n,r,o){super(t,n),this.error=r,this.target=o,this.type=3}toString(){return`NavigationError(id: ${this.id}, url: '${this.url}', error: ${this.error})`}}class y_ extends Kt{constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o,this.type=4}toString(){return`RoutesRecognized(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}}class nL extends Kt{constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o,this.type=7}toString(){return`GuardsCheckStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}}class rL extends Kt{constructor(t,n,r,o,i){super(t,n),this.urlAfterRedirects=r,this.state=o,this.shouldActivate=i,this.type=8}toString(){return`GuardsCheckEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state}, shouldActivate: ${this.shouldActivate})`}}class oL extends Kt{constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o,this.type=5}toString(){return`ResolveStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}}class iL extends Kt{constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o,this.type=6}toString(){return`ResolveEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}}class sL{constructor(t){this.route=t,this.type=9}toString(){return`RouteConfigLoadStart(path: ${this.route.path})`}}class aL{constructor(t){this.route=t,this.type=10}toString(){return`RouteConfigLoadEnd(path: ${this.route.path})`}}class uL{constructor(t){this.snapshot=t,this.type=11}toString(){return`ChildActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}}class cL{constructor(t){this.snapshot=t,this.type=12}toString(){return`ChildActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}}class lL{constructor(t){this.snapshot=t,this.type=13}toString(){return`ActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}}class dL{constructor(t){this.snapshot=t,this.type=14}toString(){return`ActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}}class v_{constructor(t,n,r){this.routerEvent=t,this.position=n,this.anchor=r,this.type=15}toString(){return`Scroll(anchor: '${this.anchor}', position: '${this.position?`${this.position[0]}, ${this.position[1]}`:null}')`}}class Mf{}class If{constructor(t){this.url=t}}class fL{constructor(){this.outlet=null,this.route=null,this.injector=null,this.children=new Gi,this.attachRef=null}}let Gi=(()=>{class e{constructor(){this.contexts=new Map}onChildOutletCreated(n,r){const o=this.getOrCreateContext(n);o.outlet=r,this.contexts.set(n,o)}onChildOutletDestroyed(n){const r=this.getContext(n);r&&(r.outlet=null,r.attachRef=null)}onOutletDeactivated(){const n=this.contexts;return this.contexts=new Map,n}onOutletReAttached(n){this.contexts=n}getOrCreateContext(n){let r=this.getContext(n);return r||(r=new fL,this.contexts.set(n,r)),r}getContext(n){return this.contexts.get(n)||null}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=S({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();class D_{constructor(t){this._root=t}get root(){return this._root.value}parent(t){const n=this.pathFromRoot(t);return n.length>1?n[n.length-2]:null}children(t){const n=Sf(t,this._root);return n?n.children.map(r=>r.value):[]}firstChild(t){const n=Sf(t,this._root);return n&&n.children.length>0?n.children[0].value:null}siblings(t){const n=Af(t,this._root);return n.length<2?[]:n[n.length-2].children.map(o=>o.value).filter(o=>o!==t)}pathFromRoot(t){return Af(t,this._root).map(n=>n.value)}}function Sf(e,t){if(e===t.value)return t;for(const n of t.children){const r=Sf(e,n);if(r)return r}return null}function Af(e,t){if(e===t.value)return[t];for(const n of t.children){const r=Af(e,n);if(r.length)return r.unshift(t),r}return[]}class Cn{constructor(t,n){this.value=t,this.children=n}toString(){return`TreeNode(${this.value})`}}function Do(e){const t={};return e&&e.children.forEach(n=>t[n.value.outlet]=n),t}class C_ extends D_{constructor(t,n){super(t),this.snapshot=n,Tf(this,t)}toString(){return this.snapshot.toString()}}function w_(e,t){const n=function hL(e,t){const s=new hu([],{},{},"",{},B,t,null,{});return new E_("",new Cn(s,[]))}(0,t),r=new mt([new Vi("",{})]),o=new mt({}),i=new mt({}),s=new mt({}),a=new mt(""),u=new Co(r,o,s,a,i,B,t,n.root);return u.snapshot=n.root,new C_(new Cn(u,[]),n)}class Co{constructor(t,n,r,o,i,s,a,u){this.urlSubject=t,this.paramsSubject=n,this.queryParamsSubject=r,this.fragmentSubject=o,this.dataSubject=i,this.outlet=s,this.component=a,this._futureSnapshot=u,this.title=this.dataSubject?.pipe(Z(c=>c[Li]))??T(void 0),this.url=t,this.params=n,this.queryParams=r,this.fragment=o,this.data=i}get routeConfig(){return this._futureSnapshot.routeConfig}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap||(this._paramMap=this.params.pipe(Z(t=>mo(t)))),this._paramMap}get queryParamMap(){return this._queryParamMap||(this._queryParamMap=this.queryParams.pipe(Z(t=>mo(t)))),this._queryParamMap}toString(){return this.snapshot?this.snapshot.toString():`Future(${this._futureSnapshot})`}}function __(e,t="emptyOnly"){const n=e.pathFromRoot;let r=0;if("always"!==t)for(r=n.length-1;r>=1;){const o=n[r],i=n[r-1];if(o.routeConfig&&""===o.routeConfig.path)r--;else{if(i.component)break;r--}}return function pL(e){return e.reduce((t,n)=>({params:{...t.params,...n.params},data:{...t.data,...n.data},resolve:{...n.data,...t.resolve,...n.routeConfig?.data,...n._resolvedData}}),{params:{},data:{},resolve:{}})}(n.slice(r))}class hu{get title(){return this.data?.[Li]}constructor(t,n,r,o,i,s,a,u,c){this.url=t,this.params=n,this.queryParams=r,this.fragment=o,this.data=i,this.outlet=s,this.component=a,this.routeConfig=u,this._resolve=c}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap||(this._paramMap=mo(this.params)),this._paramMap}get queryParamMap(){return this._queryParamMap||(this._queryParamMap=mo(this.queryParams)),this._queryParamMap}toString(){return`Route(url:'${this.url.map(r=>r.toString()).join("/")}', path:'${this.routeConfig?this.routeConfig.path:""}')`}}class E_ extends D_{constructor(t,n){super(n),this.url=t,Tf(this,n)}toString(){return b_(this._root)}}function Tf(e,t){t.value._routerState=e,t.children.forEach(n=>Tf(e,n))}function b_(e){const t=e.children.length>0?` { ${e.children.map(b_).join(", ")} } `:"";return`${e.value}${t}`}function Nf(e){if(e.snapshot){const t=e.snapshot,n=e._futureSnapshot;e.snapshot=n,Jt(t.queryParams,n.queryParams)||e.queryParamsSubject.next(n.queryParams),t.fragment!==n.fragment&&e.fragmentSubject.next(n.fragment),Jt(t.params,n.params)||e.paramsSubject.next(n.params),function Nk(e,t){if(e.length!==t.length)return!1;for(let n=0;n<e.length;++n)if(!Jt(e[n],t[n]))return!1;return!0}(t.url,n.url)||e.urlSubject.next(n.url),Jt(t.data,n.data)||e.dataSubject.next(n.data)}else e.snapshot=e._futureSnapshot,e.dataSubject.next(e._futureSnapshot.data)}function Rf(e,t){const n=Jt(e.params,t.params)&&function Pk(e,t){return ur(e,t)&&e.every((n,r)=>Jt(n.parameters,t[r].parameters))}(e.url,t.url);return n&&!(!e.parent!=!t.parent)&&(!e.parent||Rf(e.parent,t.parent))}let M_=(()=>{class e{constructor(){this.activated=null,this._activatedRoute=null,this.name=B,this.activateEvents=new ge,this.deactivateEvents=new ge,this.attachEvents=new ge,this.detachEvents=new ge,this.parentContexts=E(Gi),this.location=E(Ft),this.changeDetector=E(Fa),this.environmentInjector=E(ct),this.inputBinder=E(pu,{optional:!0}),this.supportsBindingToComponentInputs=!0}get activatedComponentRef(){return this.activated}ngOnChanges(n){if(n.name){const{firstChange:r,previousValue:o}=n.name;if(r)return;this.isTrackedInParentContexts(o)&&(this.deactivate(),this.parentContexts.onChildOutletDestroyed(o)),this.initializeOutletWithName()}}ngOnDestroy(){this.isTrackedInParentContexts(this.name)&&this.parentContexts.onChildOutletDestroyed(this.name),this.inputBinder?.unsubscribeFromRouteData(this)}isTrackedInParentContexts(n){return this.parentContexts.getContext(n)?.outlet===this}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(this.parentContexts.onChildOutletCreated(this.name,this),this.activated)return;const n=this.parentContexts.getContext(this.name);n?.route&&(n.attachRef?this.attach(n.attachRef,n.route):this.activateWith(n.route,n.injector))}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new C(4012,!1);return this.activated.instance}get activatedRoute(){if(!this.activated)throw new C(4012,!1);return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){if(!this.activated)throw new C(4012,!1);this.location.detach();const n=this.activated;return this.activated=null,this._activatedRoute=null,this.detachEvents.emit(n.instance),n}attach(n,r){this.activated=n,this._activatedRoute=r,this.location.insert(n.hostView),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.attachEvents.emit(n.instance)}deactivate(){if(this.activated){const n=this.component;this.activated.destroy(),this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(n)}}activateWith(n,r){if(this.isActivated)throw new C(4013,!1);this._activatedRoute=n;const o=this.location,s=n.snapshot.component,a=this.parentContexts.getOrCreateContext(this.name).children,u=new gL(n,a,o.injector);this.activated=o.createComponent(s,{index:o.length,injector:u,environmentInjector:r??this.environmentInjector}),this.changeDetector.markForCheck(),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.activateEvents.emit(this.activated.instance)}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275dir=O({type:e,selectors:[["router-outlet"]],inputs:{name:"name"},outputs:{activateEvents:"activate",deactivateEvents:"deactivate",attachEvents:"attach",detachEvents:"detach"},exportAs:["outlet"],standalone:!0,features:[wt]})}}return e})();class gL{constructor(t,n,r){this.route=t,this.childContexts=n,this.parent=r}get(t,n){return t===Co?this.route:t===Gi?this.childContexts:this.parent.get(t,n)}}const pu=new M("");let I_=(()=>{class e{constructor(){this.outletDataSubscriptions=new Map}bindActivatedRouteToOutletComponent(n){this.unsubscribeFromRouteData(n),this.subscribeToRouteData(n)}unsubscribeFromRouteData(n){this.outletDataSubscriptions.get(n)?.unsubscribe(),this.outletDataSubscriptions.delete(n)}subscribeToRouteData(n){const{activatedRoute:r}=n,o=mf([r.queryParams,r.params,r.data]).pipe(yt(([i,s,a],u)=>(a={...i,...s,...a},0===u?T(a):Promise.resolve(a)))).subscribe(i=>{if(!n.isActivated||!n.activatedComponentRef||n.activatedRoute!==r||null===r.component)return void this.unsubscribeFromRouteData(n);const s=function pP(e){const t=G(e);if(!t)return null;const n=new di(t);return{get selector(){return n.selector},get type(){return n.componentType},get inputs(){return n.inputs},get outputs(){return n.outputs},get ngContentSelectors(){return n.ngContentSelectors},get isStandalone(){return t.standalone},get isSignal(){return t.signals}}}(r.component);if(s)for(const{templateName:a}of s.inputs)n.activatedComponentRef.setInput(a,i[a]);else this.unsubscribeFromRouteData(n)});this.outletDataSubscriptions.set(n,o)}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=S({token:e,factory:e.\u0275fac})}}return e})();function qi(e,t,n){if(n&&e.shouldReuseRoute(t.value,n.value.snapshot)){const r=n.value;r._futureSnapshot=t.value;const o=function yL(e,t,n){return t.children.map(r=>{for(const o of n.children)if(e.shouldReuseRoute(r.value,o.value.snapshot))return qi(e,r,o);return qi(e,r)})}(e,t,n);return new Cn(r,o)}{if(e.shouldAttach(t.value)){const i=e.retrieve(t.value);if(null!==i){const s=i.route;return s.value._futureSnapshot=t.value,s.children=t.children.map(a=>qi(e,a)),s}}const r=function vL(e){return new Co(new mt(e.url),new mt(e.params),new mt(e.queryParams),new mt(e.fragment),new mt(e.data),e.outlet,e.component,e)}(t.value),o=t.children.map(i=>qi(e,i));return new Cn(r,o)}}const xf="ngNavigationCancelingError";function S_(e,t){const{redirectTo:n,navigationBehaviorOptions:r}=cr(t)?{redirectTo:t,navigationBehaviorOptions:void 0}:t,o=A_(!1,0,t);return o.url=n,o.navigationBehaviorOptions=r,o}function A_(e,t,n){const r=new Error("NavigationCancelingError: "+(e||""));return r[xf]=!0,r.cancellationCode=t,n&&(r.url=n),r}function T_(e){return e&&e[xf]}let N_=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275cmp=ms({type:e,selectors:[["ng-component"]],standalone:!0,features:[iD],decls:1,vars:0,template:function(r,o){1&r&&Ca(0,"router-outlet")},dependencies:[M_],encapsulation:2})}}return e})();function Of(e){const t=e.children&&e.children.map(Of),n=t?{...e,children:t}:{...e};return!n.component&&!n.loadComponent&&(t||n.loadChildren)&&n.outlet&&n.outlet!==B&&(n.component=N_),n}function Vt(e){return e.outlet||B}function Wi(e){if(!e)return null;if(e.routeConfig?._injector)return e.routeConfig._injector;for(let t=e.parent;t;t=t.parent){const n=t.routeConfig;if(n?._loadedInjector)return n._loadedInjector;if(n?._injector)return n._injector}return null}class IL{constructor(t,n,r,o,i){this.routeReuseStrategy=t,this.futureState=n,this.currState=r,this.forwardEvent=o,this.inputBindingEnabled=i}activate(t){const n=this.futureState._root,r=this.currState?this.currState._root:null;this.deactivateChildRoutes(n,r,t),Nf(this.futureState.root),this.activateChildRoutes(n,r,t)}deactivateChildRoutes(t,n,r){const o=Do(n);t.children.forEach(i=>{const s=i.value.outlet;this.deactivateRoutes(i,o[s],r),delete o[s]}),Object.values(o).forEach(i=>{this.deactivateRouteAndItsChildren(i,r)})}deactivateRoutes(t,n,r){const o=t.value,i=n?n.value:null;if(o===i)if(o.component){const s=r.getContext(o.outlet);s&&this.deactivateChildRoutes(t,n,s.children)}else this.deactivateChildRoutes(t,n,r);else i&&this.deactivateRouteAndItsChildren(n,r)}deactivateRouteAndItsChildren(t,n){t.value.component&&this.routeReuseStrategy.shouldDetach(t.value.snapshot)?this.detachAndStoreRouteSubtree(t,n):this.deactivateRouteAndOutlet(t,n)}detachAndStoreRouteSubtree(t,n){const r=n.getContext(t.value.outlet),o=r&&t.value.component?r.children:n,i=Do(t);for(const s of Object.keys(i))this.deactivateRouteAndItsChildren(i[s],o);if(r&&r.outlet){const s=r.outlet.detach(),a=r.children.onOutletDeactivated();this.routeReuseStrategy.store(t.value.snapshot,{componentRef:s,route:t,contexts:a})}}deactivateRouteAndOutlet(t,n){const r=n.getContext(t.value.outlet),o=r&&t.value.component?r.children:n,i=Do(t);for(const s of Object.keys(i))this.deactivateRouteAndItsChildren(i[s],o);r&&(r.outlet&&(r.outlet.deactivate(),r.children.onOutletDeactivated()),r.attachRef=null,r.route=null)}activateChildRoutes(t,n,r){const o=Do(n);t.children.forEach(i=>{this.activateRoutes(i,o[i.value.outlet],r),this.forwardEvent(new dL(i.value.snapshot))}),t.children.length&&this.forwardEvent(new cL(t.value.snapshot))}activateRoutes(t,n,r){const o=t.value,i=n?n.value:null;if(Nf(o),o===i)if(o.component){const s=r.getOrCreateContext(o.outlet);this.activateChildRoutes(t,n,s.children)}else this.activateChildRoutes(t,n,r);else if(o.component){const s=r.getOrCreateContext(o.outlet);if(this.routeReuseStrategy.shouldAttach(o.snapshot)){const a=this.routeReuseStrategy.retrieve(o.snapshot);this.routeReuseStrategy.store(o.snapshot,null),s.children.onOutletReAttached(a.contexts),s.attachRef=a.componentRef,s.route=a.route.value,s.outlet&&s.outlet.attach(a.componentRef,a.route.value),Nf(a.route.value),this.activateChildRoutes(t,null,s.children)}else{const a=Wi(o.snapshot);s.attachRef=null,s.route=o,s.injector=a,s.outlet&&s.outlet.activateWith(o,s.injector),this.activateChildRoutes(t,null,s.children)}}else this.activateChildRoutes(t,null,r)}}class R_{constructor(t){this.path=t,this.route=this.path[this.path.length-1]}}class gu{constructor(t,n){this.component=t,this.route=n}}function SL(e,t,n){const r=e._root;return Zi(r,t?t._root:null,n,[r.value])}function wo(e,t){const n=Symbol(),r=t.get(e,n);return r===n?"function"!=typeof e||function mM(e){return null!==cs(e)}(e)?t.get(e):e:r}function Zi(e,t,n,r,o={canDeactivateChecks:[],canActivateChecks:[]}){const i=Do(t);return e.children.forEach(s=>{(function TL(e,t,n,r,o={canDeactivateChecks:[],canActivateChecks:[]}){const i=e.value,s=t?t.value:null,a=n?n.getContext(e.value.outlet):null;if(s&&i.routeConfig===s.routeConfig){const u=function NL(e,t,n){if("function"==typeof n)return n(e,t);switch(n){case"pathParamsChange":return!ur(e.url,t.url);case"pathParamsOrQueryParamsChange":return!ur(e.url,t.url)||!Jt(e.queryParams,t.queryParams);case"always":return!0;case"paramsOrQueryParamsChange":return!Rf(e,t)||!Jt(e.queryParams,t.queryParams);default:return!Rf(e,t)}}(s,i,i.routeConfig.runGuardsAndResolvers);u?o.canActivateChecks.push(new R_(r)):(i.data=s.data,i._resolvedData=s._resolvedData),Zi(e,t,i.component?a?a.children:null:n,r,o),u&&a&&a.outlet&&a.outlet.isActivated&&o.canDeactivateChecks.push(new gu(a.outlet.component,s))}else s&&Yi(t,a,o),o.canActivateChecks.push(new R_(r)),Zi(e,null,i.component?a?a.children:null:n,r,o)})(s,i[s.value.outlet],n,r.concat([s.value]),o),delete i[s.value.outlet]}),Object.entries(i).forEach(([s,a])=>Yi(a,n.getContext(s),o)),o}function Yi(e,t,n){const r=Do(e),o=e.value;Object.entries(r).forEach(([i,s])=>{Yi(s,o.component?t?t.children.getContext(i):null:t,n)}),n.canDeactivateChecks.push(new gu(o.component&&t&&t.outlet&&t.outlet.isActivated?t.outlet.component:null,o))}function Qi(e){return"function"==typeof e}function x_(e){return e instanceof ru||"EmptyError"===e?.name}const mu=Symbol("INITIAL_VALUE");function _o(){return yt(e=>mf(e.map(t=>t.pipe(go(1),function wk(...e){const t=To(e);return _e((n,r)=>{(t?yf(e,n,t):yf(e,n)).subscribe(r)})}(mu)))).pipe(Z(t=>{for(const n of t)if(!0!==n){if(n===mu)return mu;if(!1===n||n instanceof yo)return n}return!0}),Dn(t=>t!==mu),go(1)))}function O_(e){return function pb(...e){return gh(e)}(Le(t=>{if(cr(t))throw S_(0,t)}),Z(t=>!0===t))}class yu{constructor(t){this.segmentGroup=t||null}}class P_{constructor(t){this.urlTree=t}}function Eo(e){return ki(new yu(e))}function F_(e){return ki(new P_(e))}class QL{constructor(t,n){this.urlSerializer=t,this.urlTree=n}noMatchError(t){return new C(4002,!1)}lineralizeSegments(t,n){let r=[],o=n.root;for(;;){if(r=r.concat(o.segments),0===o.numberOfChildren)return T(r);if(o.numberOfChildren>1||!o.children[B])return ki(new C(4e3,!1));o=o.children[B]}}applyRedirectCommands(t,n,r){return this.applyRedirectCreateUrlTree(n,this.urlSerializer.parse(n),t,r)}applyRedirectCreateUrlTree(t,n,r,o){const i=this.createSegmentGroup(t,n.root,r,o);return new yo(i,this.createQueryParams(n.queryParams,this.urlTree.queryParams),n.fragment)}createQueryParams(t,n){const r={};return Object.entries(t).forEach(([o,i])=>{if("string"==typeof i&&i.startsWith(":")){const a=i.substring(1);r[o]=n[a]}else r[o]=i}),r}createSegmentGroup(t,n,r,o){const i=this.createSegments(t,n.segments,r,o);let s={};return Object.entries(n.children).forEach(([a,u])=>{s[a]=this.createSegmentGroup(t,u,r,o)}),new ee(i,s)}createSegments(t,n,r,o){return n.map(i=>i.path.startsWith(":")?this.findPosParam(t,i,o):this.findOrReturn(i,r))}findPosParam(t,n,r){const o=r[n.path.substring(1)];if(!o)throw new C(4001,!1);return o}findOrReturn(t,n){let r=0;for(const o of n){if(o.path===t.path)return n.splice(r),o;r++}return t}}const Pf={matched:!1,consumedSegments:[],remainingSegments:[],parameters:{},positionalParamSegments:{}};function XL(e,t,n,r,o){const i=Ff(e,t,n);return i.matched?(r=function CL(e,t){return e.providers&&!e._injector&&(e._injector=ad(e.providers,t,`Route: ${e.path}`)),e._injector??t}(t,r),function WL(e,t,n,r){const o=t.canMatch;return o&&0!==o.length?T(o.map(s=>{const a=wo(s,e);return Vn(function kL(e){return e&&Qi(e.canMatch)}(a)?a.canMatch(t,n):e.runInContext(()=>a(t,n)))})).pipe(_o(),O_()):T(!0)}(r,t,n).pipe(Z(s=>!0===s?i:{...Pf}))):T(i)}function Ff(e,t,n){if(""===t.path)return"full"===t.pathMatch&&(e.hasChildren()||n.length>0)?{...Pf}:{matched:!0,consumedSegments:[],remainingSegments:n,parameters:{},positionalParamSegments:{}};const o=(t.matcher||Tk)(n,e,t);if(!o)return{...Pf};const i={};Object.entries(o.posParams??{}).forEach(([a,u])=>{i[a]=u.path});const s=o.consumed.length>0?{...i,...o.consumed[o.consumed.length-1].parameters}:i;return{matched:!0,consumedSegments:o.consumed,remainingSegments:n.slice(o.consumed.length),parameters:s,positionalParamSegments:o.posParams??{}}}function k_(e,t,n,r){return n.length>0&&function eV(e,t,n){return n.some(r=>vu(e,t,r)&&Vt(r)!==B)}(e,n,r)?{segmentGroup:new ee(t,KL(r,new ee(n,e.children))),slicedSegments:[]}:0===n.length&&function tV(e,t,n){return n.some(r=>vu(e,t,r))}(e,n,r)?{segmentGroup:new ee(e.segments,JL(e,0,n,r,e.children)),slicedSegments:n}:{segmentGroup:new ee(e.segments,e.children),slicedSegments:n}}function JL(e,t,n,r,o){const i={};for(const s of r)if(vu(e,n,s)&&!o[Vt(s)]){const a=new ee([],{});i[Vt(s)]=a}return{...o,...i}}function KL(e,t){const n={};n[B]=t;for(const r of e)if(""===r.path&&Vt(r)!==B){const o=new ee([],{});n[Vt(r)]=o}return n}function vu(e,t,n){return(!(e.hasChildren()||t.length>0)||"full"!==n.pathMatch)&&""===n.path}class iV{constructor(t,n,r,o,i,s,a){this.injector=t,this.configLoader=n,this.rootComponentType=r,this.config=o,this.urlTree=i,this.paramsInheritanceStrategy=s,this.urlSerializer=a,this.allowRedirects=!0,this.applyRedirects=new QL(this.urlSerializer,this.urlTree)}noMatchError(t){return new C(4002,!1)}recognize(){const t=k_(this.urlTree.root,[],[],this.config).segmentGroup;return this.processSegmentGroup(this.injector,this.config,t,B).pipe(ar(n=>{if(n instanceof P_)return this.allowRedirects=!1,this.urlTree=n.urlTree,this.match(n.urlTree);throw n instanceof yu?this.noMatchError(n):n}),Z(n=>{const r=new hu([],Object.freeze({}),Object.freeze({...this.urlTree.queryParams}),this.urlTree.fragment,{},B,this.rootComponentType,null,{}),o=new Cn(r,n),i=new E_("",o),s=function Yk(e,t,n=null,r=null){return d_(l_(e),t,n,r)}(r,[],this.urlTree.queryParams,this.urlTree.fragment);return s.queryParams=this.urlTree.queryParams,i.url=this.urlSerializer.serialize(s),this.inheritParamsAndData(i._root),{state:i,tree:s}}))}match(t){return this.processSegmentGroup(this.injector,this.config,t.root,B).pipe(ar(r=>{throw r instanceof yu?this.noMatchError(r):r}))}inheritParamsAndData(t){const n=t.value,r=__(n,this.paramsInheritanceStrategy);n.params=Object.freeze(r.params),n.data=Object.freeze(r.data),t.children.forEach(o=>this.inheritParamsAndData(o))}processSegmentGroup(t,n,r,o){return 0===r.segments.length&&r.hasChildren()?this.processChildren(t,n,r):this.processSegment(t,n,r,r.segments,o,!0)}processChildren(t,n,r){const o=[];for(const i of Object.keys(r.children))"primary"===i?o.unshift(i):o.push(i);return Ee(o).pipe(fo(i=>{const s=r.children[i],a=function bL(e,t){const n=e.filter(r=>Vt(r)===t);return n.push(...e.filter(r=>Vt(r)!==t)),n}(n,i);return this.processSegmentGroup(t,a,s,i)}),function bk(e,t){return _e(function Ek(e,t,n,r,o){return(i,s)=>{let a=n,u=t,c=0;i.subscribe(Ce(s,l=>{const d=c++;u=a?e(u,l,d):(a=!0,l),r&&s.next(u)},o&&(()=>{a&&s.next(u),s.complete()})))}}(e,t,arguments.length>=2,!0))}((i,s)=>(i.push(...s),i)),ou(null),function Mk(e,t){const n=arguments.length>=2;return r=>r.pipe(e?Dn((o,i)=>e(o,i,r)):_n,Df(1),n?ou(t):Xw(()=>new ru))}(),Ae(i=>{if(null===i)return Eo(r);const s=L_(i);return function sV(e){e.sort((t,n)=>t.value.outlet===B?-1:n.value.outlet===B?1:t.value.outlet.localeCompare(n.value.outlet))}(s),T(s)}))}processSegment(t,n,r,o,i,s){return Ee(n).pipe(fo(a=>this.processSegmentAgainstRoute(a._injector??t,n,a,r,o,i,s).pipe(ar(u=>{if(u instanceof yu)return T(null);throw u}))),sr(a=>!!a),ar(a=>{if(x_(a))return function rV(e,t,n){return 0===t.length&&!e.children[n]}(r,o,i)?T([]):Eo(r);throw a}))}processSegmentAgainstRoute(t,n,r,o,i,s,a){return function nV(e,t,n,r){return!!(Vt(e)===r||r!==B&&vu(t,n,e))&&("**"===e.path||Ff(t,e,n).matched)}(r,o,i,s)?void 0===r.redirectTo?this.matchSegmentAgainstRoute(t,o,r,i,s,a):a&&this.allowRedirects?this.expandSegmentAgainstRouteUsingRedirect(t,o,n,r,i,s):Eo(o):Eo(o)}expandSegmentAgainstRouteUsingRedirect(t,n,r,o,i,s){return"**"===o.path?this.expandWildCardWithParamsAgainstRouteUsingRedirect(t,r,o,s):this.expandRegularSegmentAgainstRouteUsingRedirect(t,n,r,o,i,s)}expandWildCardWithParamsAgainstRouteUsingRedirect(t,n,r,o){const i=this.applyRedirects.applyRedirectCommands([],r.redirectTo,{});return r.redirectTo.startsWith("/")?F_(i):this.applyRedirects.lineralizeSegments(r,i).pipe(Ae(s=>{const a=new ee(s,{});return this.processSegment(t,n,a,s,o,!1)}))}expandRegularSegmentAgainstRouteUsingRedirect(t,n,r,o,i,s){const{matched:a,consumedSegments:u,remainingSegments:c,positionalParamSegments:l}=Ff(n,o,i);if(!a)return Eo(n);const d=this.applyRedirects.applyRedirectCommands(u,o.redirectTo,l);return o.redirectTo.startsWith("/")?F_(d):this.applyRedirects.lineralizeSegments(o,d).pipe(Ae(f=>this.processSegment(t,r,n,f.concat(c),s,!1)))}matchSegmentAgainstRoute(t,n,r,o,i,s){let a;if("**"===r.path){const u=o.length>0?Kw(o).parameters:{};a=T({snapshot:new hu(o,u,Object.freeze({...this.urlTree.queryParams}),this.urlTree.fragment,V_(r),Vt(r),r.component??r._loadedComponent??null,r,j_(r)),consumedSegments:[],remainingSegments:[]}),n.children={}}else a=XL(n,r,o,t).pipe(Z(({matched:u,consumedSegments:c,remainingSegments:l,parameters:d})=>u?{snapshot:new hu(c,d,Object.freeze({...this.urlTree.queryParams}),this.urlTree.fragment,V_(r),Vt(r),r.component??r._loadedComponent??null,r,j_(r)),consumedSegments:c,remainingSegments:l}:null));return a.pipe(yt(u=>null===u?Eo(n):this.getChildConfig(t=r._injector??t,r,o).pipe(yt(({routes:c})=>{const l=r._loadedInjector??t,{snapshot:d,consumedSegments:f,remainingSegments:h}=u,{segmentGroup:p,slicedSegments:g}=k_(n,f,h,c);if(0===g.length&&p.hasChildren())return this.processChildren(l,c,p).pipe(Z(D=>null===D?null:[new Cn(d,D)]));if(0===c.length&&0===g.length)return T([new Cn(d,[])]);const y=Vt(r)===i;return this.processSegment(l,c,p,g,y?B:i,!0).pipe(Z(D=>[new Cn(d,D)]))}))))}getChildConfig(t,n,r){return n.children?T({routes:n.children,injector:t}):n.loadChildren?void 0!==n._loadedRoutes?T({routes:n._loadedRoutes,injector:n._loadedInjector}):function qL(e,t,n,r){const o=t.canLoad;return void 0===o||0===o.length?T(!0):T(o.map(s=>{const a=wo(s,e);return Vn(function xL(e){return e&&Qi(e.canLoad)}(a)?a.canLoad(t,n):e.runInContext(()=>a(t,n)))})).pipe(_o(),O_())}(t,n,r).pipe(Ae(o=>o?this.configLoader.loadChildren(t,n).pipe(Le(i=>{n._loadedRoutes=i.routes,n._loadedInjector=i.injector})):function YL(e){return ki(A_(!1,3))}())):T({routes:[],injector:t})}}function aV(e){const t=e.value.routeConfig;return t&&""===t.path}function L_(e){const t=[],n=new Set;for(const r of e){if(!aV(r)){t.push(r);continue}const o=t.find(i=>r.value.routeConfig===i.value.routeConfig);void 0!==o?(o.children.push(...r.children),n.add(o)):t.push(r)}for(const r of n){const o=L_(r.children);t.push(new Cn(r.value,o))}return t.filter(r=>!n.has(r))}function V_(e){return e.data||{}}function j_(e){return e.resolve||{}}function B_(e){return"string"==typeof e.title||null===e.title}function kf(e){return yt(t=>{const n=e(t);return n?Ee(n).pipe(Z(()=>t)):T(t)})}const bo=new M("ROUTES");let Lf=(()=>{class e{constructor(){this.componentLoaders=new WeakMap,this.childrenLoaders=new WeakMap,this.compiler=E(KD)}loadComponent(n){if(this.componentLoaders.get(n))return this.componentLoaders.get(n);if(n._loadedComponent)return T(n._loadedComponent);this.onLoadStartListener&&this.onLoadStartListener(n);const r=Vn(n.loadComponent()).pipe(Z(H_),Le(i=>{this.onLoadEndListener&&this.onLoadEndListener(n),n._loadedComponent=i}),Oi(()=>{this.componentLoaders.delete(n)})),o=new Qw(r,()=>new At).pipe(vf());return this.componentLoaders.set(n,o),o}loadChildren(n,r){if(this.childrenLoaders.get(r))return this.childrenLoaders.get(r);if(r._loadedRoutes)return T({routes:r._loadedRoutes,injector:r._loadedInjector});this.onLoadStartListener&&this.onLoadStartListener(r);const i=function pV(e,t,n,r){return Vn(e.loadChildren()).pipe(Z(H_),Ae(o=>o instanceof rD||Array.isArray(o)?T(o):Ee(t.compileModuleAsync(o))),Z(o=>{r&&r(e);let i,s,a=!1;return Array.isArray(o)?(s=o,!0):(i=o.create(n).injector,s=i.get(bo,[],{optional:!0,self:!0}).flat()),{routes:s.map(Of),injector:i}}))}(r,this.compiler,n,this.onLoadEndListener).pipe(Oi(()=>{this.childrenLoaders.delete(r)})),s=new Qw(i,()=>new At).pipe(vf());return this.childrenLoaders.set(r,s),s}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=S({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function H_(e){return function gV(e){return e&&"object"==typeof e&&"default"in e}(e)?e.default:e}let Du=(()=>{class e{get hasRequestedNavigation(){return 0!==this.navigationId}constructor(){this.currentNavigation=null,this.currentTransition=null,this.lastSuccessfulNavigation=null,this.events=new At,this.transitionAbortSubject=new At,this.configLoader=E(Lf),this.environmentInjector=E(ct),this.urlSerializer=E(ji),this.rootContexts=E(Gi),this.inputBindingEnabled=null!==E(pu,{optional:!0}),this.navigationId=0,this.afterPreactivation=()=>T(void 0),this.rootComponentType=null,this.configLoader.onLoadEndListener=o=>this.events.next(new aL(o)),this.configLoader.onLoadStartListener=o=>this.events.next(new sL(o))}complete(){this.transitions?.complete()}handleNavigationRequest(n){const r=++this.navigationId;this.transitions?.next({...this.transitions.value,...n,id:r})}setupNavigations(n,r,o){return this.transitions=new mt({id:0,currentUrlTree:r,currentRawUrl:r,currentBrowserUrl:r,extractedUrl:n.urlHandlingStrategy.extract(r),urlAfterRedirects:n.urlHandlingStrategy.extract(r),rawUrl:r,extras:{},resolve:null,reject:null,promise:Promise.resolve(!0),source:Ui,restoredState:null,currentSnapshot:o.snapshot,targetSnapshot:null,currentRouterState:o,targetRouterState:null,guards:{canActivateChecks:[],canDeactivateChecks:[]},guardsResult:null}),this.transitions.pipe(Dn(i=>0!==i.id),Z(i=>({...i,extractedUrl:n.urlHandlingStrategy.extract(i.rawUrl)})),yt(i=>{this.currentTransition=i;let s=!1,a=!1;return T(i).pipe(Le(u=>{this.currentNavigation={id:u.id,initialUrl:u.rawUrl,extractedUrl:u.extractedUrl,trigger:u.source,extras:u.extras,previousNavigation:this.lastSuccessfulNavigation?{...this.lastSuccessfulNavigation,previousNavigation:null}:null}}),yt(u=>{const c=u.currentBrowserUrl.toString(),l=!n.navigated||u.extractedUrl.toString()!==c||c!==u.currentUrlTree.toString();if(!l&&"reload"!==(u.extras.onSameUrlNavigation??n.onSameUrlNavigation)){const f="";return this.events.next(new vo(u.id,this.urlSerializer.serialize(u.rawUrl),f,0)),u.resolve(null),jt}if(n.urlHandlingStrategy.shouldProcessUrl(u.rawUrl))return T(u).pipe(yt(f=>{const h=this.transitions?.getValue();return this.events.next(new du(f.id,this.urlSerializer.serialize(f.extractedUrl),f.source,f.restoredState)),h!==this.transitions?.getValue()?jt:Promise.resolve(f)}),function uV(e,t,n,r,o,i){return Ae(s=>function oV(e,t,n,r,o,i,s="emptyOnly"){return new iV(e,t,n,r,o,s,i).recognize()}(e,t,n,r,s.extractedUrl,o,i).pipe(Z(({state:a,tree:u})=>({...s,targetSnapshot:a,urlAfterRedirects:u}))))}(this.environmentInjector,this.configLoader,this.rootComponentType,n.config,this.urlSerializer,n.paramsInheritanceStrategy),Le(f=>{i.targetSnapshot=f.targetSnapshot,i.urlAfterRedirects=f.urlAfterRedirects,this.currentNavigation={...this.currentNavigation,finalUrl:f.urlAfterRedirects};const h=new y_(f.id,this.urlSerializer.serialize(f.extractedUrl),this.urlSerializer.serialize(f.urlAfterRedirects),f.targetSnapshot);this.events.next(h)}));if(l&&n.urlHandlingStrategy.shouldProcessUrl(u.currentRawUrl)){const{id:f,extractedUrl:h,source:p,restoredState:g,extras:y}=u,D=new du(f,this.urlSerializer.serialize(h),p,g);this.events.next(D);const m=w_(0,this.rootComponentType).snapshot;return this.currentTransition=i={...u,targetSnapshot:m,urlAfterRedirects:h,extras:{...y,skipLocationChange:!1,replaceUrl:!1}},T(i)}{const f="";return this.events.next(new vo(u.id,this.urlSerializer.serialize(u.extractedUrl),f,1)),u.resolve(null),jt}}),Le(u=>{const c=new nL(u.id,this.urlSerializer.serialize(u.extractedUrl),this.urlSerializer.serialize(u.urlAfterRedirects),u.targetSnapshot);this.events.next(c)}),Z(u=>(this.currentTransition=i={...u,guards:SL(u.targetSnapshot,u.currentSnapshot,this.rootContexts)},i)),function VL(e,t){return Ae(n=>{const{targetSnapshot:r,currentSnapshot:o,guards:{canActivateChecks:i,canDeactivateChecks:s}}=n;return 0===s.length&&0===i.length?T({...n,guardsResult:!0}):function jL(e,t,n,r){return Ee(e).pipe(Ae(o=>function GL(e,t,n,r,o){const i=t&&t.routeConfig?t.routeConfig.canDeactivate:null;return i&&0!==i.length?T(i.map(a=>{const u=Wi(t)??o,c=wo(a,u);return Vn(function FL(e){return e&&Qi(e.canDeactivate)}(c)?c.canDeactivate(e,t,n,r):u.runInContext(()=>c(e,t,n,r))).pipe(sr())})).pipe(_o()):T(!0)}(o.component,o.route,n,t,r)),sr(o=>!0!==o,!0))}(s,r,o,e).pipe(Ae(a=>a&&function RL(e){return"boolean"==typeof e}(a)?function BL(e,t,n,r){return Ee(t).pipe(fo(o=>yf(function $L(e,t){return null!==e&&t&&t(new uL(e)),T(!0)}(o.route.parent,r),function HL(e,t){return null!==e&&t&&t(new lL(e)),T(!0)}(o.route,r),function zL(e,t,n){const r=t[t.length-1],i=t.slice(0,t.length-1).reverse().map(s=>function AL(e){const t=e.routeConfig?e.routeConfig.canActivateChild:null;return t&&0!==t.length?{node:e,guards:t}:null}(s)).filter(s=>null!==s).map(s=>Yw(()=>T(s.guards.map(u=>{const c=Wi(s.node)??n,l=wo(u,c);return Vn(function PL(e){return e&&Qi(e.canActivateChild)}(l)?l.canActivateChild(r,e):c.runInContext(()=>l(r,e))).pipe(sr())})).pipe(_o())));return T(i).pipe(_o())}(e,o.path,n),function UL(e,t,n){const r=t.routeConfig?t.routeConfig.canActivate:null;if(!r||0===r.length)return T(!0);const o=r.map(i=>Yw(()=>{const s=Wi(t)??n,a=wo(i,s);return Vn(function OL(e){return e&&Qi(e.canActivate)}(a)?a.canActivate(t,e):s.runInContext(()=>a(t,e))).pipe(sr())}));return T(o).pipe(_o())}(e,o.route,n))),sr(o=>!0!==o,!0))}(r,i,e,t):T(a)),Z(a=>({...n,guardsResult:a})))})}(this.environmentInjector,u=>this.events.next(u)),Le(u=>{if(i.guardsResult=u.guardsResult,cr(u.guardsResult))throw S_(0,u.guardsResult);const c=new rL(u.id,this.urlSerializer.serialize(u.extractedUrl),this.urlSerializer.serialize(u.urlAfterRedirects),u.targetSnapshot,!!u.guardsResult);this.events.next(c)}),Dn(u=>!!u.guardsResult||(this.cancelNavigationTransition(u,"",3),!1)),kf(u=>{if(u.guards.canActivateChecks.length)return T(u).pipe(Le(c=>{const l=new oL(c.id,this.urlSerializer.serialize(c.extractedUrl),this.urlSerializer.serialize(c.urlAfterRedirects),c.targetSnapshot);this.events.next(l)}),yt(c=>{let l=!1;return T(c).pipe(function cV(e,t){return Ae(n=>{const{targetSnapshot:r,guards:{canActivateChecks:o}}=n;if(!o.length)return T(n);let i=0;return Ee(o).pipe(fo(s=>function lV(e,t,n,r){const o=e.routeConfig,i=e._resolve;return void 0!==o?.title&&!B_(o)&&(i[Li]=o.title),function dV(e,t,n,r){const o=function fV(e){return[...Object.keys(e),...Object.getOwnPropertySymbols(e)]}(e);if(0===o.length)return T({});const i={};return Ee(o).pipe(Ae(s=>function hV(e,t,n,r){const o=Wi(t)??r,i=wo(e,o);return Vn(i.resolve?i.resolve(t,n):o.runInContext(()=>i(t,n)))}(e[s],t,n,r).pipe(sr(),Le(a=>{i[s]=a}))),Df(1),function Ik(e){return Z(()=>e)}(i),ar(s=>x_(s)?jt:ki(s)))}(i,e,t,r).pipe(Z(s=>(e._resolvedData=s,e.data=__(e,n).resolve,o&&B_(o)&&(e.data[Li]=o.title),null)))}(s.route,r,e,t)),Le(()=>i++),Df(1),Ae(s=>i===o.length?T(n):jt))})}(n.paramsInheritanceStrategy,this.environmentInjector),Le({next:()=>l=!0,complete:()=>{l||this.cancelNavigationTransition(c,"",2)}}))}),Le(c=>{const l=new iL(c.id,this.urlSerializer.serialize(c.extractedUrl),this.urlSerializer.serialize(c.urlAfterRedirects),c.targetSnapshot);this.events.next(l)}))}),kf(u=>{const c=l=>{const d=[];l.routeConfig?.loadComponent&&!l.routeConfig._loadedComponent&&d.push(this.configLoader.loadComponent(l.routeConfig).pipe(Le(f=>{l.component=f}),Z(()=>{})));for(const f of l.children)d.push(...c(f));return d};return mf(c(u.targetSnapshot.root)).pipe(ou(),go(1))}),kf(()=>this.afterPreactivation()),Z(u=>{const c=function mL(e,t,n){const r=qi(e,t._root,n?n._root:void 0);return new C_(r,t)}(n.routeReuseStrategy,u.targetSnapshot,u.currentRouterState);return this.currentTransition=i={...u,targetRouterState:c},i}),Le(()=>{this.events.next(new Mf)}),((e,t,n,r)=>Z(o=>(new IL(t,o.targetRouterState,o.currentRouterState,n,r).activate(e),o)))(this.rootContexts,n.routeReuseStrategy,u=>this.events.next(u),this.inputBindingEnabled),go(1),Le({next:u=>{s=!0,this.lastSuccessfulNavigation=this.currentNavigation,this.events.next(new jn(u.id,this.urlSerializer.serialize(u.extractedUrl),this.urlSerializer.serialize(u.urlAfterRedirects))),n.titleStrategy?.updateTitle(u.targetRouterState.snapshot),u.resolve(!0)},complete:()=>{s=!0}}),function Sk(e){return _e((t,n)=>{rt(e).subscribe(Ce(n,()=>n.complete(),Ou)),!n.closed&&t.subscribe(n)})}(this.transitionAbortSubject.pipe(Le(u=>{throw u}))),Oi(()=>{s||a||this.cancelNavigationTransition(i,"",1),this.currentNavigation?.id===i.id&&(this.currentNavigation=null)}),ar(u=>{if(a=!0,T_(u))this.events.next(new zi(i.id,this.urlSerializer.serialize(i.extractedUrl),u.message,u.cancellationCode)),function DL(e){return T_(e)&&cr(e.url)}(u)?this.events.next(new If(u.url)):i.resolve(!1);else{this.events.next(new fu(i.id,this.urlSerializer.serialize(i.extractedUrl),u,i.targetSnapshot??void 0));try{i.resolve(n.errorHandler(u))}catch(c){i.reject(c)}}return jt}))}))}cancelNavigationTransition(n,r,o){const i=new zi(n.id,this.urlSerializer.serialize(n.extractedUrl),r,o);this.events.next(i),n.resolve(!1)}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=S({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function $_(e){return e!==Ui}let U_=(()=>{class e{buildTitle(n){let r,o=n.root;for(;void 0!==o;)r=this.getResolvedTitleForRoute(o)??r,o=o.children.find(i=>i.outlet===B);return r}getResolvedTitleForRoute(n){return n.data[Li]}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=S({token:e,factory:function(){return E(mV)},providedIn:"root"})}}return e})(),mV=(()=>{class e extends U_{constructor(n){super(),this.title=n}updateTitle(n){const r=this.buildTitle(n);void 0!==r&&this.title.setTitle(r)}static{this.\u0275fac=function(r){return new(r||e)(I(Ew))}}static{this.\u0275prov=S({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),yV=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=S({token:e,factory:function(){return E(DV)},providedIn:"root"})}}return e})();class vV{shouldDetach(t){return!1}store(t,n){}shouldAttach(t){return!1}retrieve(t){return null}shouldReuseRoute(t,n){return t.routeConfig===n.routeConfig}}let DV=(()=>{class e extends vV{static{this.\u0275fac=function(){let n;return function(o){return(n||(n=Oe(e)))(o||e)}}()}static{this.\u0275prov=S({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();const Cu=new M("",{providedIn:"root",factory:()=>({})});let CV=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=S({token:e,factory:function(){return E(wV)},providedIn:"root"})}}return e})(),wV=(()=>{class e{shouldProcessUrl(n){return!0}extract(n){return n}merge(n,r){return n}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=S({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();var Xi=function(e){return e[e.COMPLETE=0]="COMPLETE",e[e.FAILED=1]="FAILED",e[e.REDIRECTING=2]="REDIRECTING",e}(Xi||{});function z_(e,t){e.events.pipe(Dn(n=>n instanceof jn||n instanceof zi||n instanceof fu||n instanceof vo),Z(n=>n instanceof jn||n instanceof vo?Xi.COMPLETE:n instanceof zi&&(0===n.code||1===n.code)?Xi.REDIRECTING:Xi.FAILED),Dn(n=>n!==Xi.REDIRECTING),go(1)).subscribe(()=>{t()})}function _V(e){throw e}function EV(e,t,n){return t.parse("/")}const bV={paths:"exact",fragment:"ignored",matrixParams:"ignored",queryParams:"exact"},MV={paths:"subset",fragment:"ignored",matrixParams:"ignored",queryParams:"subset"};let St=(()=>{class e{get navigationId(){return this.navigationTransitions.navigationId}get browserPageId(){return"computed"!==this.canceledNavigationResolution?this.currentPageId:this.location.getState()?.\u0275routerPageId??this.currentPageId}get events(){return this._events}constructor(){this.disposed=!1,this.currentPageId=0,this.console=E(JD),this.isNgZoneEnabled=!1,this._events=new At,this.options=E(Cu,{optional:!0})||{},this.pendingTasks=E(Ra),this.errorHandler=this.options.errorHandler||_V,this.malformedUriErrorHandler=this.options.malformedUriErrorHandler||EV,this.navigated=!1,this.lastSuccessfulId=-1,this.urlHandlingStrategy=E(CV),this.routeReuseStrategy=E(yV),this.titleStrategy=E(U_),this.onSameUrlNavigation=this.options.onSameUrlNavigation||"ignore",this.paramsInheritanceStrategy=this.options.paramsInheritanceStrategy||"emptyOnly",this.urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred",this.canceledNavigationResolution=this.options.canceledNavigationResolution||"replace",this.config=E(bo,{optional:!0})?.flat()??[],this.navigationTransitions=E(Du),this.urlSerializer=E(ji),this.location=E(jd),this.componentInputBindingEnabled=!!E(pu,{optional:!0}),this.eventsSubscription=new Ze,this.isNgZoneEnabled=E(se)instanceof se&&se.isInAngularZone(),this.resetConfig(this.config),this.currentUrlTree=new yo,this.rawUrlTree=this.currentUrlTree,this.browserUrlTree=this.currentUrlTree,this.routerState=w_(0,null),this.navigationTransitions.setupNavigations(this,this.currentUrlTree,this.routerState).subscribe(n=>{this.lastSuccessfulId=n.id,this.currentPageId=this.browserPageId},n=>{this.console.warn(`Unhandled Navigation Error: ${n}`)}),this.subscribeToNavigationEvents()}subscribeToNavigationEvents(){const n=this.navigationTransitions.events.subscribe(r=>{try{const{currentTransition:o}=this.navigationTransitions;if(null===o)return void(G_(r)&&this._events.next(r));if(r instanceof du)$_(o.source)&&(this.browserUrlTree=o.extractedUrl);else if(r instanceof vo)this.rawUrlTree=o.rawUrl;else if(r instanceof y_){if("eager"===this.urlUpdateStrategy){if(!o.extras.skipLocationChange){const i=this.urlHandlingStrategy.merge(o.urlAfterRedirects,o.rawUrl);this.setBrowserUrl(i,o)}this.browserUrlTree=o.urlAfterRedirects}}else if(r instanceof Mf)this.currentUrlTree=o.urlAfterRedirects,this.rawUrlTree=this.urlHandlingStrategy.merge(o.urlAfterRedirects,o.rawUrl),this.routerState=o.targetRouterState,"deferred"===this.urlUpdateStrategy&&(o.extras.skipLocationChange||this.setBrowserUrl(this.rawUrlTree,o),this.browserUrlTree=o.urlAfterRedirects);else if(r instanceof zi)0!==r.code&&1!==r.code&&(this.navigated=!0),(3===r.code||2===r.code)&&this.restoreHistory(o);else if(r instanceof If){const i=this.urlHandlingStrategy.merge(r.url,o.currentRawUrl),s={skipLocationChange:o.extras.skipLocationChange,replaceUrl:"eager"===this.urlUpdateStrategy||$_(o.source)};this.scheduleNavigation(i,Ui,null,s,{resolve:o.resolve,reject:o.reject,promise:o.promise})}r instanceof fu&&this.restoreHistory(o,!0),r instanceof jn&&(this.navigated=!0),G_(r)&&this._events.next(r)}catch(o){this.navigationTransitions.transitionAbortSubject.next(o)}});this.eventsSubscription.add(n)}resetRootComponentType(n){this.routerState.root.component=n,this.navigationTransitions.rootComponentType=n}initialNavigation(){if(this.setUpLocationChangeListener(),!this.navigationTransitions.hasRequestedNavigation){const n=this.location.getState();this.navigateToSyncWithBrowser(this.location.path(!0),Ui,n)}}setUpLocationChangeListener(){this.locationSubscription||(this.locationSubscription=this.location.subscribe(n=>{const r="popstate"===n.type?"popstate":"hashchange";"popstate"===r&&setTimeout(()=>{this.navigateToSyncWithBrowser(n.url,r,n.state)},0)}))}navigateToSyncWithBrowser(n,r,o){const i={replaceUrl:!0},s=o?.navigationId?o:null;if(o){const u={...o};delete u.navigationId,delete u.\u0275routerPageId,0!==Object.keys(u).length&&(i.state=u)}const a=this.parseUrl(n);this.scheduleNavigation(a,r,s,i)}get url(){return this.serializeUrl(this.currentUrlTree)}getCurrentNavigation(){return this.navigationTransitions.currentNavigation}get lastSuccessfulNavigation(){return this.navigationTransitions.lastSuccessfulNavigation}resetConfig(n){this.config=n.map(Of),this.navigated=!1,this.lastSuccessfulId=-1}ngOnDestroy(){this.dispose()}dispose(){this.navigationTransitions.complete(),this.locationSubscription&&(this.locationSubscription.unsubscribe(),this.locationSubscription=void 0),this.disposed=!0,this.eventsSubscription.unsubscribe()}createUrlTree(n,r={}){const{relativeTo:o,queryParams:i,fragment:s,queryParamsHandling:a,preserveFragment:u}=r,c=u?this.currentUrlTree.fragment:s;let d,l=null;switch(a){case"merge":l={...this.currentUrlTree.queryParams,...i};break;case"preserve":l=this.currentUrlTree.queryParams;break;default:l=i||null}null!==l&&(l=this.removeEmptyProps(l));try{d=l_(o?o.snapshot:this.routerState.snapshot.root)}catch{("string"!=typeof n[0]||!n[0].startsWith("/"))&&(n=[]),d=this.currentUrlTree.root}return d_(d,n,l,c??null)}navigateByUrl(n,r={skipLocationChange:!1}){const o=cr(n)?n:this.parseUrl(n),i=this.urlHandlingStrategy.merge(o,this.rawUrlTree);return this.scheduleNavigation(i,Ui,null,r)}navigate(n,r={skipLocationChange:!1}){return function IV(e){for(let t=0;t<e.length;t++)if(null==e[t])throw new C(4008,!1)}(n),this.navigateByUrl(this.createUrlTree(n,r),r)}serializeUrl(n){return this.urlSerializer.serialize(n)}parseUrl(n){let r;try{r=this.urlSerializer.parse(n)}catch(o){r=this.malformedUriErrorHandler(o,this.urlSerializer,n)}return r}isActive(n,r){let o;if(o=!0===r?{...bV}:!1===r?{...MV}:r,cr(n))return t_(this.currentUrlTree,n,o);const i=this.parseUrl(n);return t_(this.currentUrlTree,i,o)}removeEmptyProps(n){return Object.keys(n).reduce((r,o)=>{const i=n[o];return null!=i&&(r[o]=i),r},{})}scheduleNavigation(n,r,o,i,s){if(this.disposed)return Promise.resolve(!1);let a,u,c;s?(a=s.resolve,u=s.reject,c=s.promise):c=new Promise((d,f)=>{a=d,u=f});const l=this.pendingTasks.add();return z_(this,()=>{queueMicrotask(()=>this.pendingTasks.remove(l))}),this.navigationTransitions.handleNavigationRequest({source:r,restoredState:o,currentUrlTree:this.currentUrlTree,currentRawUrl:this.currentUrlTree,currentBrowserUrl:this.browserUrlTree,rawUrl:n,extras:i,resolve:a,reject:u,promise:c,currentSnapshot:this.routerState.snapshot,currentRouterState:this.routerState}),c.catch(d=>Promise.reject(d))}setBrowserUrl(n,r){const o=this.urlSerializer.serialize(n);if(this.location.isCurrentPathEqualTo(o)||r.extras.replaceUrl){const s={...r.extras.state,...this.generateNgRouterState(r.id,this.browserPageId)};this.location.replaceState(o,"",s)}else{const i={...r.extras.state,...this.generateNgRouterState(r.id,this.browserPageId+1)};this.location.go(o,"",i)}}restoreHistory(n,r=!1){if("computed"===this.canceledNavigationResolution){const i=this.currentPageId-this.browserPageId;0!==i?this.location.historyGo(i):this.currentUrlTree===this.getCurrentNavigation()?.finalUrl&&0===i&&(this.resetState(n),this.browserUrlTree=n.currentUrlTree,this.resetUrlToCurrentUrlTree())}else"replace"===this.canceledNavigationResolution&&(r&&this.resetState(n),this.resetUrlToCurrentUrlTree())}resetState(n){this.routerState=n.currentRouterState,this.currentUrlTree=n.currentUrlTree,this.rawUrlTree=this.urlHandlingStrategy.merge(this.currentUrlTree,n.rawUrl)}resetUrlToCurrentUrlTree(){this.location.replaceState(this.urlSerializer.serialize(this.rawUrlTree),"",this.generateNgRouterState(this.lastSuccessfulId,this.currentPageId))}generateNgRouterState(n,r){return"computed"===this.canceledNavigationResolution?{navigationId:n,\u0275routerPageId:r}:{navigationId:n}}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=S({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function G_(e){return!(e instanceof Mf||e instanceof If)}class q_{}let TV=(()=>{class e{constructor(n,r,o,i,s){this.router=n,this.injector=o,this.preloadingStrategy=i,this.loader=s}setUpPreloading(){this.subscription=this.router.events.pipe(Dn(n=>n instanceof jn),fo(()=>this.preload())).subscribe(()=>{})}preload(){return this.processRoutes(this.injector,this.router.config)}ngOnDestroy(){this.subscription&&this.subscription.unsubscribe()}processRoutes(n,r){const o=[];for(const i of r){i.providers&&!i._injector&&(i._injector=ad(i.providers,n,`Route: ${i.path}`));const s=i._injector??n,a=i._loadedInjector??s;(i.loadChildren&&!i._loadedRoutes&&void 0===i.canLoad||i.loadComponent&&!i._loadedComponent)&&o.push(this.preloadConfig(s,i)),(i.children||i._loadedRoutes)&&o.push(this.processRoutes(a,i.children??i._loadedRoutes))}return Ee(o).pipe(pr())}preloadConfig(n,r){return this.preloadingStrategy.preload(r,()=>{let o;o=r.loadChildren&&void 0===r.canLoad?this.loader.loadChildren(n,r):T(null);const i=o.pipe(Ae(s=>null===s?T(void 0):(r._loadedRoutes=s.routes,r._loadedInjector=s.injector,this.processRoutes(s.injector??n,s.routes))));return r.loadComponent&&!r._loadedComponent?Ee([i,this.loader.loadComponent(r)]).pipe(pr()):i})}static{this.\u0275fac=function(r){return new(r||e)(I(St),I(KD),I(ct),I(q_),I(Lf))}}static{this.\u0275prov=S({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();const jf=new M("");let W_=(()=>{class e{constructor(n,r,o,i,s={}){this.urlSerializer=n,this.transitions=r,this.viewportScroller=o,this.zone=i,this.options=s,this.lastId=0,this.lastSource="imperative",this.restoredId=0,this.store={},s.scrollPositionRestoration=s.scrollPositionRestoration||"disabled",s.anchorScrolling=s.anchorScrolling||"disabled"}init(){"disabled"!==this.options.scrollPositionRestoration&&this.viewportScroller.setHistoryScrollRestoration("manual"),this.routerEventsSubscription=this.createScrollEvents(),this.scrollEventsSubscription=this.consumeScrollEvents()}createScrollEvents(){return this.transitions.events.subscribe(n=>{n instanceof du?(this.store[this.lastId]=this.viewportScroller.getScrollPosition(),this.lastSource=n.navigationTrigger,this.restoredId=n.restoredState?n.restoredState.navigationId:0):n instanceof jn?(this.lastId=n.id,this.scheduleScrollEvent(n,this.urlSerializer.parse(n.urlAfterRedirects).fragment)):n instanceof vo&&0===n.code&&(this.lastSource=void 0,this.restoredId=0,this.scheduleScrollEvent(n,this.urlSerializer.parse(n.url).fragment))})}consumeScrollEvents(){return this.transitions.events.subscribe(n=>{n instanceof v_&&(n.position?"top"===this.options.scrollPositionRestoration?this.viewportScroller.scrollToPosition([0,0]):"enabled"===this.options.scrollPositionRestoration&&this.viewportScroller.scrollToPosition(n.position):n.anchor&&"enabled"===this.options.anchorScrolling?this.viewportScroller.scrollToAnchor(n.anchor):"disabled"!==this.options.scrollPositionRestoration&&this.viewportScroller.scrollToPosition([0,0]))})}scheduleScrollEvent(n,r){this.zone.runOutsideAngular(()=>{setTimeout(()=>{this.zone.run(()=>{this.transitions.events.next(new v_(n,"popstate"===this.lastSource?this.store[this.restoredId]:null,r))})},0)})}ngOnDestroy(){this.routerEventsSubscription?.unsubscribe(),this.scrollEventsSubscription?.unsubscribe()}static{this.\u0275fac=function(r){!function zm(){throw new Error("invalid")}()}}static{this.\u0275prov=S({token:e,factory:e.\u0275fac})}}return e})();function wn(e,t){return{\u0275kind:e,\u0275providers:t}}function Y_(){const e=E(dt);return t=>{const n=e.get(uo);if(t!==n.components[0])return;const r=e.get(St),o=e.get(Q_);1===e.get(Bf)&&r.initialNavigation(),e.get(X_,null,$.Optional)?.setUpPreloading(),e.get(jf,null,$.Optional)?.init(),r.resetRootComponentType(n.componentTypes[0]),o.closed||(o.next(),o.complete(),o.unsubscribe())}}const Q_=new M("",{factory:()=>new At}),Bf=new M("",{providedIn:"root",factory:()=>1}),X_=new M("");function OV(e){return wn(0,[{provide:X_,useExisting:TV},{provide:q_,useExisting:e}])}const J_=new M("ROUTER_FORROOT_GUARD"),FV=[jd,{provide:ji,useClass:Cf},St,Gi,{provide:Co,useFactory:function Z_(e){return e.routerState.root},deps:[St]},Lf,[]];function kV(){return new sC("Router",St)}let K_=(()=>{class e{constructor(n){}static forRoot(n,r){return{ngModule:e,providers:[FV,[],{provide:bo,multi:!0,useValue:n},{provide:J_,useFactory:BV,deps:[[St,new Ps,new Fs]]},{provide:Cu,useValue:r||{}},r?.useHash?{provide:or,useClass:DP}:{provide:or,useClass:LC},{provide:jf,useFactory:()=>{const e=E(LF),t=E(se),n=E(Cu),r=E(Du),o=E(ji);return n.scrollOffset&&e.setOffset(n.scrollOffset),new W_(o,r,e,t,n)}},r?.preloadingStrategy?OV(r.preloadingStrategy).\u0275providers:[],{provide:sC,multi:!0,useFactory:kV},r?.initialNavigation?HV(r):[],r?.bindToComponentInputs?wn(8,[I_,{provide:pu,useExisting:I_}]).\u0275providers:[],[{provide:eE,useFactory:Y_},{provide:Td,multi:!0,useExisting:eE}]]}}static forChild(n){return{ngModule:e,providers:[{provide:bo,multi:!0,useValue:n}]}}static{this.\u0275fac=function(r){return new(r||e)(I(J_,8))}}static{this.\u0275mod=Dt({type:e})}static{this.\u0275inj=it({})}}return e})();function BV(e){return"guarded"}function HV(e){return["disabled"===e.initialNavigation?wn(3,[{provide:wd,multi:!0,useFactory:()=>{const t=E(St);return()=>{t.setUpLocationChangeListener()}}},{provide:Bf,useValue:2}]).\u0275providers:[],"enabledBlocking"===e.initialNavigation?wn(2,[{provide:Bf,useValue:0},{provide:wd,multi:!0,deps:[dt],useFactory:t=>{const n=t.get(yP,Promise.resolve());return()=>n.then(()=>new Promise(r=>{const o=t.get(St),i=t.get(Q_);z_(o,()=>{r(!0)}),t.get(Du).afterPreactivation=()=>(r(!0),i.closed?T(void 0):i),o.initialNavigation()}))}}]).\u0275providers:[]]}const eE=new M("");class UV extends Ze{constructor(t,n){super()}schedule(t,n=0){return this}}const wu={setInterval(e,t,...n){const{delegate:r}=wu;return r?.setInterval?r.setInterval(e,t,...n):setInterval(e,t,...n)},clearInterval(e){const{delegate:t}=wu;return(t?.clearInterval||clearInterval)(e)},delegate:void 0},tE={now:()=>(tE.delegate||Date).now(),delegate:void 0};class Ji{constructor(t,n=Ji.now){this.schedulerActionCtor=t,this.now=n}schedule(t,n=0,r){return new this.schedulerActionCtor(this,t).schedule(r,n)}}Ji.now=tE.now;const nE=new class GV extends Ji{constructor(t,n=Ji.now){super(t,n),this.actions=[],this._active=!1}flush(t){const{actions:n}=this;if(this._active)return void n.push(t);let r;this._active=!0;do{if(r=t.execute(t.state,t.delay))break}while(t=n.shift());if(this._active=!1,r){for(;t=n.shift();)t.unsubscribe();throw r}}}(class zV extends UV{constructor(t,n){super(t,n),this.scheduler=t,this.work=n,this.pending=!1}schedule(t,n=0){var r;if(this.closed)return this;this.state=t;const o=this.id,i=this.scheduler;return null!=o&&(this.id=this.recycleAsyncId(i,o,n)),this.pending=!0,this.delay=n,this.id=null!==(r=this.id)&&void 0!==r?r:this.requestAsyncId(i,this.id,n),this}requestAsyncId(t,n,r=0){return wu.setInterval(t.flush.bind(t,this),r)}recycleAsyncId(t,n,r=0){if(null!=r&&this.delay===r&&!1===this.pending)return n;null!=n&&wu.clearInterval(n)}execute(t,n){if(this.closed)return new Error("executing a cancelled action");this.pending=!1;const r=this._execute(t,n);if(r)return r;!1===this.pending&&null!=this.id&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))}_execute(t,n){let o,r=!1;try{this.work(t)}catch(i){r=!0,o=i||new Error("Scheduled action threw falsy error")}if(r)return this.unsubscribe(),o}unsubscribe(){if(!this.closed){const{id:t,scheduler:n}=this,{actions:r}=n;this.work=this.state=this.scheduler=null,this.pending=!1,hr(r,this),null!=t&&(this.id=this.recycleAsyncId(n,t,null)),this.delay=null,super.unsubscribe()}}}),qV=nE;let rE=(()=>{class e{constructor(n){this.http=n,this.apiUrl="/api/metrics"}getRefreshInterval(){return this.http.get("/api/config/refresh-interval")}getDBMetrics(){return this.http.get("/api/metrics/db")}getKafkaMetrics(){return this.http.get("/api/metrics/kafka")}getErrors(){return this.http.get("/errors")}getPollingMetrics(n){return function YV(e=0,t=nE){return e<0&&(e=0),function ZV(e=0,t,n=qV){let r=-1;return null!=t&&(Oh(t)?n=t:r=t),new he(o=>{let i=function WV(e){return e instanceof Date&&!isNaN(e)}(e)?+e-n.now():e;i<0&&(i=0);let s=0;return n.schedule(function(){o.closed||(o.next(s++),0<=r?this.schedule(void 0,r):o.complete())},i)})}(e,e,t)}(n).pipe(yt(()=>Promise.all([this.getDBMetrics().toPromise(),this.getKafkaMetrics().toPromise(),this.getErrors().toPromise()])))}setLastRecord2ShowNum(n){return this.http.post(`${this.apiUrl}/db/lastRecord2ShowNum`,null,{params:{newValue:n.toString()}})}static{this.\u0275fac=function(r){return new(r||e)(I(xw))}}static{this.\u0275prov=S({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),oE=(()=>{class e{constructor(n,r){this._renderer=n,this._elementRef=r,this.onChange=o=>{},this.onTouched=()=>{}}setProperty(n,r){this._renderer.setProperty(this._elementRef.nativeElement,n,r)}registerOnTouched(n){this.onTouched=n}registerOnChange(n){this.onChange=n}setDisabledState(n){this.setProperty("disabled",n)}static{this.\u0275fac=function(r){return new(r||e)(w(ln),w(lt))}}static{this.\u0275dir=O({type:e})}}return e})(),lr=(()=>{class e extends oE{static{this.\u0275fac=function(){let n;return function(o){return(n||(n=Oe(e)))(o||e)}}()}static{this.\u0275dir=O({type:e,features:[K]})}}return e})();const en=new M("NgValueAccessor"),JV={provide:en,useExisting:re(()=>_u),multi:!0},ej=new M("CompositionEventMode");let _u=(()=>{class e extends oE{constructor(n,r,o){super(n,r),this._compositionMode=o,this._composing=!1,null==this._compositionMode&&(this._compositionMode=!function KV(){const e=Pn()?Pn().getUserAgent():"";return/android (\d+)/.test(e.toLowerCase())}())}writeValue(n){this.setProperty("value",n??"")}_handleInput(n){(!this._compositionMode||this._compositionMode&&!this._composing)&&this.onChange(n)}_compositionStart(){this._composing=!0}_compositionEnd(n){this._composing=!1,this._compositionMode&&this.onChange(n)}static{this.\u0275fac=function(r){return new(r||e)(w(ln),w(lt),w(ej,8))}}static{this.\u0275dir=O({type:e,selectors:[["input","formControlName","",3,"type","checkbox"],["textarea","formControlName",""],["input","formControl","",3,"type","checkbox"],["textarea","formControl",""],["input","ngModel","",3,"type","checkbox"],["textarea","ngModel",""],["","ngDefaultControl",""]],hostBindings:function(r,o){1&r&&et("input",function(s){return o._handleInput(s.target.value)})("blur",function(){return o.onTouched()})("compositionstart",function(){return o._compositionStart()})("compositionend",function(s){return o._compositionEnd(s.target.value)})},features:[de([JV]),K]})}}return e})();const Ve=new M("NgValidators"),Hn=new M("NgAsyncValidators");function gE(e){return null!=e}function mE(e){return yi(e)?Ee(e):e}function yE(e){let t={};return e.forEach(n=>{t=null!=n?{...t,...n}:t}),0===Object.keys(t).length?null:t}function vE(e,t){return t.map(n=>n(e))}function DE(e){return e.map(t=>function nj(e){return!e.validate}(t)?t:n=>t.validate(n))}function Hf(e){return null!=e?function CE(e){if(!e)return null;const t=e.filter(gE);return 0==t.length?null:function(n){return yE(vE(n,t))}}(DE(e)):null}function $f(e){return null!=e?function wE(e){if(!e)return null;const t=e.filter(gE);return 0==t.length?null:function(n){return function QV(...e){const t=Ph(e),{args:n,keys:r}=Gw(e),o=new he(i=>{const{length:s}=n;if(!s)return void i.complete();const a=new Array(s);let u=s,c=s;for(let l=0;l<s;l++){let d=!1;rt(n[l]).subscribe(Ce(i,f=>{d||(d=!0,c--),a[l]=f},()=>u--,void 0,()=>{(!u||!d)&&(c||i.next(r?Ww(r,a):a),i.complete())}))}});return t?o.pipe(qw(t)):o}(vE(n,t).map(mE)).pipe(Z(yE))}}(DE(e)):null}function _E(e,t){return null===e?[t]:Array.isArray(e)?[...e,t]:[e,t]}function Uf(e){return e?Array.isArray(e)?e:[e]:[]}function bu(e,t){return Array.isArray(e)?e.includes(t):e===t}function ME(e,t){const n=Uf(t);return Uf(e).forEach(o=>{bu(n,o)||n.push(o)}),n}function IE(e,t){return Uf(t).filter(n=>!bu(e,n))}class SE{constructor(){this._rawValidators=[],this._rawAsyncValidators=[],this._onDestroyCallbacks=[]}get value(){return this.control?this.control.value:null}get valid(){return this.control?this.control.valid:null}get invalid(){return this.control?this.control.invalid:null}get pending(){return this.control?this.control.pending:null}get disabled(){return this.control?this.control.disabled:null}get enabled(){return this.control?this.control.enabled:null}get errors(){return this.control?this.control.errors:null}get pristine(){return this.control?this.control.pristine:null}get dirty(){return this.control?this.control.dirty:null}get touched(){return this.control?this.control.touched:null}get status(){return this.control?this.control.status:null}get untouched(){return this.control?this.control.untouched:null}get statusChanges(){return this.control?this.control.statusChanges:null}get valueChanges(){return this.control?this.control.valueChanges:null}get path(){return null}_setValidators(t){this._rawValidators=t||[],this._composedValidatorFn=Hf(this._rawValidators)}_setAsyncValidators(t){this._rawAsyncValidators=t||[],this._composedAsyncValidatorFn=$f(this._rawAsyncValidators)}get validator(){return this._composedValidatorFn||null}get asyncValidator(){return this._composedAsyncValidatorFn||null}_registerOnDestroy(t){this._onDestroyCallbacks.push(t)}_invokeOnDestroyCallbacks(){this._onDestroyCallbacks.forEach(t=>t()),this._onDestroyCallbacks=[]}reset(t=void 0){this.control&&this.control.reset(t)}hasError(t,n){return!!this.control&&this.control.hasError(t,n)}getError(t,n){return this.control?this.control.getError(t,n):null}}class We extends SE{get formDirective(){return null}get path(){return null}}class $n extends SE{constructor(){super(...arguments),this._parent=null,this.name=null,this.valueAccessor=null}}class AE{constructor(t){this._cd=t}get isTouched(){return!!this._cd?.control?.touched}get isUntouched(){return!!this._cd?.control?.untouched}get isPristine(){return!!this._cd?.control?.pristine}get isDirty(){return!!this._cd?.control?.dirty}get isValid(){return!!this._cd?.control?.valid}get isInvalid(){return!!this._cd?.control?.invalid}get isPending(){return!!this._cd?.control?.pending}get isSubmitted(){return!!this._cd?.submitted}}let TE=(()=>{class e extends AE{constructor(n){super(n)}static{this.\u0275fac=function(r){return new(r||e)(w($n,2))}}static{this.\u0275dir=O({type:e,selectors:[["","formControlName",""],["","ngModel",""],["","formControl",""]],hostVars:14,hostBindings:function(r,o){2&r&&_a("ng-untouched",o.isUntouched)("ng-touched",o.isTouched)("ng-pristine",o.isPristine)("ng-dirty",o.isDirty)("ng-valid",o.isValid)("ng-invalid",o.isInvalid)("ng-pending",o.isPending)},features:[K]})}}return e})();const Ki="VALID",Iu="INVALID",Mo="PENDING",es="DISABLED";function Su(e){return null!=e&&!Array.isArray(e)&&"object"==typeof e}class Zf{constructor(t,n){this._pendingDirty=!1,this._hasOwnPendingAsyncValidator=!1,this._pendingTouched=!1,this._onCollectionChange=()=>{},this._parent=null,this.pristine=!0,this.touched=!1,this._onDisabledChange=[],this._assignValidators(t),this._assignAsyncValidators(n)}get validator(){return this._composedValidatorFn}set validator(t){this._rawValidators=this._composedValidatorFn=t}get asyncValidator(){return this._composedAsyncValidatorFn}set asyncValidator(t){this._rawAsyncValidators=this._composedAsyncValidatorFn=t}get parent(){return this._parent}get valid(){return this.status===Ki}get invalid(){return this.status===Iu}get pending(){return this.status==Mo}get disabled(){return this.status===es}get enabled(){return this.status!==es}get dirty(){return!this.pristine}get untouched(){return!this.touched}get updateOn(){return this._updateOn?this._updateOn:this.parent?this.parent.updateOn:"change"}setValidators(t){this._assignValidators(t)}setAsyncValidators(t){this._assignAsyncValidators(t)}addValidators(t){this.setValidators(ME(t,this._rawValidators))}addAsyncValidators(t){this.setAsyncValidators(ME(t,this._rawAsyncValidators))}removeValidators(t){this.setValidators(IE(t,this._rawValidators))}removeAsyncValidators(t){this.setAsyncValidators(IE(t,this._rawAsyncValidators))}hasValidator(t){return bu(this._rawValidators,t)}hasAsyncValidator(t){return bu(this._rawAsyncValidators,t)}clearValidators(){this.validator=null}clearAsyncValidators(){this.asyncValidator=null}markAsTouched(t={}){this.touched=!0,this._parent&&!t.onlySelf&&this._parent.markAsTouched(t)}markAllAsTouched(){this.markAsTouched({onlySelf:!0}),this._forEachChild(t=>t.markAllAsTouched())}markAsUntouched(t={}){this.touched=!1,this._pendingTouched=!1,this._forEachChild(n=>{n.markAsUntouched({onlySelf:!0})}),this._parent&&!t.onlySelf&&this._parent._updateTouched(t)}markAsDirty(t={}){this.pristine=!1,this._parent&&!t.onlySelf&&this._parent.markAsDirty(t)}markAsPristine(t={}){this.pristine=!0,this._pendingDirty=!1,this._forEachChild(n=>{n.markAsPristine({onlySelf:!0})}),this._parent&&!t.onlySelf&&this._parent._updatePristine(t)}markAsPending(t={}){this.status=Mo,!1!==t.emitEvent&&this.statusChanges.emit(this.status),this._parent&&!t.onlySelf&&this._parent.markAsPending(t)}disable(t={}){const n=this._parentMarkedDirty(t.onlySelf);this.status=es,this.errors=null,this._forEachChild(r=>{r.disable({...t,onlySelf:!0})}),this._updateValue(),!1!==t.emitEvent&&(this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._updateAncestors({...t,skipPristineCheck:n}),this._onDisabledChange.forEach(r=>r(!0))}enable(t={}){const n=this._parentMarkedDirty(t.onlySelf);this.status=Ki,this._forEachChild(r=>{r.enable({...t,onlySelf:!0})}),this.updateValueAndValidity({onlySelf:!0,emitEvent:t.emitEvent}),this._updateAncestors({...t,skipPristineCheck:n}),this._onDisabledChange.forEach(r=>r(!1))}_updateAncestors(t){this._parent&&!t.onlySelf&&(this._parent.updateValueAndValidity(t),t.skipPristineCheck||this._parent._updatePristine(),this._parent._updateTouched())}setParent(t){this._parent=t}getRawValue(){return this.value}updateValueAndValidity(t={}){this._setInitialStatus(),this._updateValue(),this.enabled&&(this._cancelExistingSubscription(),this.errors=this._runValidator(),this.status=this._calculateStatus(),(this.status===Ki||this.status===Mo)&&this._runAsyncValidator(t.emitEvent)),!1!==t.emitEvent&&(this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._parent&&!t.onlySelf&&this._parent.updateValueAndValidity(t)}_updateTreeValidity(t={emitEvent:!0}){this._forEachChild(n=>n._updateTreeValidity(t)),this.updateValueAndValidity({onlySelf:!0,emitEvent:t.emitEvent})}_setInitialStatus(){this.status=this._allControlsDisabled()?es:Ki}_runValidator(){return this.validator?this.validator(this):null}_runAsyncValidator(t){if(this.asyncValidator){this.status=Mo,this._hasOwnPendingAsyncValidator=!0;const n=mE(this.asyncValidator(this));this._asyncValidationSubscription=n.subscribe(r=>{this._hasOwnPendingAsyncValidator=!1,this.setErrors(r,{emitEvent:t})})}}_cancelExistingSubscription(){this._asyncValidationSubscription&&(this._asyncValidationSubscription.unsubscribe(),this._hasOwnPendingAsyncValidator=!1)}setErrors(t,n={}){this.errors=t,this._updateControlsErrors(!1!==n.emitEvent)}get(t){let n=t;return null==n||(Array.isArray(n)||(n=n.split(".")),0===n.length)?null:n.reduce((r,o)=>r&&r._find(o),this)}getError(t,n){const r=n?this.get(n):this;return r&&r.errors?r.errors[t]:null}hasError(t,n){return!!this.getError(t,n)}get root(){let t=this;for(;t._parent;)t=t._parent;return t}_updateControlsErrors(t){this.status=this._calculateStatus(),t&&this.statusChanges.emit(this.status),this._parent&&this._parent._updateControlsErrors(t)}_initObservables(){this.valueChanges=new ge,this.statusChanges=new ge}_calculateStatus(){return this._allControlsDisabled()?es:this.errors?Iu:this._hasOwnPendingAsyncValidator||this._anyControlsHaveStatus(Mo)?Mo:this._anyControlsHaveStatus(Iu)?Iu:Ki}_anyControlsHaveStatus(t){return this._anyControls(n=>n.status===t)}_anyControlsDirty(){return this._anyControls(t=>t.dirty)}_anyControlsTouched(){return this._anyControls(t=>t.touched)}_updatePristine(t={}){this.pristine=!this._anyControlsDirty(),this._parent&&!t.onlySelf&&this._parent._updatePristine(t)}_updateTouched(t={}){this.touched=this._anyControlsTouched(),this._parent&&!t.onlySelf&&this._parent._updateTouched(t)}_registerOnCollectionChange(t){this._onCollectionChange=t}_setUpdateStrategy(t){Su(t)&&null!=t.updateOn&&(this._updateOn=t.updateOn)}_parentMarkedDirty(t){return!t&&!(!this._parent||!this._parent.dirty)&&!this._parent._anyControlsDirty()}_find(t){return null}_assignValidators(t){this._rawValidators=Array.isArray(t)?t.slice():t,this._composedValidatorFn=function aj(e){return Array.isArray(e)?Hf(e):e||null}(this._rawValidators)}_assignAsyncValidators(t){this._rawAsyncValidators=Array.isArray(t)?t.slice():t,this._composedAsyncValidatorFn=function uj(e){return Array.isArray(e)?$f(e):e||null}(this._rawAsyncValidators)}}const Io=new M("CallSetDisabledState",{providedIn:"root",factory:()=>Au}),Au="always";function ts(e,t,n=Au){(function Qf(e,t){const n=function EE(e){return e._rawValidators}(e);null!==t.validator?e.setValidators(_E(n,t.validator)):"function"==typeof n&&e.setValidators([n]);const r=function bE(e){return e._rawAsyncValidators}(e);null!==t.asyncValidator?e.setAsyncValidators(_E(r,t.asyncValidator)):"function"==typeof r&&e.setAsyncValidators([r]);const o=()=>e.updateValueAndValidity();Ru(t._rawValidators,o),Ru(t._rawAsyncValidators,o)})(e,t),t.valueAccessor.writeValue(e.value),(e.disabled||"always"===n)&&t.valueAccessor.setDisabledState?.(e.disabled),function dj(e,t){t.valueAccessor.registerOnChange(n=>{e._pendingValue=n,e._pendingChange=!0,e._pendingDirty=!0,"change"===e.updateOn&&OE(e,t)})}(e,t),function hj(e,t){const n=(r,o)=>{t.valueAccessor.writeValue(r),o&&t.viewToModelUpdate(r)};e.registerOnChange(n),t._registerOnDestroy(()=>{e._unregisterOnChange(n)})}(e,t),function fj(e,t){t.valueAccessor.registerOnTouched(()=>{e._pendingTouched=!0,"blur"===e.updateOn&&e._pendingChange&&OE(e,t),"submit"!==e.updateOn&&e.markAsTouched()})}(e,t),function lj(e,t){if(t.valueAccessor.setDisabledState){const n=r=>{t.valueAccessor.setDisabledState(r)};e.registerOnDisabledChange(n),t._registerOnDestroy(()=>{e._unregisterOnDisabledChange(n)})}}(e,t)}function Ru(e,t){e.forEach(n=>{n.registerOnValidatorChange&&n.registerOnValidatorChange(t)})}function OE(e,t){e._pendingDirty&&e.markAsDirty(),e.setValue(e._pendingValue,{emitModelToViewChange:!1}),t.viewToModelUpdate(e._pendingValue),e._pendingChange=!1}function kE(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}function LE(e){return"object"==typeof e&&null!==e&&2===Object.keys(e).length&&"value"in e&&"disabled"in e}const VE=class extends Zf{constructor(t=null,n,r){super(function qf(e){return(Su(e)?e.validators:e)||null}(n),function Wf(e,t){return(Su(t)?t.asyncValidators:e)||null}(r,n)),this.defaultValue=null,this._onChange=[],this._pendingChange=!1,this._applyFormState(t),this._setUpdateStrategy(n),this._initObservables(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator}),Su(n)&&(n.nonNullable||n.initialValueIsDefault)&&(this.defaultValue=LE(t)?t.value:t)}setValue(t,n={}){this.value=this._pendingValue=t,this._onChange.length&&!1!==n.emitModelToViewChange&&this._onChange.forEach(r=>r(this.value,!1!==n.emitViewToModelChange)),this.updateValueAndValidity(n)}patchValue(t,n={}){this.setValue(t,n)}reset(t=this.defaultValue,n={}){this._applyFormState(t),this.markAsPristine(n),this.markAsUntouched(n),this.setValue(this.value,n),this._pendingChange=!1}_updateValue(){}_anyControls(t){return!1}_allControlsDisabled(){return this.disabled}registerOnChange(t){this._onChange.push(t)}_unregisterOnChange(t){kE(this._onChange,t)}registerOnDisabledChange(t){this._onDisabledChange.push(t)}_unregisterOnDisabledChange(t){kE(this._onDisabledChange,t)}_forEachChild(t){}_syncPendingControls(){return!("submit"!==this.updateOn||(this._pendingDirty&&this.markAsDirty(),this._pendingTouched&&this.markAsTouched(),!this._pendingChange)||(this.setValue(this._pendingValue,{onlySelf:!0,emitModelToViewChange:!1}),0))}_applyFormState(t){LE(t)?(this.value=this._pendingValue=t.value,t.disabled?this.disable({onlySelf:!0,emitEvent:!1}):this.enable({onlySelf:!0,emitEvent:!1})):this.value=this._pendingValue=t}},wj={provide:$n,useExisting:re(()=>th)},HE=(()=>Promise.resolve())();let th=(()=>{class e extends $n{constructor(n,r,o,i,s,a){super(),this._changeDetectorRef=s,this.callSetDisabledState=a,this.control=new VE,this._registered=!1,this.name="",this.update=new ge,this._parent=n,this._setValidators(r),this._setAsyncValidators(o),this.valueAccessor=function Kf(e,t){if(!t)return null;let n,r,o;return Array.isArray(t),t.forEach(i=>{i.constructor===_u?n=i:function mj(e){return Object.getPrototypeOf(e.constructor)===lr}(i)?r=i:o=i}),o||r||n||null}(0,i)}ngOnChanges(n){if(this._checkForErrors(),!this._registered||"name"in n){if(this._registered&&(this._checkName(),this.formDirective)){const r=n.name.previousValue;this.formDirective.removeControl({name:r,path:this._getPath(r)})}this._setUpControl()}"isDisabled"in n&&this._updateDisabled(n),function Jf(e,t){if(!e.hasOwnProperty("model"))return!1;const n=e.model;return!!n.isFirstChange()||!Object.is(t,n.currentValue)}(n,this.viewModel)&&(this._updateValue(this.model),this.viewModel=this.model)}ngOnDestroy(){this.formDirective&&this.formDirective.removeControl(this)}get path(){return this._getPath(this.name)}get formDirective(){return this._parent?this._parent.formDirective:null}viewToModelUpdate(n){this.viewModel=n,this.update.emit(n)}_setUpControl(){this._setUpdateStrategy(),this._isStandalone()?this._setUpStandalone():this.formDirective.addControl(this),this._registered=!0}_setUpdateStrategy(){this.options&&null!=this.options.updateOn&&(this.control._updateOn=this.options.updateOn)}_isStandalone(){return!this._parent||!(!this.options||!this.options.standalone)}_setUpStandalone(){ts(this.control,this,this.callSetDisabledState),this.control.updateValueAndValidity({emitEvent:!1})}_checkForErrors(){this._isStandalone()||this._checkParentType(),this._checkName()}_checkParentType(){}_checkName(){this.options&&this.options.name&&(this.name=this.options.name),this._isStandalone()}_updateValue(n){HE.then(()=>{this.control.setValue(n,{emitViewToModelChange:!1}),this._changeDetectorRef?.markForCheck()})}_updateDisabled(n){const r=n.isDisabled.currentValue,o=0!==r&&function co(e){return"boolean"==typeof e?e:null!=e&&"false"!==e}(r);HE.then(()=>{o&&!this.control.disabled?this.control.disable():!o&&this.control.disabled&&this.control.enable(),this._changeDetectorRef?.markForCheck()})}_getPath(n){return this._parent?function Tu(e,t){return[...t.path,e]}(n,this._parent):[n]}static{this.\u0275fac=function(r){return new(r||e)(w(We,9),w(Ve,10),w(Hn,10),w(en,10),w(Fa,8),w(Io,8))}}static{this.\u0275dir=O({type:e,selectors:[["","ngModel","",3,"formControlName","",3,"formControl",""]],inputs:{name:"name",isDisabled:["disabled","isDisabled"],model:["ngModel","model"],options:["ngModelOptions","options"]},outputs:{update:"ngModelChange"},exportAs:["ngModel"],features:[de([wj]),K,wt]})}}return e})();const Ej={provide:en,useExisting:re(()=>nh),multi:!0};let nh=(()=>{class e extends lr{writeValue(n){this.setProperty("value",n??"")}registerOnChange(n){this.onChange=r=>{n(""==r?null:parseFloat(r))}}static{this.\u0275fac=function(){let n;return function(o){return(n||(n=Oe(e)))(o||e)}}()}static{this.\u0275dir=O({type:e,selectors:[["input","type","number","formControlName",""],["input","type","number","formControl",""],["input","type","number","ngModel",""]],hostBindings:function(r,o){1&r&&et("input",function(s){return o.onChange(s.target.value)})("blur",function(){return o.onTouched()})},features:[de([Ej]),K]})}}return e})(),$E=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275mod=Dt({type:e})}static{this.\u0275inj=it({})}}return e})(),qj=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275mod=Dt({type:e})}static{this.\u0275inj=it({imports:[$E]})}}return e})(),Zj=(()=>{class e{static withConfig(n){return{ngModule:e,providers:[{provide:Io,useValue:n.callSetDisabledState??Au}]}}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275mod=Dt({type:e})}static{this.\u0275inj=it({imports:[qj]})}}return e})();function Yj(e,t){if(1&e&&(V(0,"tr")(1,"td"),z(2),j(),V(3,"td"),z(4),j(),V(5,"td"),z(6),j(),V(7,"td"),z(8),function yD(e,t){const n=q();let r;const o=e+U;n.firstCreatePass?(r=function fx(e,t){if(t)for(let n=t.length-1;n>=0;n--){const r=t[n];if(e===r.name)return r}}(t,n.pipeRegistry),n.data[o]=r,r.onDestroy&&(n.destroyHooks??=[]).push(o,r.onDestroy)):r=n.data[o];const i=r.factory||(r.factory=Wn(r.type)),a=Ye(w);try{const u=Ss(!1),c=i();return Ss(u),function $T(e,t,n,r){n>=e.data.length&&(e.data[n]=null,e.blueprint[n]=null),t[n]=r}(n,v(),o,c),c}finally{Ye(a)}}(9,"date"),j(),V(10,"td"),z(11),j(),V(12,"td"),z(13),j(),V(14,"td"),z(15),j(),V(16,"td"),z(17),j(),V(18,"td"),z(19),j()()),2&e){const n=t.$implicit;le(2),ht(n.businessId),le(2),ht(n.chain),le(2),ht(n.topic),le(2),ht(vD(9,9,n.ts,"yyyy-MM-dd HH:mm:ss.SSS")),le(3),ht(n.messageId),le(2),ht(n.auto_id),le(2),ht(n.targetClass),le(2),ht(n.ionComponent),le(2),ht(n.is_published)}}function Qj(e,t){if(1&e&&(V(0,"div",18)(1,"p"),z(2),j()()),2&e){const n=t.$implicit;le(2),ht(n)}}let ch=(()=>{class e{constructor(n){this.metricsService=n,this.initialLastRecord2ShowNum=5,this.refreshInterval=9e3,this.dbMetrics={totalCount:0,totalInserts:0,failedInserts:0,unSendRecords:0,lastRecords:[]},this.kafkaMetrics={messagesSent:0,successfulSends:0,failedSends:0,retryMapSize:0,publisherName:""},this.errors=[],this.subscription=null}ngOnInit(){this.initializePolling()}initializePolling(){this.metricsService.getRefreshInterval().subscribe(n=>{this.refreshInterval=n,this.subscription=this.metricsService.getPollingMetrics(this.refreshInterval).subscribe(([r,o,i])=>{r&&(this.dbMetrics={...this.dbMetrics,...r}),o&&(this.kafkaMetrics={...this.kafkaMetrics,...o}),i&&(this.errors=i)},r=>console.error("Failed to fetch metrics:",r))},n=>console.error("Failed to fetch refresh interval:",n))}getMetrics(){this.metricsService.getDBMetrics().subscribe(n=>{this.dbMetrics=n},n=>{console.error("Failed to fetch DB metrics",n)}),this.metricsService.getKafkaMetrics().subscribe(n=>{this.kafkaMetrics=n},n=>{console.error("Failed to fetch Kafka metrics",n)})}getErrors(){this.metricsService.getErrors().subscribe(n=>{this.errors=n},n=>{console.error("Failed to fetch errors",n)})}ngOnDestroy(){this.subscription&&this.subscription.unsubscribe()}updateLastRecord2ShowNum(n){this.metricsService.setLastRecord2ShowNum(n).subscribe(r=>{console.log("lastRecord2ShowNum Value updated successfully")},r=>{console.error("Failed to update lastRecord2ShowNum value",r)})}static{this.\u0275fac=function(r){return new(r||e)(w(rE))}}static{this.\u0275cmp=ms({type:e,selectors:[["app-metrics-dashboard"]],decls:69,vars:12,consts:[[1,"content-wrapper"],[1,"content-container"],[1,"metrics-row"],[1,"metrics-card","db-metrics"],[1,"metrics-content"],[1,"metrics-card","kafka-metrics"],["for","newValueInput"],["id","newValueInput","type","number",3,"ngModel","ngModelChange"],[1,"margin-bottom-6px",3,"click"],[1,"metrics-card","records-table"],[1,"table-header"],[1,"table-container"],[4,"ngFor","ngForOf"],[1,"metrics-card","error-section"],[1,"error-header"],[1,"error-container"],[1,"error-list"],["class","error-item",4,"ngFor","ngForOf"],[1,"error-item"]],template:function(r,o){1&r&&(V(0,"div",0)(1,"div",1)(2,"div",2)(3,"div",3)(4,"h2"),z(5,"Database Metrics"),j(),V(6,"div",4)(7,"p"),z(8),j(),V(9,"p"),z(10),j(),V(11,"p"),z(12),j(),V(13,"p"),z(14),j()()(),V(15,"div",5)(16,"h2"),z(17,"Kafka Metrics"),j(),V(18,"div",4)(19,"p"),z(20),j(),V(21,"p"),z(22),j(),V(23,"p"),z(24),j(),V(25,"p"),z(26),j(),V(27,"p"),z(28),j()()()(),V(29,"label",6),z(30,"How many last updated records to display: "),j(),V(31,"input",7),et("ngModelChange",function(s){return o.initialLastRecord2ShowNum=s}),j(),V(32,"button",8),et("click",function(){return o.updateLastRecord2ShowNum(o.initialLastRecord2ShowNum)}),z(33,"Update"),j(),V(34,"div",9)(35,"div",10)(36,"h2"),z(37,"Last Inserted Records"),j()(),V(38,"div",11)(39,"table")(40,"thead")(41,"tr")(42,"th"),z(43,"Business ID"),j(),V(44,"th"),z(45,"Source Chain"),j(),V(46,"th"),z(47,"Target Topic"),j(),V(48,"th"),z(49,"Timestamp"),j(),V(50,"th"),z(51,"messageId"),j(),V(52,"th"),z(53,"auto_id"),j(),V(54,"th"),z(55,"targetClass"),j(),V(56,"th"),z(57,"IonComponent"),j(),V(58,"th"),z(59,"Is_published"),j()()(),V(60,"tbody"),zl(61,Yj,20,12,"tr",12),j()()()(),V(62,"div",13)(63,"div",14)(64,"h2"),z(65,"Last Errors"),j()(),V(66,"div",15)(67,"div",16),zl(68,Qj,3,1,"div",17),j()()()()()),2&r&&(le(8),bt("Total Count: ",(null==o.dbMetrics?null:o.dbMetrics.totalCount)||0,""),le(2),bt("Total Inserts: ",(null==o.dbMetrics?null:o.dbMetrics.totalInserts)||0,""),le(2),bt("Failed Inserts: ",(null==o.dbMetrics?null:o.dbMetrics.failedInserts)||0,""),le(2),bt("Un-sent records: ",(null==o.dbMetrics?null:o.dbMetrics.unSendRecords)||0,""),le(6),bt("Messages Sent: ",(null==o.kafkaMetrics?null:o.kafkaMetrics.messagesSent)||0,""),le(2),bt("Successful Sends: ",(null==o.kafkaMetrics?null:o.kafkaMetrics.successfulSends)||0,""),le(2),bt("Failed Sends: ",(null==o.kafkaMetrics?null:o.kafkaMetrics.failedSends)||0,""),le(2),bt("Retry Map Size: ",(null==o.kafkaMetrics?null:o.kafkaMetrics.retryMapSize)||0,""),le(2),bt("Publisher Name: ",(null==o.kafkaMetrics?null:o.kafkaMetrics.publisherName)||0,""),le(3),mi("ngModel",o.initialLastRecord2ShowNum),le(30),mi("ngForOf",null==o.dbMetrics?null:o.dbMetrics.lastRecords),le(7),mi("ngForOf",o.errors))},dependencies:[QC,_u,nh,TE,th,ew],styles:[".content-wrapper[_ngcontent-%COMP%]{width:100%;min-height:100vh;background-color:#f3f4f6}.content-container[_ngcontent-%COMP%]{width:100%;max-width:80rem;margin:0 auto}.metrics-row[_ngcontent-%COMP%]{display:flex;gap:.75rem;margin-bottom:.75rem}.metrics-card[_ngcontent-%COMP%]{background-color:#fff;border-radius:.5rem;box-shadow:0 1px 3px #0000001a;flex:1}.metrics-card[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:1.25rem;font-weight:600;margin-bottom:1rem}.metrics-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:.5rem}.db-metrics[_ngcontent-%COMP%], .kafka-metrics[_ngcontent-%COMP%]{padding:1rem}.table-header[_ngcontent-%COMP%], .error-header[_ngcontent-%COMP%]{border-bottom:1px solid #e5e7eb;padding:1rem}.table-container[_ngcontent-%COMP%]{overflow-x:auto}table[_ngcontent-%COMP%]{width:100%;min-width:100%;border-collapse:collapse}th[_ngcontent-%COMP%]{padding:12px;text-align:left;font-size:.75rem;font-weight:500;text-transform:uppercase;color:#6b7280;background-color:#f9fafb}td[_ngcontent-%COMP%]{padding:12px;font-size:.875rem;white-space:nowrap}tr[_ngcontent-%COMP%]{border-top:1px solid #e5e7eb}.metrics-card.error-section[_ngcontent-%COMP%]{margin-top:16px}.margin-bottom-6px[_ngcontent-%COMP%]{margin-bottom:6px}.error-container[_ngcontent-%COMP%]{padding:12px}.error-list[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:12px}.error-item[_ngcontent-%COMP%]{background-color:#fef2f2;border-left:4px solid #ef4444;padding:12px}.error-item[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:.875rem;color:#b91c1c}@media (max-width: 640px){.content-container[_ngcontent-%COMP%]{width:100%}th[_ngcontent-%COMP%], td[_ngcontent-%COMP%]{padding:.75rem 1rem}}"]})}}return e})();const Xj=[{path:"",component:ch},{path:"metrics",component:ch},{path:"**",redirectTo:""}];let Jj=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275mod=Dt({type:e})}static{this.\u0275inj=it({imports:[K_.forRoot(Xj,{useHash:!0,scrollPositionRestoration:"enabled"}),K_]})}}return e})();let eB=(()=>{class e{constructor(){this.title="Ion Kafka Metrics Dashboard",this.version="1.0.0"}ngOnInit(){console.log("Application starting in production mode")}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275cmp=ms({type:e,selectors:[["app-root"]],decls:11,vars:1,consts:[[1,"min-h-screen","bg-gray-100"],[1,"bg-white","shadow-sm"],[1,"max-w-7xl","mx-auto","px-4","sm:px-6","lg:px-8"],[1,"flex","justify-between","h-16"],[1,"flex"],[1,"flex-shrink-0","flex","items-center"],[1,"text-xl","font-bold","text-gray-900"],[1,"max-w-7xl","mx-auto","sm:px-6","lg:px-8"]],template:function(r,o){1&r&&(V(0,"div",0)(1,"nav",1)(2,"div",2)(3,"div",3)(4,"div",4)(5,"div",5)(6,"h1",6),z(7),j()()()()()(),V(8,"main")(9,"div",7),Ca(10,"app-metrics-dashboard"),j()()()),2&r&&(le(7),ht(o.title))},dependencies:[ch],styles:[".app-header[_ngcontent-%COMP%]{@apply bg-white shadow-sm;}.app-content[_ngcontent-%COMP%]{@apply py-6;}"]})}}return e})(),tB=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275mod=Dt({type:e})}static{this.\u0275inj=it({providers:[rE],imports:[nw,zw,Zj]})}}return e})(),nB=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275mod=Dt({type:e,bootstrap:[eB]})}static{this.\u0275inj=it({imports:[N1,zw,Jj,tB]})}}return e})();A1().bootstrapModule(nB).catch(e=>console.error(e))}},te=>{te(te.s=988)}]);