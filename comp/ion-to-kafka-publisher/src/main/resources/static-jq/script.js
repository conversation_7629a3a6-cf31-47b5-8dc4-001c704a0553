$(document).ready(function() {
    let refreshInterval = 5000; // Default refresh interval in milliseconds (e.g., 5000ms = 5 seconds)

    // Fetch refresh interval from the backend
    $.get('/api/config/refresh-interval', function(data) {
        refreshInterval = data;
        // Initial fetch
        fetchMetrics();
        fetchErrors();
        // Set interval to refresh data
        setInterval(fetchMetrics, refreshInterval);
        setInterval(fetchErrors, refreshInterval);
    }).fail(function() {
        alert('Failed to fetch refresh interval');
    });

    function fetchMetrics() {
        // Fetch DB metrics
        $.get('/api/metrics/db', function(data) {
            $('#db-total-count').text('Total Count: ' + data.totalCount);
            $('#db-total-inserts').text('Total Inserts: ' + data.totalInserts);
            $('#db-failed-inserts').text('Failed Inserts: ' + data.failedInserts);
            const lastRecords = data.lastRecords;
            const $lastRecordsList = $('#last-records');
            $lastRecordsList.empty();
            if (lastRecords) {
                lastRecords.forEach(function(record) {
                    $lastRecordsList.append('<tr><td>' + record.businessId + '</td><td>' + record.chain + '</td><td>' + record.topic + '</td></tr>');
                });
            }
        }).fail(function() {
            alert('Failed to fetch DB metrics');
        });

        // Fetch Kafka metrics
        $.get('/api/metrics/kafka', function(data) {
            $('#kafka-messages-sent').text('Messages Sent: ' + data.messagesSent);
            $('#kafka-successful-sends').text('Successful Sends: ' + data.successfulSends);
            $('#kafka-failed-sends').text('Failed Sends: ' + data.failedSends);
            $('#kafka-retry-map-size').text('Retry Map Size: ' + data.retryMapSize);
        }).fail(function() {
            alert('Failed to fetch Kafka metrics');
        });
    }

    function fetchErrors() {
        $.get('/errors', function(data) {
            const $errorList = $('#error-list');
            $errorList.empty();
            if (data) {
                data.forEach(function(error) {
                    $errorList.append('<li class="list-group-item">' + error + '</li>');
                });
            }
        }).fail(function() {
            alert('Failed to fetch errors');
        });
    }
});
