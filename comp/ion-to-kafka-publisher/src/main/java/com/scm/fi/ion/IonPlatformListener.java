package com.scm.fi.ion;

import com.iontrading.mkv.Mkv;
import com.iontrading.mkv.MkvComponent;
import com.iontrading.mkv.MkvRecord;
import com.iontrading.mkv.MkvType;
import com.iontrading.mkv.enums.MkvFieldType;
import com.iontrading.mkv.enums.MkvPlatformEvent;
import com.iontrading.mkv.events.MkvPlatformListener;
import com.iontrading.mkv.qos.MkvQoS;
import com.scm.fi.common.util.ErrUtil;
import com.scm.fi.common.util.Jinit;
import com.scm.fi.common.util.Utils;
import com.scm.fi.configs.Ion2KafkaFieldMapping;
import com.scm.fi.db.DBThreadPool;
import com.scm.fi.db.dao.GenericDAO;
import com.scm.fi.ion.listener.ChainListener;
import com.scm.fi.ion.processor.RTDBProcessor;
import com.scm.fi.services.FieldMappingService;
import com.scm.fi.services.ShutdownService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.net.URL;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.jar.Attributes;
import java.util.jar.Manifest;

/**
 * The Purpose of this component is to work in the following modes
 * Real time mode (RT) where publications are written to a database (insert and update)
 * The above modes work handles real time publications.
 * The below SNP mode is added for ad-hoc request 

 * Chain description from SysAdmin:
 *  	CAD.CM_POSITION.POSITION_TOR.POSITION => <Region Related Currency>.<Instrument>.<Source>.<Record>
 */
@Service("IonPlatformListener")
@DependsOn("appContext")
public class IonPlatformListener implements MkvPlatformListener
{
	private static final Logger log = LoggerFactory.getLogger(IonPlatformListener.class);
	private List<Ion2KafkaFieldMapping> subs;
	private static final HashMap<String, DBThreadPool> eventPoolMap = new HashMap<>();
	private static int retryInterval = 240; // 4 minutes, in seconds (default value)
	private final FieldMappingService fieldMapping;
	private final String[] _statusFieldList = {"VerAppl"," ApplName", "CStatusStr", "CStatus"};
	private final MkvFieldType[] _statusTypeList = {
			MkvFieldType.STR,  // VerAppl
			MkvFieldType.STR,  // ApplName
			MkvFieldType.STR,  // CStatusStr
			MkvFieldType.INT   // CStatus - MATCH the platform
	};
	private String componentName = "";
	private static final String DEFAULT_VERSION = "DEV_VERSION"; // Default version if not found
	private String applicationVersion = "";

	public static DBThreadPool getEventPool(String poolName) {
		return eventPoolMap.get(poolName);
	}

	@Autowired
	public IonPlatformListener(FieldMappingService fieldMapping) {
		log.debug("@@@Service: IonPlatformListener(FieldMappingService, ApplicationContext)");
		this.fieldMapping = fieldMapping;
		DBThreadPool.setFieldMappingService(fieldMapping);
		applicationVersion = getApplicationVersionFromManifest(); // Initialize version
		componentName = Utils.getComponentName();
		log.info("componentName={}, applicationVersion={}", componentName, applicationVersion);
	}

	/**
	 * Main function to start ION API and subscribe to chain(s)
	 * @param args - arguments from command line
	 */
	public void init(String [] args)
	{
		// Add a shutdown hook to handle Ctrl+C
		Runtime.getRuntime().addShutdownHook(new Thread(() ->
				log.info("IonPlatformListener: Caught Ctrl+C, shutting down...")));
		try
		{
			MkvQoS qos = new MkvQoS();
			qos.setArgs(args);
			qos.setPlatformListeners(new MkvPlatformListener[] {this});
			Mkv mkv = Mkv.start(qos);
			log.info("Started jMkv. Version: {}, applicationVersion: {}", Mkv.getVersion(), mkv.getApplicationVersion());
			subs = fieldMapping.getMaps();
			int threadPoolSize = 1;
			try{
				threadPoolSize = Jinit.getInt("thread.pool.size");
				if(null == componentName || componentName.isEmpty())
					componentName = Utils.getComponentName();
				log.info("componentName={}, threadPoolSize configured in as {}", componentName, threadPoolSize);
			} catch(NumberFormatException e) {
				log.warn("thread.pool.size configured in system cannot be resolved to int. Using default of: {}, error={}",
						threadPoolSize, e.getMessage());
			}
			DBThreadPool.setThreadCount(threadPoolSize);
			try{
				retryInterval = Jinit.getInt("retryInterval");
				log.debug("retryInterval configured in as {}", retryInterval);
			} catch(NumberFormatException e){
				log.warn("retryInterval configured in system cannot be resolved to int. Using default of: {}", retryInterval);
			}
		}
		catch(Throwable t)
		{
			System.err.println(t);
			if(null != log)
				ErrUtil.printFullErrMsg(t, "", true);
		}
		if(null != log) {
			log.info("Init method is finished..");
		}
	} // init()

	/**
	 * Initialize the application version from the MANIFEST.MF file.
	 * This method searches for the MANIFEST.MF file in the classpath
	 * @return the application version as a String, or a default value if not found
	 */
	private static String getApplicationVersionFromManifest() {
		String version = DEFAULT_VERSION; // Default
		String expectedImplTitle = "ion-to-kafka-publisher";
		// Ensure this matches your application's actual Start-Class from MANIFEST.MF
		String expectedStartClass = "com.scm.fi.Ion2KafkaPubApp";

		try {
			ClassLoader classLoader = IonPlatformListener.class.getClassLoader();
			if (classLoader == null) {
				classLoader = ClassLoader.getSystemClassLoader();
			}
			Enumeration<URL> manifestUrls = classLoader.getResources("META-INF/MANIFEST.MF");
			while (manifestUrls.hasMoreElements()) {
				URL manifestUrl = manifestUrls.nextElement();
				try (InputStream manifestStream = manifestUrl.openStream()) {
					if (manifestStream == null) {
						log.debug("Manifest stream is null for URL: {}", manifestUrl);
						continue;
					}
					Manifest manifest = new Manifest(manifestStream);
					Attributes mainAttributes = manifest.getMainAttributes();
					String implTitle = mainAttributes.getValue("Implementation-Title");
					String startClass = mainAttributes.getValue("Start-Class");
					String currentImplVersion = mainAttributes.getValue("Implementation-Version");
					boolean titleMatch = expectedImplTitle.equals(implTitle);
					boolean startClassMatch = expectedStartClass.equals(startClass);
					boolean versionPresent = currentImplVersion != null && !currentImplVersion.isEmpty();
					// Prioritize Implementation-Title match, then Start-Class as a strong indicator for Spring Boot apps
					if ((titleMatch || startClassMatch) && versionPresent) {
						version = currentImplVersion;
						break; // Found the application's manifest and version
					}
				} catch (Exception e) {
					log.warn("Could not read or parse MANIFEST.MF at: {}. Error: {}. Skipping.", manifestUrl, e.getMessage());
				}
			}
			if (DEFAULT_VERSION.equals(version)) {
				log.warn("Could not retrieve application version from any MANIFEST.MF. Defaulting to {}. Searched for Implementation-Title: '{}' or Start-Class: '{}'",
						version, expectedImplTitle, expectedStartClass);
				// Fallback to Package method (optional, as it has its own limitations)
				Package pkg = IonPlatformListener.class.getPackage();
				if (pkg != null) {
					String implVersionPkg = pkg.getImplementationVersion();
					if (implVersionPkg != null && !implVersionPkg.isEmpty()) {
						version = implVersionPkg;
						log.info("Application version '{}' found via Package.getImplementationVersion() for package: {}", version, pkg.getName());
					}
				}
			}
		} catch (Exception e) {
			log.error("Error searching for or reading version from MANIFEST.MF. Defaulting to UNKNOWN_VERSION. {}", e.getMessage());
			version = DEFAULT_VERSION; // Ensure default on error
		}
		return version;
	} // getApplicationVersionFromManifest

	/**
	 * To monitor the state of other components in the platform.
	 * @param component - component name
	 * @param registered - true if component is registered on the platform
	 */
	@Override
	public void onComponent(MkvComponent component, boolean registered) {
		String compName = component.getName();
		if(log.isDebugEnabled() && compName.contains("KAFKA")) {
			log.debug("onComponent():{}, user: {}, host: {} : {}, type: {}, registered={}", compName,
			    component.getUserName(), component.getHostName(), component.getPort(), component.getType(), registered);
		}
	}

	/**
	 * Calls when component connects/disconnects to the router
	 * @param component - component name
	 * @param registered - true for connection
	 */
	@Override
	public void onConnect(String component, boolean registered) {
		log.info("onConnect(): Connected to Router:{}, registered={}", component, registered);
	}

	/**
	 * Function called when main events are notified to the component.
	 * The onMain() event will be called several times during the initialization of our component,
	 * one for status change. The usual sequence is START -> REGISTER -> REGISTER_IDLE -> STOP
	 * @param   event          the event notified
	 */
	@Override
	public void onMain(MkvPlatformEvent event)
	{
		log.info("onMain(): Event: {} received. {}", event, event.getCustomErrMsg());
		switch (event.intValue()) {
			case MkvPlatformEvent.START_code ->
				// The component engine has been initialized
			    log.info("START message received from platform.");
			case MkvPlatformEvent.STOP_code -> {
				// The API is stopped by the platform or by internal logic
				// After the STOP event is fired, the engine can no longer interact with the platform. The
				// application should therefore either start the API again or shut down gracefully.
				log.info("STOP message received from platform. About to shutdown");
				if (Mkv.getInstance() != null) {
					log.info("Publishing status as Not Running.");
					// "VerAppl"," ApplName", "CStatusStr", "CStatus"
					Object [] values = {applicationVersion, componentName, "Not Running", "-1" };
					supplyStatusRecord(values);
				}
				ShutdownService.shutdownGracefully(0, "Component will stop: by run 'exit 0' due to MkvPlatformEvent.STOP_code.");
			}
			case MkvPlatformEvent.REGISTER_code -> {
				// The component is active in the platform and can interact with other components in the platform
				// The component engine has been initialized
				log.info("Event REGISTER: application registered successfully.");
				// "VerAppl"," ApplName", "CStatusStr", "CStatus"
				Object [] values = {applicationVersion, componentName, "Starting", "0" };
				supplyStatusRecord(values);
				for (Ion2KafkaFieldMapping su : subs) {
					if (!su.isEnable())
						continue; // Skip disabled subscriptions
					if(null == su.getChain() || su.getChain().isEmpty()) {
						log.error("Chain name is empty for: {}. Will be skipped..", su.getName());
						continue;
					}
					try {
						eventPoolMap.put(su.getChain(), new DBThreadPool(new GenericDAO(su.getChain()), retryInterval == -1 ? -1 : retryInterval * 1000));
						ChainListener listener = new RTDBProcessor(su, fieldMapping);
						log.debug("Adding realtime listener for " + su.getName() + "->" + su.getChain());

						// Subscribe to a specific chain (getChain) with set of fields (getIonFields)
						if(su.getIonFields()[0].equals("*")) // Subscribe to all records
							Mkv.getInstance().getSubscribeManager().persistentSubscribe(su.getChain(), listener, listener);
						else
							Mkv.getInstance().getSubscribeManager().persistentSubscribe(su.getChain(), listener, listener, su.getIonFields());
						log.info("Persistent subscribe chain: " + su.getChain() + "...");
						if(log.isDebugEnabled())
							log.debug("Fields: {}", Utils.toString(su.getIonFields()));
					} catch (Exception e) {
						ErrUtil.printFullErrMsg(e, "Problem to add listener for: " + su.getChain(), true);
					}
				} //for
				// "VerAppl"," ApplName", "CStatusStr", "CStatus"
				Object [] values1 = {applicationVersion, componentName, "Running", "120" };
				supplyStatusRecord(values1);
			}
			case MkvPlatformEvent.REGISTER_IDLE_code ->
				// It follows the REGISTER events. It notifies
				// the application that the initial download has finished
				log.info("Receive event code ... REGISTER_IDLE");
			case MkvPlatformEvent.UNREGISTER_code ->
				// The component is no longer registered to the platform
				log.info("Receive event code ... UNREGISTER");
			case MkvPlatformEvent.SHUTDOWN_REQUEST_code ->
				// The component is no longer registered to the platform
				log.info("Receive event code ... SHUTDOWN_REQUEST_code");
			default -> log.warn("Unexpected MkvPlatformEvent value: {}", event.intValue());
		}
	} // onMain()

	/**
	 * Publish status record to corresponding chains in format: <Currency>.CM_STATUS.<Source>.GSTATUS,
	 * like: CAD.CM_STATUS.KAFKA_PUB_TOR.GSTATUS for Toronto
	 * @param values - record to publish
	 */
	public void supplyStatusRecord(Object[] values)
	{
		MkvRecord rec = null;
		try {
			String source = Jinit.getStr("source");
			String currency = Jinit.getStr("currency");
			String statusRecordPrefix = currency + ".CM_STATUS." + source + ".";
			String statusTypeName = source + "_CM_STATUS";
			if (Mkv.getInstance().getPublishManager().getMkvType(statusTypeName)==null){
				MkvType statusType = new MkvType(statusTypeName,
						_statusFieldList,
		    			_statusTypeList);
				statusType.publish();
			}
			rec = Mkv.getInstance().getPublishManager().getMkvRecord(statusRecordPrefix + "GSTATUS");
			if (rec==null){
				rec = new MkvRecord(statusRecordPrefix + "GSTATUS" ,statusTypeName);
				rec.publish();
			}
			rec.supply(_statusFieldList, values);
			log.info("{} chain is published fields: {}, values: {}", rec, Utils.toString(_statusFieldList), Utils.toString(values));
		} catch(Exception e) {
			ErrUtil.printFullErrMsg(e, "Exception in publishStatusRecord", true);
		}
	} // supplyStatusRecord
} // IonPlatformListener
