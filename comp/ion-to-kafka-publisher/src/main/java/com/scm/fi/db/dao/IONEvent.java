package com.scm.fi.db.dao;

import com.bns.sa.idgenerator.MessageIdGenerator;
import com.iontrading.mkv.MkvRecord;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * Event container to put in BlockingQueue<EventOperation<E>> eventQueue in order to help to store Row object to DB
 * and generate target object
 */
@Getter
@Setter
@ToString
public class IONEvent
{
    private String[]             ionFields;
    private String               tableName;
    private String[]             values;
    private String[]             dbKeys;
    private int[]                ionKeysIndexes;
    private String               mode;
    private boolean              isUpdated;
    private String               chainName;
    private String               businessIdField;
    private KafkaPub             kafkaPubObj;
    private MessageIdGenerator   msgIdGen;
    private String               targetClassName = null;
    private String               groovyMappingScript = null;
    private String               targetTopic = null;
    private String               targetControlTopic = null;
    private MkvRecord            mkvRecord = null;
    private String               validationErrors;
}
