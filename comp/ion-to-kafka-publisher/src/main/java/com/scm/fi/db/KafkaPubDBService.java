package com.scm.fi.db;

import com.scm.fi.avro.AvroJsonUtil;
import com.scm.fi.common.util.ErrUtil;
import com.scm.fi.common.util.GroovyUtils;
import com.scm.fi.db.dao.KafkaPub;
import com.scm.fi.db.repo.KafkaPubRepository;
import com.scm.fi.db.util.OracleLobHandler;
import com.scm.fi.sa.kafka.producer.MessagePK;
import com.scm.fi.services.AppContext;
import com.scotia.gcm.sa.v1.MessageHeader;
import jakarta.annotation.PostConstruct;
import jakarta.persistence.EntityManagerFactory;
import lombok.Getter;
import org.apache.avro.specific.SpecificRecordBase;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Primary;
import org.springframework.orm.jpa.JpaSystemException;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.transaction.support.TransactionTemplate;

import java.sql.SQLRecoverableException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

import static com.scm.fi.groovy.GroovyEvaluation.castClass;

@Primary
@Service
@Getter
public class KafkaPubDBService implements IKafkaPubDBService {
    private static final Logger log = LoggerFactory.getLogger(KafkaPubDBService.class);
    @Value("${app.dataRemovalHours:96}")
    private int dataRemovalHours;
    private final boolean skipDbOperations;
    @Value("${app.db.batch-size:1000}")
    private int batchSize;
    @Value("${app.db.delete-batch-size:20000}")
    private int deleteBatchSize;
    @Value("${app.ionComponentName}")
    private String ionComponentName;

    private final AtomicInteger failedInserts = new AtomicInteger();
    private final AtomicInteger succeedInserts = new AtomicInteger();

    private final KafkaPubRepository kafkaPubRepository;
    private final OracleLobHandler oracleLobHandler;
    private final EntityManagerFactory entityManagerFactory;
    private final PlatformTransactionManager transactionManager;
    private TransactionTemplate transactionTemplate;
    @Autowired
    @Lazy
    private KafkaPubDBService selfProxy;

    @Autowired
    public KafkaPubDBService(KafkaPubRepository kafkaPubRepository, OracleLobHandler oracleLobHandler,
                             EntityManagerFactory entityManagerFactory, PlatformTransactionManager transactionManager,
                             @Value("${app.skipDbOperations:false}") boolean skipDbOperations)
    {
        log.debug("@@@Service: KafkaPubDBService(KafkaPubRepository, OracleLobHandler, EntityManagerFactory, PlatformTransactionManager, skipDbOperations)");
        this.kafkaPubRepository = kafkaPubRepository;
        this.skipDbOperations = skipDbOperations;
        this.oracleLobHandler = oracleLobHandler;
        this.entityManagerFactory = entityManagerFactory;
        this.transactionManager = transactionManager;
        if (skipDbOperations)
            AppContext.setIsDBDown(true);
    }

    /**
     * Initializes the transaction management components for handling database operations,
     * particularly focused on providing isolation for the Oracle LOB fallback operations.
     * This method sets up a TransactionTemplate that will be used to execute database
     * operations in new transaction contexts when needed, particularly when recovering
     * from Oracle LOB streaming errors (ORA-22923).
     * The initialization happens once after dependency injection is complete (via @PostConstruct),
     * ensuring that the transaction template is ready before any database operations are performed.
     */
    @PostConstruct
    public void init() {
        // Create a transaction template using the application's transaction manager
        // The transaction manager was automatically configured by Spring Boot
        // based on the JPA and database configurations in your application
        this.transactionTemplate = new TransactionTemplate(transactionManager);
        // Set propagation behavior to REQUIRES_NEW, which means:
        // - A new transaction will ALWAYS be created for operations using this template
        // - If there's an existing transaction in progress, it will be suspended
        // - The new transaction will run completely independently of any existing transaction
        // - This is crucial for the LOB fallback process because it allows us to attempt
        //   a fresh operation even when the original transaction is marked for rollback
        this.transactionTemplate.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
        // Set the isolation level to READ_COMMITTED, which means:
        // - This transaction will only see data that has been committed by other transactions
        // - This prevents "dirty reads" of uncommitted data
        // - READ_COMMITTED is a good balance between data consistency and performance
        // - For Oracle, this is the default isolation level and generally appropriate for
        //   most business operations that don't require extreme isolation guarantees
        this.transactionTemplate.setIsolationLevel(TransactionDefinition.ISOLATION_READ_COMMITTED);
        log.debug("Initialized transaction template with new transaction isolation for LOB fallback operations");
    }

   /**
     * retrieve all message Ids from specific chain
     * @param chainName - chain name
     * @return list of Ids
     */
    @Override
    @Transactional(readOnly = true)
    public List<String> retrieveMessageIdsFromChain(final String chainName) {
        try {
            return kafkaPubRepository.retrieveMessageIdsFromChain(chainName);
        } catch (Exception e) {
            log.error("Error retrieving message IDs for chain {}: {}", chainName, e.getMessage());
            AppContext.setIsDBDown(true);
            return Collections.emptyList();
        }
    }

    /**
     * Checks if a KafkaPub with a given ID already exists in the repository, and if it doesn’t, it saves the KafkaPub
     * to the repository.
     * Transactional annotation is ensured that the existsById and save operations are part of the same transaction,
     * which can be important for data consistency.
     * if an error occurs while saving the KafkaPub, the transaction will be rolled back, and the database will be
     * left in a consistent state.
     * @param kafkaPub - object to store to KAFKA_PUB table
     */
    //@Async
    @Transactional
    @Retryable(value = {SQLRecoverableException.class, JpaSystemException.class},
            maxAttempts = 3, backoff = @Backoff(delay = 1000,      // 1 second initial delay
                                                multiplier = 2.0,  // exponential backoff
                                                maxDelay = 10000)) // max 10 seconds between retries
    public void insertUniqueKafkaPub(final KafkaPub kafkaPub, final SpecificRecordBase targetObj) {
        KafkaPub insertedObj = null;
        String targetObject = "", chainFields = "", messageId = "";
        try {
            MessageHeader header = (MessageHeader)targetObj.get(0);
            messageId = header.getMessageId().toString();
            // Check if the record already exists
            long countRecordsWithMessageId = kafkaPubRepository.countRecordsWithMessageId(messageId);
            if (countRecordsWithMessageId > 0) {
                log.warn("KafkaPub with id {} already exists (Rec#{}), skipping insert", messageId, countRecordsWithMessageId);
                return;
            }
            // Validate fields
            targetObject = kafkaPub.getTargetObject();
            chainFields = kafkaPub.getChainFields();
            if (targetObject == null || targetObject.trim().isEmpty()) {
                failedInserts.incrementAndGet();
                log.error("KafkaPub with id {} has empty LOB (targetObject) fields, skipping insert", messageId);
                return;
            }
            else if(chainFields == null || chainFields.trim().isEmpty()) {
                failedInserts.incrementAndGet();
                log.error("KafkaPub with id {} has empty LOB (chainFields) fields, skipping insert", messageId);
                return;
            }
            // Try standard JPA approach
            insertedObj = kafkaPubRepository.save(kafkaPub);
            succeedInserts.incrementAndGet();
            return; //insertedObj;
        }
        catch(JpaSystemException ex) {
            if(ex.getMessage().contains("ORA-22923")) {
                // The transaction is already marked for rollback at this point
                // We need to completely exit this transaction before trying the fallback
                // Set variables for the fallback method
                final String finalMessageId = messageId;
                final String finalTargetObject = targetObject;
                final String finalChainFields = chainFields;
                final KafkaPub finalKafkaPub = kafkaPub;
                // Force transaction rollback now
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                // Schedule a new attempt outside this transaction
                transactionTemplate.execute(status -> {
                    try {
                        log.warn("Failed to insert KafkaPub with id {} due to ORA-22923 (error is misleading since all blob fields are not empty): {} will try alternative JDBC approach...", finalMessageId, ex.getMessage());
                        KafkaPub insertedObj2 = oracleLobHandler.insertKafkaPubWithJDBC(finalKafkaPub, finalMessageId);
                        if(null == insertedObj2) {
                            failedInserts.incrementAndGet();
                            AppContext.setIsDBDown(true);
                            String blobSizes = "";
                            if (null != finalChainFields && null != finalTargetObject) {
                                blobSizes = String.format("BLOB fields sizes are: CHAINFIELDS: %d, TARGETOBJECT: %d.",
                                        finalChainFields.length(), finalTargetObject.length());
                            }
                            // java.sql.SQLException: ORA-22923: amount of data specified in streaming LOB write is 0
                            // TODO: - investigate this issue - since data for BLOB field is not empty!
                            log.error("""
                                    Incorrect Oracle error message since all LOB fields are not empty!
                                    Oracle Error: {}.
                                    Cause: This error can occur after upgrading the database to a newer version, such as Oracle 19C.
                                    Action: Apply relevant Oracle patches or configurations to address the issue. For example,
                                    Oracle has provided an interim fix identified as SR 3-22628328201 for this issue. {}""", ex.getMessage(), blobSizes);
                            return null;
                        }
                        succeedInserts.incrementAndGet();
                        return null; // We're not using the return value
                    } catch (Exception e) {
                        log.error("JDBC fallback attempt failed with: {}", e.getMessage(), e);
                        failedInserts.incrementAndGet();
                        return null;
                    }
                });
                return;
            }
            // If not ORA-22923, propagate the exception
            failedInserts.incrementAndGet();
            AppContext.setIsDBDown(true);
            return ;
        }
        catch (Exception e) {
            failedInserts.incrementAndGet();
            AppContext.setIsDBDown(true);
            return;
        }
    } // insertUniqueKafkaPub

    // Recovery method to handle when all retries are exhausted
    @Recover
    public KafkaPub recoverFromDatabaseFailure(Exception e, KafkaPub kafkaPub, SpecificRecordBase targetObj) {
        log.error("All retry attempts failed. Marking database as down.", e);
        AppContext.setIsDBDown(true);
        return null;
    }
    /*@Recover
    public KafkaPub recoverFromDatabaseFailure(SQLRecoverableException e, KafkaPub kafkaPub, SpecificRecordBase targetObj) {
        log.error("All retry attempts failed for database operation after 3 attempts, switching to permanent failover mode", e);
        AppContext.setIsDBDown(true);
        return null;
    }*/
    /**
     * Delete all records from KAFKA_PUB table before timestamp
     * @param timestamp - TS for records to delete
     */
    public long deleteOldRecords(LocalDateTime timestamp, String ionComponentName) {
        long totalDeleted = 0;
        int deleted;
        try {
            log.info("deleteBatchSize: {}", deleteBatchSize);
            do {
                // Call the method in the proxy to ensure it runs in a new transaction
                deleted = selfProxy.deleteBatchInSeparateTransaction(timestamp, ionComponentName, deleteBatchSize);
                totalDeleted += deleted;
                if (deleted > 0) {
                    log.info("Deleted {} records in this batch (total so far: {})", deleted, totalDeleted);
                }
            } while (deleted == deleteBatchSize);
            return totalDeleted;
        } catch (Exception e) {
            ErrUtil.printFullErrMsg(e, "Error deleting records from KAFKA_PUB table for component: " + ionComponentName, true);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return totalDeleted;
        }
    }

    /**
     * Delete records from KAFKA_PUB table in separate transaction
     * @param timestamp - TS for records to delete
     * @param ionComponentName - ion component name
     * @param batchSize - batch size for delete operation
     * @return - number of deleted records
     */
    @Transactional(
            propagation = Propagation.REQUIRES_NEW, // Creates a new transaction regardless of any existing one,ensuring that the transaction behavior is isolated.
            rollbackFor = Exception.class
    )
    protected int deleteBatchInSeparateTransaction(LocalDateTime timestamp, String ionComponentName, int batchSize) {
        try {
            return kafkaPubRepository.deleteByTimestampBeforeBatch(timestamp, ionComponentName, batchSize);
        } catch (Exception e) {
            log.error("Error in batch deletion: {}", e.getMessage());
            throw e; // Re-throw to allow transaction rollback
        }
    }

    /**
     * Get total number of records from KAFKA_PUB table
     * @return - records#
     */
    public long getTotalRecords(String ionComponentName) {
        try {
            return kafkaPubRepository.countAllRecords(ionComponentName);
        } catch (Exception e) {
            log.error("Error getting total records from KAFKA_PUB table: {}", e.getMessage());
            //AppContext.setIsDBDown(true);
            return 0;
        }
    }

    /**
     * Get the total number of records from KAFKA_PUB table that are not published yet
     * @param ionComponentName - ion component name
     * @return - unpublished records#
     */
    public long countUnPublishedRecords(String ionComponentName) {
        try {
            return kafkaPubRepository.countUnPublishedRecords(ionComponentName);
        } catch (Exception e) {
            log.error("Error getting total records from KAFKA_PUB table: {}", e.getMessage());
            //AppContext.setIsDBDown(true);
            return 0;
        }
    }

    public long getCount() {
        try {
            return kafkaPubRepository.count();
        }  catch (Exception e) {
            log.error("Error getting total records from KAFKA_PUB table: {}", e.getMessage());
            //AppContext.setIsDBDown(true);
            return 0;
        }
    }

    public int getFailedInsertsCount() {
        return failedInserts.get();
    }
    public int getSucceedInsertsCount() {
        return succeedInserts.get();
    }

    public List<KafkaPub> getLastRecords(final long count, final String ionComponentName) {
        try {
            return kafkaPubRepository.getLastRecords(count, ionComponentName);
        } catch (Exception e) {
            log.error("Error getting last records from KAFKA_PUB table: {}", e.getMessage());
            AppContext.setIsDBDown(true);
            return Collections.emptyList();
        }
    }

    public List<KafkaPub> getChainsToRepublish(final int offset, final int batchSize, final String ionComponentName) {
        try {
            return kafkaPubRepository.getChainsToRepublish(offset, batchSize, ionComponentName);
        } catch (Exception e) {
            log.error("Error getting chains to republish from KAFKA_PUB table: {}", e.getMessage());
            AppContext.setIsDBDown(true);
            return Collections.emptyList();
        }
    }
    public List<KafkaPub> getAllChainsToRepublish(final int batchSize, final String ionComponentName) {
        try {
            return kafkaPubRepository.getAllChainsToRepublish(batchSize, ionComponentName);
        } catch (Exception e) {
            log.error("Error getting chains to republish from KAFKA_PUB table: {}", e.getMessage());
            AppContext.setIsDBDown(true);
            return Collections.emptyList();
        }
    }

    /**
     * Delete records from KAFKA_PUB table according to app.dataRemovalHours parameter:
     * How many hours to keep records for redundancy in KAFKA_PUB table
     * dataRemovalHours - To remove records from KAFKA_PUB that older than (in hours)
     * @return - how many records were deleted
     */
    // @Transactional(timeout = 180) // 3 minute timeout
    public long deleteOldKafkaPubRecords()
    {
        long deletedRecords = 0;
        try {
            long lRecordNum = getTotalRecords(ionComponentName);
            // Convert LocalDateTime to Timestamp
            log.info("Remove records from that older than: {} hours. Before delete: Rec# {}", dataRemovalHours, lRecordNum);
            LocalDateTime threshold = LocalDateTime.now().minusHours(dataRemovalHours);
            deletedRecords = deleteOldRecords(threshold, ionComponentName);
            lRecordNum = getTotalRecords(ionComponentName);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd hh:mm:ss");
            log.info("Ion Component: {}: Deleted {} records from KAFKA_PUB table. All records before: {} TS are removed. After delete Rec# {}",
                    ionComponentName, deletedRecords, threshold.format(formatter), lRecordNum);
            return deletedRecords;
        } catch (Exception e) {
            log.error("Error deleting old records from KAFKA_PUB table: {}", e.getMessage(), e);
            AppContext.setIsDBDown(true);
            // Force transaction rollback
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return 0;
        }
    }

    /**
     * Get the full list of KafkaPub records to re-publish to Kafka (was newer confirmed to be successfully published)
     * @return list of objects to publish
     */
    //@Transactional(readOnly = true)
    public HashMap<MessagePK, SpecificRecordBase> getObjectsToRepublish()
    {
        if(skipDbOperations) {
            return null;
        }
        HashMap<MessagePK, SpecificRecordBase> mapToRepublish = new HashMap<>();
        int offset = 0;
        try {
                while (true) {
                    List<KafkaPub> batch = getChainsToRepublish(offset, batchSize, ionComponentName);

                    if (null == batch || batch.isEmpty()) {
                        break;
                    }
                    for (KafkaPub pub : batch) {
                        MessagePK messagePK = new MessagePK(pub.getChain(), pub.getTopic(), pub.getMessageId());
                        Class<?> targetClass = GroovyUtils.str2TargetClass(pub.getTargetClass());
                        SpecificRecordBase targetObj = AvroJsonUtil.jsonToAvroObj(pub.getTargetObject(), castClass(targetClass));
                        mapToRepublish.put(messagePK, targetObj);
                        log.debug("Step#1: Target Object created from DB: for messageId={}, businessId={}, currentTS={}, targetClass={}",
                                pub.getMessageId(), pub.getBusinessId(), pub.getTs(), pub.getTargetClass());
                    }
                    offset += batch.size();
                    //Utils.sleepForMS(0, 10);
                } //while
                return mapToRepublish;
            }
            catch(Exception ex)
            {
                log.error(ErrUtil.printFullErrMsg(ex, "Failed to get ChainsToRepublish", true));
                AppContext.setIsDBDown(true);
            }
        return null;
    } //getChainsToRepublish

    /**
     * Update Kafka_pub record after it was successfully published to Kafka topic (IS_PUBLISHED field set from 0 to 1)
     * @param messageId - PK of record to update
     * @return - number of updated records
     */
    @Transactional(
            propagation = Propagation.REQUIRES_NEW, // Creates a new transaction regardless of any existing one,ensuring that the transaction behavior is isolated.
            rollbackFor = Exception.class  // Without this,Spring would attempt to commit the transaction even after catching the exception
    )
    public int updatePublishedStatus(final String messageId) {
        try {
             int iUpdated = kafkaPubRepository.updatePublishedStatus(messageId);
             if(iUpdated <= 0)
                 log.warn("Publishing status was not updated for: messageId={}.", messageId);
            return iUpdated;
        }
        catch(Exception ex) {
            ErrUtil.printFullErrMsg(ex,"Failed to update KAFKA_PUB table. messageId="+messageId, true);
            AppContext.setIsDBDown(true);
            // Force transaction rollback
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
        return 0;
    }
} // KafkaPubDBService()
