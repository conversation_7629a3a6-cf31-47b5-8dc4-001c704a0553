package com.scm.fi;

import com.scm.fi.common.util.ErrUtil;
import com.scm.fi.services.ShutdownService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.WebApplicationType;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.server.WebServerException;
import org.springframework.context.ApplicationContextException;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/*
 * Main application class for the Ion to Kafka Publisher component.
 * Related page: https://confluence.agile.bns/pages/viewpage.action?pageId=780770282
 * <p>
 * This Spring Boot 3.x application (using RedHat OpenJDK 17) is responsible for publishing data from Ion Chains to Kafka topics.
 * <ul>
 *   <li>Reads configuration from various files for Ion Chains and Kafka topics.</li>
 *   <li>Supports asynchronous processing, retry logic, and scheduled tasks.</li>
 *   <li>Enables JPA repositories for database operations.</li>
 * </ul>
 * <p>
 * Usage:
 * <ul>
 *   <li>Command line: -init config/mmi-tor/ist/mkv_KAFKA_PUB_TOR.jinit --spring.profiles.active=tor-dev --spring.config.location=file:./config/mmi-tor/ist/</li>
 *   <li>VM Parameters: Various security and Kafka-related properties (see class-level comments).</li>
 * </ul>
 */
@SpringBootApplication
@EnableAsync
@EnableRetry
@EnableScheduling
@EnableJpaRepositories(basePackages = "com.scm.fi.db.repo")
public class Ion2KafkaPubApp
{
    private static final Logger log = LoggerFactory.getLogger(Ion2KafkaPubApp.class);
    private static SpringApplication app;

    /**
     * Main entry point for the Ion2KafkaPubApp.
     * <p>
     * Starts the Spring Boot application. If the web server fails to start, attempts to restart in non-web mode.
     * All exceptions are logged, and fatal errors are printed to System.err for error file capture.
     *
     * @param args command-line arguments passed to the application
     */
    public static void main(String [] args)
    {
        log.info("Main Method start");
        try {
            runApp(args, WebApplicationType.SERVLET);
        } catch (WebServerException | ApplicationContextException e) {
            String fullErrorMsg = ErrUtil.printFullErrMsg(e, "", true);
            // Explicitly log to System.err to catch to errors.log file
            System.err.println("Application Startup Error: " + fullErrorMsg);
            if(e.getClass().toString().equals("class org.springframework.context.ApplicationContextException") && !e.getMessage().contains("webServerStartStop")) {
                log.error(fullErrorMsg);
                ShutdownService.shutdownGracefully(1, "Application Startup Error");
            }
            log.warn("Web server failed to start: {}: {}. Restarting in non-web mode...",
                    e.getMessage(), e.getCause() != null ? e.getCause().getMessage() : "");
            try {
                // Restart without web server
                runApp(args, WebApplicationType.NONE);
            } catch (Exception ex) {
                logAndExit(ex);
            }
        } // catch (WebServerException | ApplicationContextException e)
        catch (Exception ex) {
            logAndExit(ex);
        }
    } // main()

    private static void runApp(String[] args, WebApplicationType type) {
        app = new SpringApplication(Ion2KafkaPubApp.class);
        app.setWebApplicationType(type);
        app.run(args);
        log.info("Application {} started successfully. profiles# {}",
                app.getMainApplicationClass().getCanonicalName(), app.getAdditionalProfiles().size());
    }

    private static void logAndExit(Exception ex) {
        String fullErrorMsg = ErrUtil.printFullErrMsg(ex, "", true);
        System.err.println("Application Startup Error: " + fullErrorMsg);
        log.error(fullErrorMsg);
        throw new RuntimeException(ex);
    }
} // Ion2KafkaPubApp
