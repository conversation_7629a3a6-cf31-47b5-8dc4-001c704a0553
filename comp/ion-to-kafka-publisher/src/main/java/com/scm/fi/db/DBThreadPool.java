package com.scm.fi.db;

import com.scm.fi.common.util.ErrUtil;
import com.scm.fi.common.util.GroovyUtils;
import com.scm.fi.common.util.Utils;
import com.scm.fi.configs.Ion2KafkaFieldMapping;
import com.scm.fi.db.dao.DBDAO;
import com.scm.fi.db.dao.IONEvent;
import com.scm.fi.db.dao.KafkaPub;
import com.scm.fi.exceptions.GroovyRunException;
import com.scm.fi.groovy.GroovyEvaluation;
import com.scm.fi.sa.kafka.producer.KafkaCounters;
import com.scm.fi.sa.kafka.serializers.plugins.IonToKafkaAvroSerializer;
import com.scm.fi.sa.kafka.serializers.plugins.Row;
import com.scm.fi.sa.kafka.serializers.plugins.control.ControlTopic;
import com.scm.fi.services.AppContext;
import com.scm.fi.services.FieldMappingService;
import com.scm.fi.services.KafkaPublisherService;
import com.scotia.gcm.sa.v1.MessageHeader;
import com.scotia.gcm.sa.v1.control.ControlMessage;
import org.apache.avro.AvroRuntimeException;
import org.apache.avro.specific.SpecificRecordBase;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.sql.SQLException;
import java.text.ParseException;
import java.util.*;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.atomic.AtomicLong;

/*
 * Use this class to delegate DB related actions on a separate thread pool
 * Use a separate instance for each DAO/Event type
 *
 * The main class for handling database-related actions in a multithreading environment.
 * It's parameterized with a type E that extends Event.
 */
@Service("DBThreadPool")
public class DBThreadPool
{
    private static final Logger log = LoggerFactory.getLogger(DBThreadPool.class);

    // Thread array to run database operations and Kafka publications
    private Thread[] dbThreads = null;
    // Max number of threads in the pool
    private static int MAX_THREAD_COUNT=50;
    // List to hold trades that need to be retried
    private final List<EventOperation> retryList = new ArrayList<>();
    // Timer for a retry mechanism
    private Timer timer;
    // Array of DAOs for database operations
    private DBDAO[] daoThreads;
    // Time interval for retries
    private int retryTimeInterval = -1;
    private IonToKafkaAvroSerializer ionToKafkaAvroSerializer = null;
    private static KafkaPubDBService kafkaPubDBService;
    private static KafkaPublisherService kafkaPublisherService;
    private static FieldMappingService fieldMappingService;
    private static boolean isAddSourceTag; // Add source tag (with chain name) to the landing (Row) object
    private static boolean isEnsureUnique; // Ensure unique events in the queue
    private boolean skipDbOperations = false;
    private static final int LOOP_DELAY_MS = 0;
    private static final int LOOP_DELAY_NS = 0;
    // One-minute delay before RetryTimer starts 1st time
    private static final int FIRST_TIME_DELAY = 60000;
    private final AtomicLong totalEventsAdded = new AtomicLong(0);
    private final AtomicLong totalEventsProcessed = new AtomicLong(0);
    private KafkaCounters kafkaControlCounters;
    /**
     * Set to track events currently in the queue to prevent duplicates
     * Uses ConcurrentHashMap.newKeySet() for thread-safe operations with O(1) lookup
     */
    private final Set<EventOperation> queuedEvents = ConcurrentHashMap.newKeySet();
    // A queue to hold database operations
    private BlockingQueue<EventOperation> eventQueue;

    @Autowired
    public DBThreadPool(KafkaPubDBService kafkaPubDBService, KafkaPublisherService kafkaPublisherService,
                        @Value("${app.config.add-source-tag:true}") boolean isAddSourceTag,
                        @Value("${app.config.ensure-unique:false}") boolean isEnsureUnique) {
        log.debug("@@@Service: DBThreadPool(KafkaPubDBService, KafkaPublisherService, app.config.add-source-tag, app.config.ensure-unique)");
        DBThreadPool.kafkaPubDBService = kafkaPubDBService;
        DBThreadPool.kafkaPublisherService = kafkaPublisherService;
        DBThreadPool.isAddSourceTag = isAddSourceTag;
        DBThreadPool.isEnsureUnique = isEnsureUnique;
        this.ionToKafkaAvroSerializer = null;  // Will be initialized in another constructor
    }

    public int getQueuedEventsSize() { return queuedEvents.size(); }
    public int getEventQueueSize() { return eventQueue.size(); }

    public static void setFieldMappingService(FieldMappingService fieldMappingService) {
        DBThreadPool.fieldMappingService = fieldMappingService;
    }

    // Constructor that initializes the thread pool and DAOs
    @SuppressWarnings("unchecked")
    public DBThreadPool(int threadCount, DBDAO dao, int retryInterval) throws Exception {
        ionToKafkaAvroSerializer = new IonToKafkaAvroSerializer(dao.getChainName());
        dbThreads = new Thread[threadCount];
        daoThreads = new DBDAO[threadCount];
        eventQueue =  new LinkedBlockingQueue<EventOperation>();
        skipDbOperations = AppContext.getBooleanProperty("app.skipDbOperations", false);
        kafkaControlCounters = new KafkaCounters();
        log.debug("Initializing db thread pool count = {}", threadCount);
        for (int i = 0; i < threadCount; i++) {
            daoThreads[i] = dao.getClass().getDeclaredConstructor().newInstance();
            daoThreads[i].setChainName(dao.getChainName());
            dbThreads[i] = new Thread(new DAOQueue(daoThreads[i]));
            dbThreads[i].setPriority(Thread.NORM_PRIORITY-1);
            String newThreadName = dao.getChainName() + "-" + dbThreads[i].getName();
            dbThreads[i].setName(newThreadName);
            log.debug("#{} from {}: Start new thread: {}", (i+1), threadCount, newThreadName);
            dbThreads[i].start();
        }
        retryTimeInterval = retryInterval;
        if (retryInterval !=-1){
            timer = new Timer();
            // One-minute delay before RetryTimer starts 1st time
            timer.scheduleAtFixedRate(new RetryTimer(), FIRST_TIME_DELAY, retryInterval);
        }
    } // DBThreadPool

    public DBThreadPool(DBDAO dao, int retry) throws Exception{
        this(MAX_THREAD_COUNT, dao, retry);
    }

    public static void setThreadCount(int count){
        MAX_THREAD_COUNT = count;
    }

    // Inner class to execute DAO operations in a separate thread
    public class DAOQueue implements Runnable {
        private DBDAO eventDao;
        private boolean isStopThread = false;
        private Ion2KafkaFieldMapping map;
        private String chainName;
        private String ionComponentName;
        private KafkaCounters kafkaCounters;

        DAOQueue(DBDAO dao) {
            this.eventDao = dao;
            this.chainName = dao.getChainName();
            this.map = fieldMappingService.getMap(this.chainName);
            //this.ionComponentName = DBThreadPool.kafkaPubDBService.getIonComponentName();
            this.ionComponentName = Utils.getComponentName();
            if(ionComponentName == null || ionComponentName.isEmpty()){
                log.warn("app.ionComponentName is not set inside YAML file.");
            }
            this.kafkaCounters = new KafkaCounters();
            AppContext.setKafkaCounters(kafkaCounters);
            Runtime.getRuntime().addShutdownHook(new Thread(() ->
            {log.info("DAOQueue: Caught Ctrl+C, shutting down..."); isStopThread = true;} ));
        }

        /**
         * Method to run the DAOQueue thread to wait events from the queue to be processed
         */
        public void run() {
            log.info("Spawning thread: {} and wait for event in eventQueue. Chain: {}/{}, eventQueue size={}",
                    Thread.currentThread().getName(), eventDao.getChainName(), map.getChain(), eventQueue.size());
            
            // Compile Groovy script for a corresponding topic and add to the map if it was not done before (cashing)
            GroovyEvaluation gEvaluator = null;
            try {
                gEvaluator = kafkaPublisherService.addGroovyEvaluator(map);
                if(null == gEvaluator && !GroovyEvaluation.isUseNoGroovyScript(map.getGroovyMappingScript())){
                    log.error("Failed to build/parse Groovy Script: {}", map.getGroovyMappingScript());
                    return;
                }
            } catch(Exception ex){
                ErrUtil.printFullErrMsg(ex,"Failed to build/parse Groovy Script: " + map.getGroovyMappingScript(), true );
                return;
            }
            IONEvent trEvent = null;
            EventOperation trTypeEvent = null;


            while (true) {
                boolean isPublishFailed = false;
                Row rowObject = null;
                try {
                    trTypeEvent = takeFromQueue();
                    if (!trTypeEvent.trEvent.getClass().getTypeName().equals("com.scm.fi.db.dao.IONEvent")) {
                        log.error("Unexpected event: expected: com.scm.fi.db.dao.IONEvent but get: " + trTypeEvent.trEvent.getClass().getTypeName());
                        continue;
                    }
                    trEvent = trTypeEvent.trEvent;
                    String targetClassName = trEvent.getTargetClassName();
                    String groovyMappingScript = trEvent.getGroovyMappingScript();
                    if (isStopThread) {
                        break;
                    }
                    Class<?> targetClass = GroovyUtils.str2TargetClass(targetClassName);
                    log.debug("targetClass: {}/{}, groovyMappingScript: {}, eventQueue size={}",
                            targetClassName, targetClass.getCanonicalName(), groovyMappingScript, eventQueue.size());

                    //trEvent.setMsgIdGen(msgIdGen);
                    // "mapping/position/position-tor.groovy", PositionMessage.class
                    rowObject = ionToKafkaAvroSerializer.rowObjectCreation(map, trEvent.getValues(), DBThreadPool.isAddSourceTag);
                    SpecificRecordBase targetObj;
                    //if(GroovyEvaluation.isUseNoGroovyScript(gEvaluator.getGroovyMappingScriptName())) {
                    if(GroovyEvaluation.isUseRowObject(groovyMappingScript)) {
                        targetObj = rowObject; // Do not evaluate a target object since it suppose to be Row object
                    }
                    else if(GroovyEvaluation.isPlugIn(groovyMappingScript)) {
                        // Use plug-in class to generate a target object
                        Class<?> plugInClass = IonToKafkaAvroSerializer.getPlugInClass(groovyMappingScript);
                        targetObj = IonToKafkaAvroSerializer.buildTargetObjectFromPlugIn(plugInClass, rowObject, map.getRegion());
                    }
                    else {
                        targetObj = gEvaluator.targetObjectBuild(rowObject); // Object to publish to Kafka
                    }
                    assignSecondarySourceSystem(rowObject, targetObj);
                    log.debug("Target object evaluated: class: {}/{}, Script: {}, eventQueue size={}, secondarySourceSystem={}.",
                            targetObj.getClass(), targetClassName, groovyMappingScript, eventQueue.size(),
                            ((MessageHeader) targetObj.get(0)).getSecondarySourceSystem());

                    // Insert a target object to DB before sending to Kafka for reconciliation and redundancy purpose
                    if(!AppContext.isDBDown() && !skipDbOperations) {
                        log.debug("Insert kafkaPubObj with MessageId={} to DB", rowObject.getHeader().getMessageId());
                        KafkaPub kafkaPubObj = (new KafkaPub()).setKafkaPub(rowObject, targetObj, trEvent.getTargetTopic(), trEvent.getTargetClassName(), ionComponentName);
                        kafkaPubDBService.insertUniqueKafkaPub(kafkaPubObj, targetObj);
                        /*if(null != insertedObj)
                            log.debug("kafkaPubObj with MessageId={} inserted to DB. auto_id={}.",
                                    insertedObj.getMessageId(), insertedObj.getAuto_id());
                        else
                            log.error("kafkaPubObj with MessageId={} NOT inserted to DB due to DB error. businessId={}.",
                                    kafkaPubObj.getMessageId(), kafkaPubObj.getBusinessId());*/
                    }
                    if(trEvent.getValidationErrors() != null && !trEvent.getValidationErrors().isEmpty()){
                        ControlTopicCreation(trEvent, rowObject, null, map);
                    }
                    kafkaPublisherService.publishObjectToKafka(targetObj, map, kafkaCounters, map.getTargetTopic());
                }
                catch(AvroRuntimeException ex){ // Exceptions to create ControlTopic topic record
                    ErrUtil.printFullErrMsg(ex, "AvroRuntimeException in DBThreadPool", true);
                    isPublishFailed = true;
                    ControlTopicCreation(trEvent, rowObject, ex, map);
                }
                catch (GroovyRunException | ClassNotFoundException ex){
                    ErrUtil.printFullErrMsg(ex, "", true);
                } catch(SQLException ex){
                    ErrUtil.printFullErrMsg(ex, "Could not update database for tradeId="
                            + trEvent.getBusinessIdField() + ", Put in retry queue", true);
                    isPublishFailed = true;
                } catch(InterruptedException ex) {
                    // is caught, but the thread's interrupted status is not restored => can lead to lost interruption signals.
                    Thread.currentThread().interrupt(); // Restore the interrupted status
                    ErrUtil.printFullErrMsg(ex, "InterruptedException in DBThreadPool", true);
                    isPublishFailed = true;
                } catch(Exception ex){
                    ErrUtil.printFullErrMsg(ex, "Exception in DBThreadPool", true);
                    isPublishFailed = true;
                    // Don't break the loop for other exceptions
                    Utils.sleepForMS(1000, 0); // Brief pause before retry
                }
                finally {
                    // remove from the tracking set, even if processing fails
                    if(DBThreadPool.isEnsureUnique)
                        queuedEvents.remove(trTypeEvent);
                }
                // Republish record in case of any Fatal Error
                if(isPublishFailed && retryTimeInterval!=-1) {
                    retryList.add(trTypeEvent);
                }
                Utils.sleepForMS(LOOP_DELAY_MS, LOOP_DELAY_NS); // to let another publisher go ahead in case of small amount cores
            } // while
        } // run
    } // DAOQueue

    /**
     * set SecondarySourceSystem field in message header
     * @param rowObject - source (landing) object
     * @param targetObj - target object to publish to Kafka
     */
    private static void assignSecondarySourceSystem(Row rowObject, SpecificRecordBase targetObj) {
        if(null == rowObject || null == targetObj) {
            log.error("null == rowObject || null == targetObj");
            return;
        }
        MessageHeader messageHeader = (MessageHeader) targetObj.get(0);
        CharSequence secSrcSys = rowObject.getHeader().getSecondarySourceSystem();
        String saSecondarySourceSystem;
        if (null == secSrcSys)
            saSecondarySourceSystem = null;
        else
            saSecondarySourceSystem = secSrcSys.toString().trim();
        messageHeader.setSecondarySourceSystem(saSecondarySourceSystem);
    }

    /**
     * Method to create ControlTopic topic record. It Depended on control-topic-publish YAML parameter
     * @param trEvent - IONEvent object
     * @param rowObject - Row source object to store in a control topic
     * @param ex - Exception to a store-to-control topic
     */
    private void ControlTopicCreation(IONEvent trEvent, Row rowObject, Exception ex, Ion2KafkaFieldMapping map) {
        if(null == trEvent)
        {
            log.error("ControlTopicCreation: trEvent is null. Can not create ControlTopic topic record.");
            return;
        }
        String source = IonToKafkaAvroSerializer.getSaMessageIdPrefix();
        String topic = trEvent.getTargetControlTopic();
        Exception exception = ex;
        if(null == ex)
            exception = new ParseException(trEvent.getValidationErrors(), 0);
        ControlTopic controlTopic = new ControlTopic();
        ControlMessage ctrlMsg = controlTopic.controlMessage(trEvent.getBusinessIdField(), source, topic, rowObject, exception);
        kafkaPublisherService.publishObjectToKafka(ctrlMsg, map, kafkaControlCounters, topic);
    } // ControlTopicCreation()

    /**
     * Method to take an event from the queue
     * @return - EventOperation object
     * @throws InterruptedException
     */
    public EventOperation takeFromQueue() throws InterruptedException {
        EventOperation event = eventQueue.take();
        if (event != null) {
            totalEventsProcessed.incrementAndGet();
            log.debug("After event extraction - EventQueue size: {}, Total added: {}, Total processed: {}",
                    eventQueue.size(), totalEventsAdded.get(), totalEventsProcessed.get())        ;
        }
        return event;
    }

    /**
     * Method to add operations to the queue
     * @param record - IONEvent object
     * @throws Exception
     */
    public void addToQueue(IONEvent record) throws Exception {
        if (record == null) {
            log.warn("Attempted to add null IONEvent to queue");
            return;
        }
        EventOperation tmp  = new EventOperation(record);
        this.addToQueue(tmp);
    } // addToQueue

    /**
     * Adds an event operation to the processing queue if it's not already in the queue
     * Uses efficient O(1) duplicate detection with a concurrent set
     * @param tradeEventType The event operation to add to the queue
     * @throws Exception If there's an error adding to the queue
     */
    public void addToQueue(EventOperation tradeEventType) throws Exception {
        if (tradeEventType == null) {
            log.warn("Attempted to add null EventOperation to queue");
            return;
        }
        // Use the object's built-in hashCode/equals for efficient duplicate detection
        if (!DBThreadPool.isEnsureUnique || queuedEvents.add(tradeEventType)) { // returns true if added (not a duplicate)
            try {
                // Add to the actual processing queue
                eventQueue.put(tradeEventType);
                totalEventsAdded.incrementAndGet();
                log.debug("After adding - Queue size: {}, Total added: {}, Total processed: {}",
                        eventQueue.size(), totalEventsAdded.get(), totalEventsProcessed.get());
            } catch (InterruptedException e) {
                // If we couldn't add to the queue, remove from the tracking set
                if(DBThreadPool.isEnsureUnique)
                    queuedEvents.remove(tradeEventType);
                Thread.currentThread().interrupt();
                throw e;
            }
        } else {
            // This is a duplicate event
            log.debug("Duplicate event not added to queue. Id: {}.", tradeEventType.trEvent.getMkvRecord().toString());
        }
    } // addToQueue`

    // Inner class to encapsulate event operations
    private static class EventOperation {
        private IONEvent trEvent;
        EventOperation(IONEvent event) {
            this.trEvent = event;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            EventOperation that = (EventOperation) o;
            // Compare all relevant fields of IONEvent
            if (trEvent == null && that.trEvent == null) return true;
            if (trEvent == null || that.trEvent == null) return false;
            return  Objects.equals(trEvent.getTargetTopic(), that.trEvent.getTargetTopic()) &&
                    Objects.equals(trEvent.getChainName(), that.trEvent.getChainName()) &&
                    Arrays.equals(trEvent.getValues(), that.trEvent.getValues()) &&
                    Objects.equals(trEvent.getBusinessIdField(), that.trEvent.getBusinessIdField()) &&
                    Objects.equals(trEvent.getTableName(), that.trEvent.getTableName()) &&
                    Objects.equals(trEvent.getTargetClassName(), that.trEvent.getTargetClassName()) &&
                    Objects.equals(trEvent.getGroovyMappingScript(), that.trEvent.getGroovyMappingScript()) &&
                    Arrays.equals(trEvent.getDbKeys(), that.trEvent.getDbKeys()) &&
                    Objects.equals(trEvent.getTargetControlTopic(), that.trEvent.getTargetControlTopic()) &&
                    Arrays.equals(trEvent.getIonFields(), that.trEvent.getIonFields());
        }

        @Override
        public int hashCode() {
            int result = 17;
            if (trEvent != null) {
                result = 31 * result + Objects.hashCode(trEvent.getChainName());
                result = 31 * result + Objects.hashCode(trEvent.getBusinessIdField());
                result = 31 * result + Objects.hashCode(trEvent.getTableName());
                result = 31 * result + Objects.hashCode(trEvent.getTargetClassName());
                result = 31 * result + Arrays.hashCode(trEvent.getValues());
                result = 31 * result + Objects.hashCode(trEvent.getTargetTopic());
                result = 31 * result + Objects.hashCode(trEvent.getGroovyMappingScript());
                result = 31 * result + Objects.hashCode(trEvent.getTargetControlTopic());
            }
            return result;
        }
    } // EventOperation

    // Inner class for handling retry logic
    private class RetryTimer extends TimerTask {
        @Override
        public void run() {
            if(retryList.isEmpty())
                return;
            log.info("RetryTimer Thread kicked in ... retrySize={}", retryList.size());
            try {
                for (int i = retryList.size() - 1; i >= 0; i--) {
                    IONEvent ionEvent = retryList.get(i).trEvent;
                    log.info("#{}: TimerThread retrying DB operation/Kafka publish for tradeId={}, chain:{}, topic: {}",
                        i, ionEvent.getBusinessIdField(), ionEvent.getChainName(), ionEvent.getTargetTopic());
                    addToQueue(retryList.remove(i));
                }
            } catch(Exception e) {
                ErrUtil.printFullErrMsg(e, "Exception in RetryTimer thread");
            }
        } // run
    } // RetryTimer
}
