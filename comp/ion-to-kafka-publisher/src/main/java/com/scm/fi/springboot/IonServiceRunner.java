package com.scm.fi.springboot;

import com.scm.fi.db.KafkaPubDBService;
import com.scm.fi.ion.IonPlatformListener;
import com.scm.fi.sa.kafka.producer.KafkaPublish;
import com.scm.fi.services.AppContext;
import com.scm.fi.services.ShutdownService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

/**
 * Main Component Logic.
 * Suppose to be run as a last component/bean
 */
@Component
public class IonServiceRunner implements ApplicationRunner
{
    private static final Logger log = LoggerFactory.getLogger(IonServiceRunner.class);
    private final ApplicationContext context;
    private final IonPlatformListener ionPlatformListener;
    private final KafkaPubDBService kafkaPubDBService;
    private final KafkaPublish kafkaPublish;
    @Value("${app.skipDbOperations}")
    private boolean skipDbOperations;

    @Autowired
    public IonServiceRunner(KafkaPubDBService kafkaPubDBService, IonPlatformListener ionPlatformListener,
        ApplicationContext context, KafkaPublish kafkaPublish) {
        log.debug("@@@Component: IonServiceRunner(KafkaPubDBService, IonPlatformListener, context, KafkaPublish)");
        this.kafkaPubDBService = kafkaPubDBService;
        this.ionPlatformListener = ionPlatformListener;
        this.context = context;
        this.kafkaPublish = kafkaPublish;
    }

    /**
     * Called from the main() module to run services according to command line parameters
     * @param args incoming application arguments
     * @throws Exception
     */
    @Override
    public void run(ApplicationArguments args) throws Exception
    {
        log.debug("@@@Component: IonServiceRunner.run(args) -> service.init(sourceArgs)");
        // Skip for jUnit tests
        if(context.getEnvironment().matchesProfiles("test"))
            return;

        String[] sourceArgs = args.getSourceArgs();
        if(!parseParameters(args))
        {
            IonServiceRunner.usage();
            ShutdownService.shutdownGracefully(1, "");
        }
        //AppContext.setApplicationContext(context);
        //log.info("ApplicationContext is set to AppContext class");
        if(!AppContext.isDBDown()) {
            kafkaPubDBService.deleteOldKafkaPubRecords();
            kafkaPublish.republishRecordsFromDB(); // Redundancy option
        }
        ionPlatformListener.init(sourceArgs);
        log.info("IonServiceRunner.run() is finished.");
    } // run

    /**
     * Expected command line:
     *  -init config/mkv_KAFKA_PUB_TOR-IST.jinit 1g 3g --spring.profiles.active=tor-dev
     *
     * @param args - command line arguments
     * @return - false - to print usage and exit
     */
    private boolean parseParameters(ApplicationArguments args)
    {
        // Skip arguments validation for jUnit tests
        if(context.getEnvironment().matchesProfiles("test"))
            return true;
        if(args.getNonOptionArgs().isEmpty() || args.getNonOptionArgs().contains("-?")  || args.getNonOptionArgs().contains("--?") ||
           args.getNonOptionArgs().contains("-help") || args.getNonOptionArgs().contains("--help"))
            return false;

        return true;
    } //parseParameters
    private static void usage()
    {
        StringBuilder sb = new StringBuilder(560)
        .append("\n\nCommand line parameters:\n")
        .append("-init config.jinit --spring.profiles.active=<Profile Name --spring.config.location=file:/path/to/config-folder/\n")
        .append("VM parameters:\n")
        .append("-Xrs -Xms1024m -Xmx4048m -Xss4048k -Djavax.security.auth.useSubjectCredsOnly=true -Djava.security.auth.login.config=/path/to/kafka_client_jaas.conf -Djava.security.krb5.conf=/path/to/krb5_ist.conf -Dsun.security.krb5.disableReferrals=true\n");
        if(log.isInfoEnabled())
            log.info(sb.toString());
    } // usage
} // IonServiceRunner
