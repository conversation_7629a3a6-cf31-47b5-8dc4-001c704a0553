package com.scm.fi.controller;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/config")
public class ConfigController {

    @Value("${app.refreshInterval}")
    private int refreshInterval;

    @GetMapping("/refresh-interval")
    public ResponseEntity<Integer> getRefreshInterval() {
        return ResponseEntity.ok(refreshInterval);
    }
}
