package com.scm.fi.controller;

import com.scm.fi.exceptions.GlobalExceptionHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
public class ErrorController {

    private final GlobalExceptionHandler globalExceptionHandler;

    @Autowired
    public ErrorController(GlobalExceptionHandler globalExceptionHandler) {
        this.globalExceptionHandler = globalExceptionHandler;
    }

    @GetMapping("/errors")
    public List<String> getErrors() {
        return globalExceptionHandler.getLastErrors();
    }
}
