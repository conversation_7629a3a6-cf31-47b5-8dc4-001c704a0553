package com.scm.fi.ion.dao;

import com.iontrading.mkv.MkvChain;
import com.iontrading.mkv.MkvRecord;
import com.iontrading.mkv.enums.MkvFieldType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import java.util.List;

@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class IonChain
{
    private String chainName;
    private String typeName;
    private String recordId;
    private MkvChain mkvChain;
    private MkvRecord mkvRecord;
    private List<String> ionFields;
    private MkvFieldType[] mkvFieldTypes;
    private String [] chainFieldNames;
}
