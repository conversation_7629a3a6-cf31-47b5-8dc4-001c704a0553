package com.scm.fi.ion.listener;

import com.iontrading.mkv.*;
import com.iontrading.mkv.enums.MkvChainAction;
import com.iontrading.mkv.enums.MkvFieldType;
import com.iontrading.mkv.events.MkvChainListener;
import com.iontrading.mkv.events.MkvRecordListener;
import com.scm.fi.common.util.ErrUtil;
import com.scm.fi.common.util.Utils;
import com.scm.fi.configs.Ion2KafkaFieldMapping;
import com.scm.fi.db.dao.IONEvent;
import com.scm.fi.exceptions.GlobalExceptionHandler;
import com.scm.fi.ion.IonPlatformListener;
import com.scm.fi.services.AppContext;
import com.scm.fi.services.FieldMappingService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

public abstract class ChainListener implements MkvChainListener, MkvRecordListener {
	private static final Logger log = LoggerFactory.getLogger(ChainListener.class);
    private static final Pattern TIME_PATTERN = Pattern.compile("(\\d{2})(\\d{2})(\\d{2})");
	private static final Pattern DATE_PATTERN = Pattern.compile("(\\d{4})(\\d{2})(\\d{2})");
	private static final String DEFAULT_DATE = "19000101";
	private static int undefinedDateCounter = 0; // To prevent multiple logs for the same undefined date value
    protected HashMap<String, Integer> indexMap = new HashMap<>();
	protected Ion2KafkaFieldMapping sub;
	protected FieldMappingService fieldMapping;
	protected String ION_RECORD_ID = "ScotiaFIIONExtractor_RecId";
	// print "NaN values received for field.." warnings to the log (since it could be many)
	private final boolean isNaNValuesLog;
	private boolean isFieldInitialized;
	private static boolean isFormatTimeField;
	private static boolean isConvertEmptyDate;
	private static boolean isColumnSymbolForTime;
	private static boolean isDashSymbolForDate;
	private static boolean isControlTopicPublish;
	private static boolean isConvertEmptyDataToNull;
	private static boolean isConvertToExponentialNotation;
	private static int doubleFieldPrecision;
	private static int dateMinIntConversion;

	public ChainListener(Ion2KafkaFieldMapping sub, FieldMappingService fieldMapping) {
		isNaNValuesLog = AppContext.getBooleanProperty("app.isNaNValuesLog", true);
		this.sub = sub;
		this.fieldMapping = fieldMapping;
		isFieldInitialized = false;
		isFormatTimeField = AppContext.getIsFormatTimeField();
		isConvertEmptyDate = AppContext.getIsConvertEmptyDate();
		isColumnSymbolForTime = AppContext.getIsColumnSymbolForTime();
		isDashSymbolForDate = AppContext.getIsDashSymbolForDate();
		isControlTopicPublish = AppContext.getIsControlTopicPublish();
		isConvertEmptyDataToNull = AppContext.getIsConvertEmptyDataToNull();
		doubleFieldPrecision = AppContext.getDoubleFieldPrecision();
		dateMinIntConversion = AppContext.getDateMinIntConversion();
		isConvertToExponentialNotation = AppContext.getIsConvertToExponentialNotation();
    }

	// listen for partial record supplies (Implements MkvRecordListener.
	// When is MkvRecordListener.onPartialUpdate() called?
	// When you subscribe to a record, you specify a set of fields for which you want to receive values.
	// Due to the way the ION middleware protocol works, there is no guarantee that your subscriber component
	// will receive all the subscribed fields in a single message, even if they are supplied by the publisher
	// all at once.
	// For example, suppose you just subscribed to the Id, Price, Type, and Qty fields on a record.
	// The Java API will call onPartialUpdate() as soon as an update is received on any number of the
	// subscribed fields (potentially only one). The MKVSupply object contains values for the fields
	// received in the current message. If you need all the fields received up to this point,
	// you can access mkvRecord.getSupply():
    public void onPartialUpdate(MkvRecord mkvRecord, MkvSupply mkvSupply, boolean isSnapshot) {
		log.trace("onPartialUpdate(): {}, isSnapshot: {}", mkvRecord.getName(), isSnapshot);
    }

	// listen only for full updates record supplies (Implements MkvRecordListener)
    public void onFullUpdate(MkvRecord mkvRecord, MkvSupply mkvSupply, boolean isSnapshot) {
        try {
			StringBuilder notFoundFields = null;
			long queueSize = IonPlatformListener.getEventPool(sub.getChain()).getEventQueueSize();
			log.debug("onFullUpdate() for record: {}, isSnapshot: {}, queueSize={}", mkvRecord.getName(), isSnapshot, queueSize);
			initializeFields(mkvRecord);
			if (indexMap.isEmpty()) {
				for (String key: sub.getIonFields()){
					if (mkvRecord.getMkvType().getFieldIndex(key)>=0) {
						indexMap.put(key, mkvRecord.getMkvType().getFieldIndex(key));
					}
					else if (ION_RECORD_ID.equals(key)) {
						log.info("Key {} is internally mapped ION field => recordId", key);
					}
					else {
						if(null == notFoundFields)
							notFoundFields = new StringBuilder(256);
						notFoundFields.append(key).append(" ");
					}
				}
			}
			if(null != notFoundFields)
				log.info("Fields are not found in the chain: {}.", notFoundFields.toString());

			String[] values = new String[sub.getIonFields().length];
			int i = 0;
			StringBuilder validationErrors = new StringBuilder(128);
			for (String key: sub.getIonFields()) {
				if (ION_RECORD_ID.equals(key)) { // key is internally mapped ION field => recordId
					values[i++] = mkvRecord.getName().substring(mkvRecord.getName().lastIndexOf('.') + 1);
				}
				else {
					Integer ionFieldIndex = indexMap.get(key);
					if(null == ionFieldIndex)
						values[i++] = null; // the field isn't found in the chain
					else
						values[i++] = formatAsString(mkvRecord.getValue(ionFieldIndex), mkvRecord.getName(), key, validationErrors);
				}
			}
			IONEvent event = new IONEvent();
			event.setIonFields(sub.getIonFields());
			event.setTableName(sub.getTable());
			event.setValues(values);
			event.setUpdated(true);
			event.setChainName(sub.getChain());
			event.setBusinessIdField(sub.getBusinessIdField());
			event.setTargetClassName(sub.getTargetClassName());
			event.setGroovyMappingScript(sub.getGroovyMappingScript());
			event.setTargetTopic(sub.getTargetTopic());
			event.setTargetControlTopic(sub.getTargetControlTopic());
			event.setMkvRecord(mkvRecord);
			event.setValidationErrors(validationErrors.toString());
			processDBOperation(event);
        } catch (Exception e) {
			ErrUtil.printFullErrMsg(e, "Exception in onFullUpdate", true);
        }
    } // onFullUpdate()

	private void initializeFields(MkvRecord mkvRecord)
	{
		if(isFieldInitialized)
			return;
		if(!sub.getIonFields()[0].equals("*")) {
			isFieldInitialized = true;
			return;
		}
		MkvType mt = mkvRecord.getMkvType();
		int fieldNum = mt.size();
		String[] ionFields = new String[fieldNum];
		MkvFieldType[] ionFieldTypes = new MkvFieldType[fieldNum];
		StringBuilder fieldTypes = new StringBuilder(1024);
		for (int i = 0; i < mt.size(); i++) {
			ionFields[i] = mt.getFieldName(i);
			ionFieldTypes[i] = mt.getFieldType(i);
			if(log.isDebugEnabled())
				fieldTypes.append(ionFields[i]).append("[").append(i+1).append("]:").append(ionFieldTypes[i]).append(", ");
		}
		sub.setIonFields(ionFields);
		log.info(">>>Chain {} - Dynamically retrieved {} fields from MkvType {}.", sub.getChain(), fieldNum,  mt.getName());
		log.debug("Field/Types: {}", fieldTypes);
		isFieldInitialized = true;
	} //initializeFields

	/**
	 * Function called when an update message from a subscribed chain is received.
	 * A chain can be modified in number of ways. It can be
	 * INSERT, APPEND, DELETE a record.
	 * An IDLE event is generated before a chain snapshot which could involve multiple SET events.
	 *
	 * @param   mkvChain       the interested chain
	 * @param   recordName     the record affected
	 * @param   position       the record pos
	 * @param   mkvChainAction the operation on the chain
	 */
    public void onSupply(MkvChain mkvChain, String recordName, int position, MkvChainAction mkvChainAction) {
		//need to handle delete
		try {
			log.debug("onSupply(): mkvChainAction: {} on chain: {} for record: {} at position: {}, Rec# {}",
					mkvChainAction, mkvChain.getName(), recordName, position, mkvChain.size());

			switch(mkvChainAction.intValue()) {
				case MkvChainAction.SET_code:
					// A snapshot of the chain is received, we wait for the IDLE_code to process the chain
					break;
				case MkvChainAction.RESET_code:
					// the chain has been emptied.
					// NB. The argument record and position are useless in this case
					// Unsubscribe to all the records.
					break;
				case MkvChainAction.INSERT_code:
					break;
				case MkvChainAction.APPEND_code:
					// a record has been appended to the chain.
					break;
				case MkvChainAction.DELETE_code:
					// a record has been removed from the chain.
					if(log.isDebugEnabled()) {
						String addInfo = "";
						addInfo = "Record: " + recordName + ", chain: " + mkvChain.getName();
						log.debug("Un-implemented Event: DELETE. {}", addInfo);
					}
					break;
				case MkvChainAction.IDLE_code:
					// the snapshot of the chain is completely received
					// NB. The argument record and position are useless in this case
					break;
				case MkvChainAction.ERROR_code:
					break;
				default:
					log.warn("Unexpected mkvChainAction value: {}", mkvChainAction);
			}
		} catch (Exception e) {
			ErrUtil.printFullErrMsg(e, "Exception for the Record: " + recordName, true);
        }
    } // onSupply()

    public abstract void processDBOperation(IONEvent event) throws Exception;

	/**
	 * Format the value from MKV chain as a string
	 */
	protected String formatAsString(MkvValue obj, String fieldName, String id, StringBuilder validationErrors) {
		if(null == obj)
			return null;
		MkvFieldType type = obj.getType();
		if (type == MkvFieldType.REAL) {
			double v = obj.getReal();
			if (Double.isInfinite(v) || Double.isNaN(v)){
				if(isNaNValuesLog)
					log.warn("NaN values received for field ={}, Id={}", fieldName, id);
				return obj.toString();
			}
			String string = Utils.doubleToString(v, doubleFieldPrecision, isConvertToExponentialNotation);
			return string;
		}
		// Remove newline and carriage-return
		String value = null;
		if(null != obj)
			value = obj.toString().replace("\r", "").replace("\n", "");
		if (type == MkvFieldType.DATE) {
			if (null == value || value.isEmpty() || value.equals("0"))
				if (isConvertEmptyDate)
					value = DEFAULT_DATE; // default date for empty data: SysAdmin shows it as : 1900-01-01
				else
					return (value.isEmpty() && isConvertEmptyDataToNull) ? null : value;
			// TODO: For ControlTopic creation test uncomment next line
			// if(!(value.equals(DEFAULT_DATE) || value.isEmpty() || value.equals("0"))) value += " 0123";
			if (value.length() == 8) {
				return isDashSymbolForDate ? DATE_PATTERN.matcher(value).replaceAll("$1-$2-$3") : value;
			}
			Long longDate = Utils.stringToLong(value);
			if(null != longDate) {
				long longValue = longDate.longValue();
				if (Integer.MIN_VALUE == longValue || (Integer.MIN_VALUE + 1) == longValue) {
					value = switch (dateMinIntConversion) {
						case 0 -> null;
						case 1 -> "0";
						case 2 -> DEFAULT_DATE;
						case 3 -> value;
						default -> {
							log.error("Unexpected dateMinIntConversion value: {}", dateMinIntConversion);
							yield value;
						}
					};
					if (GlobalExceptionHandler.getErrorNumberToDisplay() > undefinedDateCounter)
						log.info("#{}: Undefined date value ({}) for field: {}, Id: {}. Convert value according to yaml parameter" +
								" (app.config.date-min-int-conversion:{}) to: {}.", (++undefinedDateCounter), longValue,
								fieldName, id, dateMinIntConversion, value);
					return value;
				}
			}
			if(isControlTopicPublish) {
				String errorMsg = "Invalid date format for field: " + fieldName + ". Id: " + id + ". Expected field length: 8, but we got value: '" + value + "'.";
				log.error(errorMsg);
				validationErrors.append(errorMsg).append("\n");
			}
			return value;
		} // DATEs
		if (type == MkvFieldType.TIME) {
			if (isFormatTimeField) {
				if (null == value || value.isEmpty() || value.equals("0"))
					return isColumnSymbolForTime ? "00:00:00" : "000000";
				else if (value.length() == 6)
					return isColumnSymbolForTime ? TIME_PATTERN.matcher(value).replaceAll("$1:$2:$3") : value;
			}
			if((null == value || value.isEmpty()) && isConvertEmptyDataToNull)
				return null;
			if(value.length() <= 6) // value.isEmpty() || value.equals("0")
				return value;
			if(isControlTopicPublish) {
				String errorMsg = "Invalid time format for field: " + fieldName + ". Id: " + id + ". Expected field length: <= 6, got value: '" + value + "'.";
				log.error(errorMsg);
				validationErrors.append(errorMsg).append("\n");
			}
			return value;
		} // TIMEs
		if (type != MkvFieldType.STR) // INT
			return value;
		// MkvFieldType.STR
		Map<String, String> rules = sub.getConversionRules();
		if(null == rules || rules.isEmpty()) // no individual conversion rules for this topic => use global
			return ((null == value || value.isEmpty()) && isConvertEmptyDataToNull) ? null : value;
		if(null == value) {
			String nullReplacement = rules.get("null");
			return (null == nullReplacement || nullReplacement.isEmpty()) ? null : nullReplacement;
		}
		if(value.isEmpty()) {
			String emptyReplacement = rules.get("empty");
			return (null == emptyReplacement || emptyReplacement.isEmpty()) ? "" : emptyReplacement;
		}
		return value;
	} // formatAsString
} // ChainListener
