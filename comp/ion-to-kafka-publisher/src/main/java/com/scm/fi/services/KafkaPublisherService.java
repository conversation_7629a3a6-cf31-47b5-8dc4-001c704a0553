package com.scm.fi.services;

import com.scm.fi.common.util.ErrUtil;
import com.scm.fi.common.util.SAKafkaUtils;
import com.scm.fi.configs.Ion2KafkaFieldMapping;
import com.scm.fi.db.DBThreadPool;
import com.scm.fi.exceptions.GroovyRunException;
import com.scm.fi.groovy.GroovyEvaluation;
import com.scm.fi.ion.IonPlatformListener;
import com.scm.fi.sa.kafka.producer.KafkaCounters;
import com.scm.fi.sa.kafka.producer.KafkaPublish;
import org.apache.avro.specific.SpecificRecordBase;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.concurrent.ConcurrentHashMap;

/**
 * High level Kafka Publishing Service class
 */
@Service
public class KafkaPublisherService
{
    private static final Logger log = LoggerFactory.getLogger(KafkaPublisherService.class);
    private final KafkaPublish kafkaPublish;
    // Chain to a Groovy object map
    private final ConcurrentHashMap<String,GroovyEvaluation> gEvaluatorMap = new ConcurrentHashMap<>();
    @Value("${app.display-record-step:20000}") 
    private int displayRecordsStep;

    @Autowired
    public KafkaPublisherService(KafkaPublish kafkaPublish)
    {
        log.debug("@@@Service: KafkaPublisherService(GroovyEvaluation, KafkaPublish)");
        kafkaPublish.setISStoreTopicFutures(false); // We need this true for csv publisher only
        this.kafkaPublish = kafkaPublish;
    }

    /**
     * In this function, computeIfAbsent will return the existing GroovyEvaluation associated with the topic if it exists,
     * or it will create a new one, add it to the map, and then return it. This operation is atomic and ensures that the
     * method is thread-safe. This ensures that only one GroovyEvaluation is associated with each topic, even when
     * addGroovyEvaluator is called from multiple threads.
     * If setIonToKafkaAvroSerializerMsgIdGen() returns false, we should not add a new member to gEvaluatorMap.
     * @param map - input CONFIG RECORD
     * @return - GroovyEvaluation cashed (or created) GroovyEvaluation object from the map
     */
    public synchronized GroovyEvaluation addGroovyEvaluator(final Ion2KafkaFieldMapping map)  throws GroovyRunException
    {
        String chain = map.getChain();
        return gEvaluatorMap.computeIfAbsent(chain, k -> {
            boolean result;
            GroovyEvaluation newEvaluator = null;
            try {
                if(GroovyEvaluation.isUseNoGroovyScript(map.getGroovyMappingScript()))
                    return null;
                newEvaluator = new GroovyEvaluation(map);
                result = newEvaluator.setIonToKafkaAvroSerializerMsgIdGen(map.getTargetClassName(), map.getGroovyMappingScript());
            } catch (ClassNotFoundException e) {
                ErrUtil.getRootCause(e);
                throw new GroovyRunException(e);
            }
            if (result) {
                return newEvaluator;
            } else {
                return null; // do not add new GroovyEvaluation object to the map
            }
        });
    } // addGroovyEvaluator()

    /**
     * Sending messages to Kafka in separate thread
     * @param targetObj - object to publish
     * @param map - configuration for the current topic
     */
    public void publishObjectToKafka(SpecificRecordBase targetObj, Ion2KafkaFieldMapping map, KafkaCounters kafkaCounters, String topic)
    {
        //String topic = map.getTargetTopic();
        int iErrorNum = 0;
        try
        {
            String messageId = SAKafkaUtils.getMessageId(targetObj);
            if( (kafkaCounters.getTotalSends() + 1) % displayRecordsStep == 0) {
                log.info("Step#4: Send record#{} to: '{}' Kafka topic, from chain:{}, messageId:{}, retryMapSize:{}",
                        (kafkaCounters.getTotalSends() + 1), topic, map.getChain(), messageId, kafkaPublish.getRetryMapSize());
            }
            else {
                log.debug("Step#5: Send record#{} to: '{}' Kafka topic, from chain:{}, messageId:{}, retryMapSize:{}",
                        (kafkaCounters.getTotalSends() + 1), topic, map.getChain(), messageId, kafkaPublish.getRetryMapSize());
            }
            DBThreadPool eventPool = IonPlatformListener.getEventPool(map.getChain());
            kafkaPublish.setQueueSize(eventPool.getEventQueueSize());
            kafkaPublish.send(topic, messageId, targetObj, map.getChain(), kafkaCounters);
            if( (kafkaCounters.getTotalSends() + 1) % displayRecordsStep != 0 ) {
                log.debug("Step#5: Send message to topic={}, RetryMapSze={}.", topic, kafkaPublish.getRetryMapSize());
            }
        } catch(Exception e) {
            if(null != targetObj) {
                kafkaPublish.add2RetryMap(targetObj, map.getChain(), topic);
            }
            String errMsg = String.format("Error# %d: Failed to Publishing Message#%d for topic:%s, from chain: %s, Root Cause: %s, retryMapSize:%d, Error: %s.",
                 (++iErrorNum), (kafkaCounters.getTotalSends() + 1), topic, map.getChain(), ErrUtil.getRootCause(e),
                 kafkaPublish.getRetryMapSize(), e.getMessage());
            ErrUtil.printFullErrMsg(e, errMsg, true);
            kafkaCounters.add2FailureCount(topic);
        }
    } // publishObjectToKafka
}
