package com.scm.fi.ion.processor;

import com.iontrading.mkv.MkvObject;
import com.scm.fi.common.util.Utils;
import com.scm.fi.configs.Ion2KafkaFieldMapping;
import com.scm.fi.db.dao.IONEvent;
import com.scm.fi.ion.IonPlatformListener;
import com.scm.fi.ion.listener.ChainListener;
import com.scm.fi.services.FieldMappingService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * To handle Real time database operations. RT (Real Time) mode
 */
public class RTDBProcessor extends ChainListener {
	private static final Logger log = LoggerFactory.getLogger(RTDBProcessor.class);
	public RTDBProcessor(Ion2KafkaFieldMapping sub, FieldMappingService fieldMapping) {
		super(sub, fieldMapping);
	}

	// We call it in case of onFullUpdate
	public void processDBOperation(IONEvent event) throws Exception {
		if(log.isDebugEnabled() && !sub.getChain().equals(event.getChainName())) {
			log.error("key:{}, {}<-event.getChainName() not equal {}<-sub.getChain()",
					Utils.toString(sub.getKeys()), event.getChainName(), sub.getChain());
		}
		IonPlatformListener.getEventPool(sub.getChain()).addToQueue(event);
	} // processDBOperation

	/**
	 * Publishers might be interested in this event.
	 * They could initialize a record only if someone is asking
	 * for it allocating resources only on demand.
	 * @param   mkvObject      the MKV object
	 */
	public void onSubscribe(MkvObject mkvObject) {
		log.info("onSubscribe: {}", mkvObject.getName());
	}
}
