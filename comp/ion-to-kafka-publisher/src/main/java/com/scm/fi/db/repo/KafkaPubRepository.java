package com.scm.fi.db.repo;

import com.scm.fi.db.dao.KafkaPub;
import jakarta.persistence.QueryHint;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.QueryHints;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Repository for KafkaPub entity
 * @QueryHint(name = "org.hibernate.comment", value = "...") - Adds SQL comments to your queries which can help with monitoring and debugging in the database logs.
 * @QueryHint(name = "org.hibernate.readOnly", value = "true") - Tells Hibernate that these are read-only operations, which enable certain optimizations.
 */
@Repository
public interface KafkaPubRepository extends CrudRepository<KafkaPub, Long>
{
    @QueryHints({
            @QueryHint(name = "org.hibernate.comment", value = "KafkaPubRepository.retrieveMessageIdsFromChain"),
            @QueryHint(name = "org.hibernate.readOnly", value = "true")
    })
    @Query(value = "SELECT k.BUSINESSID FROM KAFKA_PUB k WHERE k.chain = :chainName", nativeQuery = true)
    List<String> retrieveMessageIdsFromChain(@Param("chainName") String chainName);

    // @Modifying
    // @Query(value = "DELETE FROM KAFKA_PUB k WHERE k.TS < :timestamp and k.ioncomponent = :ionComponentName", nativeQuery = true)
    // int deleteByTimestampBefore(@Param("timestamp") LocalDateTime timestamp, @Param("ionComponentName") String ionComponentName);

    @Modifying
    @Query(value = """
    DELETE FROM KAFKA_PUB WHERE AUTO_ID IN (
        SELECT AUTO_ID FROM (
            SELECT AUTO_ID FROM KAFKA_PUB WHERE TS < :timestamp AND IONCOMPONENT = :ionComponentName ORDER BY AUTO_ID
        ) WHERE ROWNUM <= :batchSize )
    """, nativeQuery = true)
    int deleteByTimestampBeforeBatch(@Param("timestamp") LocalDateTime timestamp,
                                     @Param("ionComponentName") String ionComponentName,
                                     @Param("batchSize") int batchSize);

    @QueryHints({
            @QueryHint(name = "org.hibernate.comment", value = "KafkaPubRepository.countAllRecords"),
            @QueryHint(name = "org.hibernate.readOnly", value = "true")
    })
    @Query(value = "SELECT COUNT(*) FROM KAFKA_PUB where ioncomponent = :ionComponentName", nativeQuery = true)
    long countAllRecords(@Param("ionComponentName") String ionComponentName);

    @QueryHints({
            @QueryHint(name = "org.hibernate.comment", value = "KafkaPubRepository.countUnPublishedRecords"),
            @QueryHint(name = "org.hibernate.readOnly", value = "true")
    })
    @Query(value = "SELECT COUNT(*) FROM KAFKA_PUB where ioncomponent = :ionComponentName and IS_PUBLISHED = 0", nativeQuery = true)
    long countUnPublishedRecords(@Param("ionComponentName") String ionComponentName);

    // Expected to return 1 or 0 ONLY
    @QueryHints({
            @QueryHint(name = "org.hibernate.comment", value = "KafkaPubRepository.countRecordsWithMessageId"),
            @QueryHint(name = "org.hibernate.readOnly", value = "true")
    })
    @Query(value = "SELECT COUNT(*) FROM KAFKA_PUB k where k.messageId = :messageId", nativeQuery = true)
    long countRecordsWithMessageId(@Param("messageId") String messageId);

    @QueryHints({
            @QueryHint(name = "org.hibernate.comment", value = "KafkaPubRepository.getChainsToRepublish"),
            @QueryHint(name = "org.hibernate.readOnly", value = "true")
    })
    @Query(value = """
            WITH ranked_records AS (
                 SELECT k.*, ROW_NUMBER() OVER (ORDER BY k.auto_id) as rn
                 FROM KAFKA_PUB k
                 WHERE k.is_published = 0 AND k.ioncomponent = :ionComponentName
            )
            SELECT Auto_id, MessageId, Chain, Topic, TargetClass, TS, BusinessId, '' as chainfields, TO_CLOB(TargetObject) as targetobject, ioncomponent, Is_Published FROM ranked_records
            WHERE rn > :offset AND rn <= :offset + :batchSize
            ORDER BY topic, auto_id
            """, nativeQuery = true)
    List<KafkaPub> getChainsToRepublish(@Param("offset") int offset, @Param("batchSize") int batchSize, @Param("ionComponentName") String ionComponentName);

    @QueryHints({
            @QueryHint(name = "org.hibernate.comment", value = "KafkaPubRepository.getAllChainsToRepublish"),
            @QueryHint(name = "org.hibernate.readOnly", value = "true")
    })
    @Query(value = """
            SELECT Auto_id, MessageId, Chain, Topic, TargetClass, TS, BusinessId, '' as chainfields, TO_CLOB(TargetObject) as targetobject, ioncomponent, Is_Published
                 FROM KAFKA_PUB
                 WHERE is_published = 0 AND ioncomponent = :ionComponentName and ROWNUM <= :batchSize 
            ORDER BY topic, auto_id
            """, nativeQuery = true)
    List<KafkaPub> getAllChainsToRepublish(@Param("batchSize") int batchSize, @Param("ionComponentName") String ionComponentName);

    @QueryHints({
            @QueryHint(name = "org.hibernate.comment", value = "KafkaPubRepository.getLastRecords"),
            @QueryHint(name = "org.hibernate.readOnly", value = "true")
    })
    @Query(value = """
          SELECT * FROM (
            SELECT Auto_id, MessageId, Chain, Topic, TargetClass, TS, BusinessId, '' as chainfields, Substr(TO_CLOB(TargetObject),0, 10) as targetobject, ioncomponent, Is_Published
                FROM KAFKA_PUB
                    WHERE ioncomponent = :ionComponentName
                        ORDER BY AUTO_ID DESC
          ) WHERE ROWNUM <= :count
          """, nativeQuery = true)
    List<KafkaPub> getLastRecords(@Param("count") Long count, @Param("ionComponentName") String ionComponentName);

    @Modifying
    @Query(value = "UPDATE KAFKA_PUB k SET k.IS_PUBLISHED = 1 WHERE k.MESSAGEID = :messageId", nativeQuery = true)
    int updatePublishedStatus(@Param("messageId") String messageId);
}
