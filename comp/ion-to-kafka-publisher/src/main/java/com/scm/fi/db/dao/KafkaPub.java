package com.scm.fi.db.dao;

import com.scm.fi.avro.AvroJsonUtil;
import com.scm.fi.common.util.DBMS;
import com.scm.fi.common.util.GroovyUtils;
import com.scm.fi.sa.kafka.serializers.plugins.Row;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.avro.specific.SpecificRecordBase;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Timestamp;

import static com.scm.fi.common.util.DateTimeUtils.DateTime2TS;

/**
 * Class corresponding to Row object to use for insert queries like:
 * INSERT INTO KAFKA_PUB (messageId, CHAIN, TS, BUSINESSID, targetObject)  VALUES VALUES(?, ?, ?, ?, ?)
 * select AUTO_ID, messageId, CHAIN, TS, BUSINESSID,  to_clob(targetObject) from KAFKA_PUB where chain='CAD.CM_POSITION.POSITION_TOR.POSITION' order by auto_id desc
 */
@Entity
@Table(name = "KAFKA_PUB")
@Getter
@Setter
@ToString
public class KafkaPub
{
    private static final Logger log = LoggerFactory.getLogger(KafkaPub.class);
    private static boolean isFirstSerializationVerified = false;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "AUTO_ID", insertable = false, updatable = false)
    private Long auto_id;

    @Column(name = "MESSAGEID", nullable = false)
    private String messageId;

    @Column(name = "CHAIN", nullable = false)
    private String chain;

    @Column(name = "TOPIC", nullable = false)
    private String topic;

    @Column(name = "TARGETCLASS", nullable = false)
    private String targetClass;

    // sourceSystemCreationTimestamp
    @Column(name = "TS", nullable = false)
    private Timestamp ts;

    @Column(name = "BUSINESSID", nullable = false)
    private String businessId;

    @Lob
    @Column(name = "CHAINFIELDS")
    private String chainFields;
    @Lob
    @Column(name = "TARGETOBJECT")
    private String targetObject;

    @Column(name = "IONCOMPONENT")
    private String ionComponent;

    @Column(name = "IS_PUBLISHED", nullable = false)
    private Integer is_published;

    // Transient cache
    @Transient
    private String chainFieldsString;
    @Transient
    private String targetObjectString;

    public KafkaPub() {}

    public KafkaPub(String messageId, String chain, String topic, String targetClass, Timestamp ts, String businessId,
                    String chainFields, String targetObject, String ionComponent, Integer is_published)
    {
        this.messageId = messageId;
        this.chain = chain;
        this.topic = topic;
        this.targetClass = targetClass;
        this.ts = ts;
        this.businessId = businessId;
        this.targetObject = targetObject;
        this.chainFields = chainFields;
        this.ionComponent = ionComponent;
        this.is_published = is_published;
    }

    // Getters that handle the conversion
    public String getChainFields() {
        if (chainFieldsString == null && chainFields != null) {
            chainFieldsString = DBMS.convertLOBToString(chainFields);
        }
        return chainFieldsString;
    }

    public String getTargetObject() {
        if (targetObjectString == null && targetObject != null) {
            targetObjectString = DBMS.convertLOBToString(targetObject);
        }
        return targetObjectString;
    }

    /**
     * Populate KafkaPub object from the Row object
     * @param rowObj
     * @return KafkaPub object
     * @throws Exception
     */
    public KafkaPub setKafkaPub(final Row rowObj, final SpecificRecordBase targetObj, final String topic, final String targetClass, final String ionComponent) throws Exception {
        this.messageId = rowObj.getHeader().getMessageId().toString();
        this.chain = rowObj.getHeader().getSentBy().toString();
        this.topic = topic;
        this.targetClass = targetClass;
        this.ts = DateTime2TS(rowObj.getHeader().getSourceSystemCreationTimestamp());
        this.businessId = rowObj.getHeader().getBusinessId().toString();
        //this.targetObject = AvroJsonUtil.toJson(targetObj);
        this.targetObject = AvroJsonUtil.avroObjToJson(targetObj);
        this.chainFields = AvroJsonUtil.avroObjToJson(rowObj);
        this.is_published = 0;
        this.ionComponent = ionComponent;
        if(!isFirstSerializationVerified && log.isDebugEnabled()) {
            Class<?> classObj = GroovyUtils.str2TargetClass(this.targetClass);
            SpecificRecordBase avroObject = AvroJsonUtil.jsonToAvroObj(this.getTargetObject(), classObj);
            // Just to test: compare objects before serialization and after de-serialization
            if (!avroObject.equals(targetObj)) {
                log.error("avroHeaderToJsonStr->jsonStrToAvroHeader->not Equal!");
            }
            Class<?> rowClassObj = GroovyUtils.str2TargetClass("com.scm.fi.sa.kafka.serializers.plugins.Row");
            SpecificRecordBase rowAvroObject = AvroJsonUtil.jsonToAvroObj(this.getChainFields(), rowClassObj);
            if (!rowAvroObject.equals(rowObj)) {
                log.error("rowAvroObject->rowObj->not Equal!");
            }

            isFirstSerializationVerified = true; // check only once
        }
        return this;
    } // setChain
/*
 TODO [Reverse Engineering] create field to map the 'SCM_LAST_UPD_USER_ID' column
 Available actions: Define target Java type | Uncomment as is | Remove column mapping
    @Column(name = "SCM_LAST_UPD_USER_ID", columnDefinition = "unknown")
    private java.lang.Object scmLastUpdUserId;
*/
}
