package com.scm.fi.controller;

import com.scm.fi.common.util.Utils;
import com.scm.fi.db.KafkaPubDBService;
import com.scm.fi.db.dao.KafkaPub;
import com.scm.fi.sa.kafka.producer.KafkaCounters;
import com.scm.fi.sa.kafka.producer.KafkaPublish;
import com.scm.fi.services.AppContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Base project metrics related to Kafka and DB:
 * http://localhost:8080/api/metrics/db
 * http://localhost:8080/api/metrics/kafka
 *
 * Called from main GUI: http://localhost:8080
 */
@RestController
@RequestMapping("/api/metrics")
public class MetricsController
{
    private final KafkaPubDBService kafkaPubService;
    private final KafkaPublish kafkaMessagingService;
    private KafkaCounters kafkaCounters;
    private static String publisherName;
    private static long lastRecord2ShowNum = 5;
    @PostMapping("/db/lastRecord2ShowNum")
    public ResponseEntity<?> setLastRecord2ShowNum(@RequestParam long newValue) {
        lastRecord2ShowNum = newValue;
        return ResponseEntity.ok().build();
    }

    @Autowired
    public MetricsController(KafkaPubDBService kafkaPubService, KafkaPublish kafkaMessagingService) {
        this.kafkaPubService = kafkaPubService;
        this.kafkaMessagingService = kafkaMessagingService;
    }
    private static String getComponentName() {
        if(null == publisherName || publisherName.isEmpty())
            publisherName = Utils.getComponentName();
        return publisherName;
    }
    @GetMapping("/db")
    public ResponseEntity<?> getDbMetrics() {
        Map<String, Object> metrics = new HashMap<>();
        if(AppContext.isDBDown())
            metrics.put("totalCount", "DB is disconnected or down due to yaml configurations..");
        else {
            metrics.put("totalCount", kafkaPubService.getTotalRecords(getComponentName()));
            metrics.put("totalInserts", kafkaPubService.getSucceedInsertsCount());
            metrics.put("failedInserts", kafkaPubService.getFailedInsertsCount());
            metrics.put("unSendRecords", kafkaPubService.countUnPublishedRecords(getComponentName()));
            List<KafkaPub> lastInsertedRecords = kafkaPubService.getLastRecords(lastRecord2ShowNum, getComponentName());
            metrics.put("lastRecords", lastInsertedRecords);
        }
        return ResponseEntity.ok(metrics);
    }

    @GetMapping("/kafka")
    public ResponseEntity<?> getKafkaMetrics() {
        Map<String, Object> metrics = new HashMap<>();
        kafkaCounters = AppContext.getKafkaCounters();
        if(null == kafkaCounters) {
            metrics.put("messagesSent", "Kafka kafkaCounters is not available yet..");
            return ResponseEntity.ok(metrics);
        }
        metrics.put("messagesSent", kafkaCounters.getTotalSends());
        metrics.put("successfulSends", kafkaCounters.getAllSuccessCount());
        metrics.put("failedSends", kafkaCounters.getAllFailureCount());
        metrics.put("retryMapSize", kafkaMessagingService.getRetryMapSize());
        metrics.put("publisherName", getComponentName());
        return ResponseEntity.ok(metrics);
    }
}
