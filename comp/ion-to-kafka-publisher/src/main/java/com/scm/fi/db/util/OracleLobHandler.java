package com.scm.fi.db.util;

import com.scm.fi.db.dao.KafkaPub;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.sql.DataSource;
import java.sql.*;
import java.util.Base64;

/**
 * Utility class to handle Oracle LOB write operations
 * Bypasses Hibernates LOB handling to avoid ORA-22923 errors
 */
@Component
public class OracleLobHandler {
    private static final Logger log = LoggerFactory.getLogger(OracleLobHandler.class);
    private final DataSource dataSource;

    @Autowired
    public OracleLobHandler(DataSource dataSource) {
        this.dataSource = dataSource;
    }

    /**
     * Insert a KafkaPub record using JD<PERSON> as a fallback
     * @param kafkaPub - KafkaPub object to insert
     */
   @Transactional(propagation = Propagation.REQUIRES_NEW)
   public KafkaPub insertKafkaPubWithJDBC(KafkaPub kafkaPub, String messageId) {
       KafkaPub insertedObj = null;

       try {
           // Use the OracleLobHandler as fallback
           Long autoId = insertKafkaPub(
                   kafkaPub.getMessageId(),
                   kafkaPub.getChain(),
                   kafkaPub.getTopic(),
                   kafkaPub.getTargetClass(),
                   kafkaPub.getTs(),
                   kafkaPub.getBusinessId(),
                   kafkaPub.getChainFields(),
                   kafkaPub.getTargetObject(),
                   kafkaPub.getIonComponent()
           );
           if (autoId != null) {
               // Verify the LOB data was saved properly
               boolean verified = verifyLobSave(autoId);
               if (verified) {
                   // Create a KafkaPub object with the retrieved AUTO_ID
                   insertedObj = new KafkaPub();
                   insertedObj.setAuto_id(autoId);
                   insertedObj.setMessageId(messageId);
                   insertedObj.setChain(kafkaPub.getChain());
                   insertedObj.setTopic(kafkaPub.getTopic());
                   insertedObj.setTargetClass(kafkaPub.getTargetClass());
                   insertedObj.setTs(kafkaPub.getTs());
                   insertedObj.setBusinessId(kafkaPub.getBusinessId());
                   insertedObj.setIonComponent(kafkaPub.getIonComponent());
                   insertedObj.setIs_published(0);

                   // Don't set the LOB fields as they're already in the DB
                   log.info("Successfully inserted KafkaPub record using JDBC fallback, messageId: {}, autoId: {}",
                           messageId, autoId);

                   return insertedObj;
               } else {
                   log.error("LOB verification failed for messageId: {}, autoId: {}", messageId, autoId);
                   return null;
               }
           }
       } catch (Exception e) {
            // Handle any other exceptions
            log.error("Unexpected error inserting KafkaPub record with messageId {}: {}",
                    messageId, e.getMessage(), e);
           // Mark transaction for rollback
           TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return null;
        }
       return insertedObj;
   } // insertKafkaPubWithJDBC

    /**
     * Insert a record with LOB data using direct JDBC and Base64 encoding
     * This completely bypasses the problematic streaming LOB API
     */
    public Long     insertKafkaPub(
            String messageId,
            String chain,
            String topic,
            String targetClass,
            Timestamp ts,
            String businessId,
            String chainFields,
            String targetObject,
            String ionComponent) {

        // Then use PL/SQL to convert Base64 to CLOB
        // The code is dealing with the issue of inserting large text data into CLOB fields by first encoding it to
        // Base64 (in Java), passing the encoded string to SQL, and then decoding it within the database using these
        // Oracle utilities. This technique bypasses potential issues with the standard JDBC LOB streaming API.
        String plsql = "BEGIN " +
                "   INSERT INTO KAFKA_PUB (MESSAGEID, CHAIN, TOPIC, TARGETCLASS, TS, BUSINESSID, " +
                "                         CHAINFIELDS, TARGETOBJECT, IONCOMPONENT, IS_PUBLISHED) " +
                "   VALUES (?, ?, ?, ?, ?, ?, " +
                "           UTL_RAW.CAST_TO_VARCHAR2(UTL_ENCODE.BASE64_DECODE(UTL_RAW.CAST_TO_RAW(?))), " +
                "           UTL_RAW.CAST_TO_VARCHAR2(UTL_ENCODE.BASE64_DECODE(UTL_RAW.CAST_TO_RAW(?))), " +
                "           ?, 0); " +
                "END;";

        Connection conn = null;
        PreparedStatement stmt = null;
        CallableStatement cstmt = null;
        Long autoId = null;
        String chainFieldsB64, targetObjectB64;

        try {
            conn = dataSource.getConnection();
            conn.setAutoCommit(false);

            // Try the PL/SQL approach first
            try {
                // Convert LOB content to Base64 to avoid LOB streaming entirely
                chainFieldsB64 = Base64.getEncoder().encodeToString(
                        chainFields.getBytes(java.nio.charset.StandardCharsets.UTF_8));
                targetObjectB64 = Base64.getEncoder().encodeToString(
                        targetObject.getBytes(java.nio.charset.StandardCharsets.UTF_8));

                cstmt = conn.prepareCall(plsql);
                cstmt.setString(1, messageId);
                cstmt.setString(2, chain);
                cstmt.setString(3, topic);
                cstmt.setString(4, targetClass);
                cstmt.setTimestamp(5, ts);
                cstmt.setString(6, businessId);
                cstmt.setString(7, chainFieldsB64);
                cstmt.setString(8, targetObjectB64);
                cstmt.setString(9, ionComponent);

                cstmt.execute();
                conn.commit();

                // Get the AUTO_ID
                try (PreparedStatement idStmt = conn.prepareStatement(
                        "SELECT AUTO_ID FROM KAFKA_PUB WHERE MESSAGEID = ?")) {
                    idStmt.setString(1, messageId);
                    try (ResultSet rs = idStmt.executeQuery()) {
                        if (rs.next()) {
                            autoId = rs.getLong(1);
                        }
                    }
                }
                if (autoId != null) {
                    log.info("Successfully inserted KafkaPub record using PL/SQL approach, " +
                            "messageId: {}, autoId: {}", messageId, autoId);
                }
                return autoId;
            } catch (SQLException e) {
                // If PL/SQL fails (likely because UTL_ENCODE isn't available), fall back to alternative approach
                log.warn("PL/SQL insert failed, falling back to alternative approach: {}", e.getMessage());
                conn.rollback();
                // Alternative approach - Direct insert with TO_CLOB
                String altSql = "INSERT INTO KAFKA_PUB (MESSAGEID, CHAIN, TOPIC, TARGETCLASS, TS, BUSINESSID, " +
                        "CHAINFIELDS, TARGETOBJECT, IONCOMPONENT, IS_PUBLISHED) " +
                        "VALUES (?, ?, ?, ?, ?, ?, TO_CLOB(?), TO_CLOB(?), ?, 0)";

                stmt = conn.prepareStatement(altSql, new String[]{"AUTO_ID"});
                stmt.setString(1, messageId);
                stmt.setString(2, chain);
                stmt.setString(3, topic);
                stmt.setString(4, targetClass);
                stmt.setTimestamp(5, ts);
                stmt.setString(6, businessId);
                stmt.setString(7, chainFields);
                stmt.setString(8, targetObject);
                stmt.setString(9, ionComponent);

                int rows = stmt.executeUpdate();
                if (rows > 0) {
                    try (ResultSet rs = stmt.getGeneratedKeys()) {
                        if (rs.next()) {
                            autoId = rs.getLong(1);
                            log.info("Successfully inserted KafkaPub record using TO_CLOB approach, " +
                                    "messageId: {}, autoId: {}", messageId, autoId);
                            conn.commit();
                            return autoId;
                        }
                    }
                }
                // If we get here, the TO_CLOB approach also failed
                // Try one more approach - chunked insert
                log.warn("TO_CLOB approach failed, trying chunked insert");
                conn.rollback();
                return insertWithChunkedLobs(conn, messageId, chain, topic, targetClass,
                        ts, businessId, chainFields, targetObject, ionComponent);
            }
        } catch (SQLException e) {
            if (conn != null) {
                try {
                    conn.rollback();
                } catch (SQLException ex) {
                    log.error("Error rolling back transaction", ex);
                }
            }
            log.error("All insertion approaches failed for messageId: {}, error: {}",
                    messageId, e.getMessage(), e);
            return null;

        } finally {
            closeResources(null, stmt, null);
            closeResources(null, cstmt, conn);
        }
    } // insertKafkaPub

    /**
     * Last resort approach: insert record with empty LOBs, then update in chunks
     */
    private Long insertWithChunkedLobs(Connection conn, String messageId, String chain,
                                       String topic, String targetClass, Timestamp ts,
                                       String businessId, String chainFields,
                                       String targetObject, String ionComponent) throws SQLException {
        // First insert with empty CLOBs
        String insertSql = "INSERT INTO KAFKA_PUB (MESSAGEID, CHAIN, TOPIC, TARGETCLASS, TS, BUSINESSID, " +
                "CHAINFIELDS, TARGETOBJECT, IONCOMPONENT, IS_PUBLISHED) " +
                "VALUES (?, ?, ?, ?, ?, ?, EMPTY_CLOB(), EMPTY_CLOB(), ?, 0)";

        try (PreparedStatement stmt = conn.prepareStatement(insertSql)) {
            stmt.setString(1, messageId);
            stmt.setString(2, chain);
            stmt.setString(3, topic);
            stmt.setString(4, targetClass);
            stmt.setTimestamp(5, ts);
            stmt.setString(6, businessId);
            stmt.setString(7, ionComponent);
            int rows = stmt.executeUpdate();
            if (rows == 0) {
                return null;
            }
        }
        // Get the AUTO_ID
        Long autoId = null;
        try (PreparedStatement stmt = conn.prepareStatement(
                "SELECT AUTO_ID FROM KAFKA_PUB WHERE MESSAGEID = ?")) {
            stmt.setString(1, messageId);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    autoId = rs.getLong(1);
                }
            }
        }
        if (autoId == null) {
            return null;
        }
        // Convert the fields to byte arrays for chunked update
        byte[] chainFieldsBytes = chainFields.getBytes(java.nio.charset.StandardCharsets.UTF_8);
        byte[] targetObjectBytes = targetObject.getBytes(java.nio.charset.StandardCharsets.UTF_8);

        // Update CHAINFIELDS in chunks
        try {
            updateLobInChunks(conn, autoId, "CHAINFIELDS", chainFieldsBytes);
        } catch (SQLException e) {
            log.error("Failed to update CHAINFIELDS for autoId: {}", autoId, e);
            throw e;
        }

        // Update TARGETOBJECT in chunks
        try {
            updateLobInChunks(conn, autoId, "TARGETOBJECT", targetObjectBytes);
        } catch (SQLException e) {
            log.error("Failed to update TARGETOBJECT for autoId: {}", autoId, e);
            throw e;
        }
        conn.commit();
        log.info("Successfully inserted record with chunked LOB updates, autoId: {}", autoId);
        return autoId;
    } // insertWithChunkedLobs

    /**
     * Update a LOB field in chunks to avoid streaming issues
     */
    private void updateLobInChunks(Connection conn, Long autoId, String columnName,
                                   byte[] data) throws SQLException {
        // Chunk size of 4000 bytes (to stay under Oracle's VARCHAR2 limit)
        final int CHUNK_SIZE = 4000;
        // Calculate number of chunks
        int chunks = (data.length + CHUNK_SIZE - 1) / CHUNK_SIZE;

        for (int i = 0; i < chunks; i++) {
            int offset = i * CHUNK_SIZE;
            int length = Math.min(CHUNK_SIZE, data.length - offset);

            // Extract this chunk
            byte[] chunk = new byte[length];
            System.arraycopy(data, offset, chunk, 0, length);

            // Convert chunk to a string
            String chunkStr = new String(chunk, java.nio.charset.StandardCharsets.UTF_8);

            // Update this chunk
            String sql = "DECLARE\n" +
                    "  l_lob CLOB;\n" +
                    "BEGIN\n" +
                    "  SELECT " + columnName + " INTO l_lob FROM KAFKA_PUB WHERE AUTO_ID = ? FOR UPDATE;\n" +
                    "  DBMS_LOB.WRITEAPPEND(l_lob, ?, ?);\n" +
                    "END;";

            try (CallableStatement cstmt = conn.prepareCall(sql)) {
                cstmt.setLong(1, autoId);
                cstmt.setInt(2, length);
                cstmt.setString(3, chunkStr);
                cstmt.execute();
            }
        } // for
    } // updateLobInChunks

    /**
     * Verify the LOB content was properly saved
     */
    public boolean verifyLobSave(Long autoId) {
        String sql = "SELECT DBMS_LOB.GETLENGTH(CHAINFIELDS) as CF_LEN, " +
                "DBMS_LOB.GETLENGTH(TARGETOBJECT) as TO_LEN " +
                "FROM KAFKA_PUB WHERE AUTO_ID = ?";

        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;

        try {
            conn = dataSource.getConnection();
            stmt = conn.prepareStatement(sql);
            stmt.setLong(1, autoId);
            rs = stmt.executeQuery();

            if (rs.next()) {
                long chainFieldsLength = rs.getLong(1);
                long targetObjectLength = rs.getLong(2);

                log.info("Verified LOB lengths for AUTO_ID {}: CHAINFIELDS={}, TARGETOBJECT={}",
                        autoId, chainFieldsLength, targetObjectLength);

                return chainFieldsLength > 0 && targetObjectLength > 0;
            }
            return false;
        } catch (SQLException e) {
            log.error("Error verifying LOB save for AUTO_ID: {}", autoId, e);
            return false;
        } finally {
            closeResources(rs, stmt, conn);
        }
    } // verifyLobSave

    /**
     * Close JDBC resources properly
     */
    private static void closeResources(ResultSet rs, Statement stmt, Connection conn) {
        if (rs != null) {
            try { rs.close(); } catch (SQLException e) { log.error("Error closing ResultSet", e); }
        }
        if (stmt != null) {
            try { stmt.close(); } catch (SQLException e) { log.error("Error closing Statement", e); }
        }
        try {
            if (conn != null && !conn.isClosed() && !conn.getAutoCommit()) {
                conn.setAutoCommit(true);
                conn.close();
            } else if (conn != null && !conn.isClosed()) {
                conn.close();
            }
        } catch (SQLException e) {
            log.error("Error closing Connection: {}", e.getMessage());
        }
    }
} // OracleLobHandler
