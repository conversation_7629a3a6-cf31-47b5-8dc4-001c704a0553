import {Component, NgModule, OnInit} from '@angular/core';
import { environment } from '../environments/environment';
import {BrowserModule} from "@angular/platform-browser";
import {MetricsModule} from "@app/metrics/metrics.module";

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.css']
})
export class AppComponent implements OnInit {
  title = 'Ion Kafka Metrics Dashboard';
  version = '1.0.0';
  
  ngOnInit() {
    console.log(`Application starting in ${environment.production ? 'production' : 'development'} mode`);
  }
}
