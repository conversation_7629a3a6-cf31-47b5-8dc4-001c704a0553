import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { MetricsDashboardComponent } from './metrics/metrics-dashboard.component';

const routes: Routes = [
  { path: '', component: MetricsDashboardComponent },
  { path: 'metrics', component: MetricsDashboardComponent },
  { path: '**', redirectTo: '' }
];

@NgModule({
  imports: [RouterModule.forRoot(routes, {
    useHash: true, // This is important for Spring Boot integration
    scrollPositionRestoration: 'enabled'
  })],
  exports: [RouterModule]
})
export class AppRoutingModule { }