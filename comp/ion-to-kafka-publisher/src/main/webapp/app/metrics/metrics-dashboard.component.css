.content-wrapper {
  width: 100%;
  min-height: 100vh;
  background-color: #f3f4f6;
}

.content-container {
  width: 100%;
  max-width: 80rem; /* equivalent to max-w-7xl */
  margin: 0 auto;
}

.metrics-row {
  display: flex;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
}

.metrics-card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  flex: 1;
}

.metrics-card h2 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.metrics-content {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.db-metrics,
.kafka-metrics {
  padding: 1rem;
}

.table-header,
.error-header {
  border-bottom: 1px solid #e5e7eb;
  padding: 1rem;
}

.table-container {
  overflow-x: auto;
}

table {
  width: 100%;
  min-width: 100%;
  border-collapse: collapse;
}

th {
  padding: 12px 12px;
  text-align: left;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  color: #6b7280;
  background-color: #f9fafb;
}

td {
  padding: 12px 12px;
  font-size: 0.875rem;
  white-space: nowrap;
}

tr {
  border-top: 1px solid #e5e7eb;
}

.metrics-card.error-section {
  margin-top: 16px;
}

.margin-bottom-6px {
  margin-bottom: 6px;
}

.error-container {
  padding: 12px;
}

.error-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.error-item {
  background-color: #fef2f2;
  border-left: 4px solid #ef4444;
  padding: 12px;
}

.error-item p {
  font-size: 0.875rem;
  color: #b91c1c;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .content-container {
    width: 100%;
  }

  th, td {
    padding: 0.75rem 1rem;
  }
}
