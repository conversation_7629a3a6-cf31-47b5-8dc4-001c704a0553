import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HttpClientModule } from '@angular/common/http';
import { FormsModule } from '@angular/forms';
import { MetricsDashboardComponent } from './metrics-dashboard.component';
import { MetricsService } from './metrics.service';

@NgModule({
  declarations: [
    MetricsDashboardComponent
  ],
  imports: [
    CommonModule,
    HttpClientModule,
    FormsModule
  ],
  providers: [
    MetricsService
  ],
  exports: [
    MetricsDashboardComponent
  ]
})
export class MetricsModule { }
