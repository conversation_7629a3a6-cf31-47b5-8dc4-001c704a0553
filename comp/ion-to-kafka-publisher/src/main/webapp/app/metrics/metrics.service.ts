import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, interval } from 'rxjs';
import { switchMap } from 'rxjs/operators';

export interface DBMetrics {
  totalCount: number;
  totalInserts: number;
  failedInserts: number;
  unSendRecords: number;
  lastRecords: Array<{
    businessId: string;
    chain: string;
    topic: string;
    ts: string;
    messageId: string;
    auto_id: number;
    targetClass: string;
    ionComponent: string;
    is_published: number;
  }>;
}

export interface KafkaMetrics {
  messagesSent: number;
  successfulSends: number;
  failedSends: number;
  retryMapSize: number;
  publisherName: string;
}

@Injectable({
  providedIn: 'root'
})
export class MetricsService {
  private apiUrl = '/api/metrics';

  constructor(private http: HttpClient) {}

  getRefreshInterval(): Observable<number> {
    return this.http.get<number>('/api/config/refresh-interval');
  }
  getDBMetrics(): Observable<DBMetrics> {
    return this.http.get<DBMetrics>('/api/metrics/db');
  }
  getKafkaMetrics(): Observable<KafkaMetrics> {
    return this.http.get<KafkaMetrics>('/api/metrics/kafka');
  }
  getErrors(): Observable<string[]> {
    return this.http.get<string[]>('/errors');
  }
  getPollingMetrics(refreshInterval: number): Observable<any> {
    return interval(refreshInterval).pipe(
      switchMap(() => Promise.all([
        this.getDBMetrics().toPromise(),
        this.getKafkaMetrics().toPromise(),
        this.getErrors().toPromise()
      ]))
    );
  }
  setLastRecord2ShowNum(newValue: number) {
    return this.http.post(`${this.apiUrl}/db/lastRecord2ShowNum`, null, {
      params: { newValue: newValue.toString() }
    });
  }
}
