import { Component, OnInit, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { MetricsService } from './metrics.service';
import { Subscription } from 'rxjs';

interface DbRecord {
    businessId: string;
    chain: string;
    topic: string;
    ts: string;
    messageId: string;
    auto_id: number;
    targetClass: string;
    ionComponent: number;
    is_published: number;
}

@Component({
    selector: 'app-metrics-dashboard',
    templateUrl: './metrics-dashboard.component.html',
    styleUrls: ['./metrics-dashboard.component.css']
})
export class MetricsDashboardComponent implements OnInit, OnDestroy {
    initialLastRecord2ShowNum: number = 5;
    refreshInterval: number = 9000;
    dbMetrics: any = {
        totalCount: 0,
        totalInserts: 0,
        failedInserts: 0,
        unSendRecords: 0,
        lastRecords: [] as DbRecord[]
    };
    kafkaMetrics: any = {
        messagesSent: 0,
        successfulSends: 0,
        failedSends: 0,
        retryMapSize: 0,
        publisherName: ""
    };
    errors: string[] = [];
    private subscription: Subscription | null = null;

    constructor(private metricsService: MetricsService) {}

    ngOnInit() {
        this.initializePolling();
    }

    private initializePolling() {
        this.metricsService.getRefreshInterval().subscribe(
            refreshInterval => {
                this.refreshInterval = refreshInterval;
                this.subscription = this.metricsService.getPollingMetrics(this.refreshInterval).subscribe(
                    ([dbMetrics, kafkaMetrics, errors]) => {
                        // Only update if we receive valid data
                        if (dbMetrics) {
                            this.dbMetrics = {
                                ...this.dbMetrics,
                                ...dbMetrics
                            };
                        }
                        if (kafkaMetrics) {
                            this.kafkaMetrics = {
                                ...this.kafkaMetrics,
                                ...kafkaMetrics
                            };
                        }
                        if (errors) {
                            this.errors = errors;
                        }
                    },
                    error => console.error('Failed to fetch metrics:', error)
                );
            },
            error => console.error('Failed to fetch refresh interval:', error)
        );
    }

    getMetrics() {
        this.metricsService.getDBMetrics().subscribe(
            (data: any) => {
                this.dbMetrics = data;
            },
            (error) => {
                console.error('Failed to fetch DB metrics', error);
            }
        );

        this.metricsService.getKafkaMetrics().subscribe(
            (data: any) => {
                this.kafkaMetrics = data;
            },
            (error) => {
                console.error('Failed to fetch Kafka metrics', error);
            }
        );
    }

    getErrors() {
        this.metricsService.getErrors().subscribe(
            (data: string[]) => {
                this.errors = data;
            },
            (error) => {
                console.error('Failed to fetch errors', error);
            }
        );
    }

    ngOnDestroy() {
        if (this.subscription) {
            this.subscription.unsubscribe();
        }
    }

    updateLastRecord2ShowNum(numericValue: number) {
        //const numericValue = Number(value);
        this.metricsService.setLastRecord2ShowNum(numericValue).subscribe(response => {
            console.log('lastRecord2ShowNum Value updated successfully');
        }, error => {
            console.error('Failed to update lastRecord2ShowNum value', error);
        });
    }
}
