<div class="content-wrapper">
    <div class="content-container">
        <div class="metrics-row">
            <div class="metrics-card db-metrics">
                <h2>Database Metrics</h2>
                <div class="metrics-content">
                    <p>Total Count: {{dbMetrics?.totalCount || 0}}</p>
                    <p>Total Inserts: {{dbMetrics?.totalInserts || 0}}</p>
                    <p>Failed Inserts: {{dbMetrics?.failedInserts || 0}}</p>
                    <p>Un-sent records: {{dbMetrics?.unSendRecords || 0}}</p>
                </div>
            </div>

            <div class="metrics-card kafka-metrics">
                <h2>Kafka Metrics</h2>
                <div class="metrics-content">
                    <p>Messages Sent: {{kafkaMetrics?.messagesSent || 0}}</p>
                    <p>Successful Sends: {{kafkaMetrics?.successfulSends || 0}}</p>
                    <p>Failed Sends: {{kafkaMetrics?.failedSends || 0}}</p>
                    <p>Retry Map Size: {{kafkaMetrics?.retryMapSize || 0}}</p>
                    <p>Publisher Name: {{kafkaMetrics?.publisherName || 0}}</p>
                </div>
            </div>
        </div>

        <label for="newValueInput">How many last updated records to display: </label>
        <input id="newValueInput" type="number" [(ngModel)]="initialLastRecord2ShowNum">
        <button (click)="updateLastRecord2ShowNum(initialLastRecord2ShowNum)" class="margin-bottom-6px">Update</button>

        <div class="metrics-card records-table">
            <div class="table-header">
                <h2>Last Inserted Records</h2>
            </div>
            <div class="table-container">
                <table>
                    <thead>
                    <tr>
                        <th>Business ID</th>
                        <th>Source Chain</th>
                        <th>Target Topic</th>
                        <th>Timestamp</th>
                        <th>messageId</th>
                        <th>auto_id</th>
                        <th>targetClass</th>
                        <th>IonComponent</th>
                        <th>Is_published</th>
                    </thead>
                    <tbody>
                    <tr *ngFor="let record of dbMetrics?.lastRecords">
                        <td>{{record.businessId}}</td>
                        <td>{{record.chain}}</td>
                        <td>{{record.topic}}</td>
                        <td>{{record.ts | date:'yyyy-MM-dd HH:mm:ss.SSS'}}</td>
                        <td>{{record.messageId}}</td>
                        <td>{{record.auto_id}}</td>
                        <td>{{record.targetClass}}</td>
                        <td>{{record.ionComponent}}</td>
                        <td>{{record.is_published}}</td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="metrics-card error-section">
            <div class="error-header">
                <h2>Last Errors</h2>
            </div>
            <div class="error-container">
                <div class="error-list">
                    <div *ngFor="let error of errors" class="error-item">
                        <p>{{error}}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>