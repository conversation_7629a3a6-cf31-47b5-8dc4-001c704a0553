package com.scm.fi.kafka;

import com.scm.fi.Ion2KafkaPubApp;
import com.scm.fi.avro.JacksonMapper;
import com.scm.fi.configs.AsyncConfig;
import com.scm.fi.configs.ConfigFileLogger;
import com.scm.fi.configs.JacksonConfig;
import com.scm.fi.controller.MetricsController;
import com.scm.fi.db.IKafkaPubDBService;
import com.scm.fi.db.KafkaPubDBService;
import com.scm.fi.exceptions.GlobalExceptionHandler;
import com.scm.fi.groovy.GroovyEvaluation;
import com.scm.fi.ion.IonPlatformListener;
import com.scm.fi.sa.kafka.consumer.KafkaConsumer;
import com.scm.fi.sa.kafka.producer.KafkaPublish;
import com.scm.fi.services.AppContext;
import com.scm.fi.services.FieldMappingService;
import com.scm.fi.services.KafkaPublisherService;
import com.scm.fi.services.StartupService;
import com.scm.fi.springboot.IonServiceRunner;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * This class is used to test the context loading of the application
 * It is a Spring Boot test class that ensures the application context loads correctly. It uses the @SpringBootTest
 * annotation to create an application context for integration tests and the @ActiveProfiles("test") annotation to
 * specify the active profile for the tests.
 */
@SpringBootTest(classes = Ion2KafkaPubApp.class) // Specify your main application class
@ActiveProfiles("test") // Use this if you have a specific profile for tests
class IonSAPub2KafkaApplicationTests {
    @Autowired
    private IonServiceRunner ionServiceRunner;

    @Autowired
    private StartupService startupService;

    @Autowired
    private KafkaPublish kafkaPublish;

    @Autowired
    private GroovyEvaluation groovyEval;

    @Autowired
    private GroovyEvaluation groovyEvaluation;

    @Autowired
    private KafkaPublisherService kafkaPublisherService;

    @Autowired
    private IonPlatformListener ionPlatformListener;

    @Autowired
    private KafkaPubDBService kafkaPubDBService;

    @Autowired
    private MetricsController metricsController;

    @Autowired
    private FieldMappingService fieldMappingService;

    @Autowired
    private AsyncConfig asyncConfig;

    @Autowired
    private JacksonMapper jacksonMapper;

    @Autowired
    private AppContext appContext;

    @Autowired
    private ConfigFileLogger configFileLogger;

    @Autowired
    private JacksonConfig jacksonConfig;

    @Autowired
    @Qualifier("kafkaPubDBService")
    private IKafkaPubDBService iKafkaPubDBService;

    //@Autowired
    //private KafkaPubFakeDBService kafkaPubFakeDBService;

    @Autowired
    private GlobalExceptionHandler globalExceptionHandler;

    @Autowired
    private KafkaConsumer kafkaConsumer;

    @Test
    void contextLoads() {
    }
}