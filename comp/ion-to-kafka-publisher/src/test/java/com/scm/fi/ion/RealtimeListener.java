package com.scm.fi.ion;

import com.iontrading.mkv.Mkv;
import com.iontrading.mkv.MkvChain;
import com.iontrading.mkv.MkvRecord;
import com.iontrading.mkv.enums.MkvChainAction;
import com.iontrading.mkv.events.MkvChainListener;
import com.iontrading.mkv.qos.MkvQoS;

import java.util.Arrays;

public class RealtimeListener {
    public static void main(String[] args) {
        try {
            String jmkvArgs[] = new String[] {
                    "-init", "comp/ion-to-kafka-publisher/src/test/resources/mkv_IonTestConsumer.jinit"
            };

            MkvQoS qos = new MkvQoS();
            qos.setArgs(jmkvArgs);
            Mkv mkv = Mkv.start(qos);

            System.out.println("Consumer connected, setting up real-time listener...");
            Thread.sleep(3000);

            // Create chain and listener
            MkvChain chain = new MkvChain("EUR.QUOTE.ION_TEST_PUB_TOR.QUOTE", "Type");

            MkvChainListener listener = new MkvChainListener() {
                public void onUpdate(MkvChain chain, MkvRecord record, String[] fields, Object[] values) {
                    System.out.println("\n🔄 LIVE UPDATE RECEIVED!");
                    System.out.println("Record: " + record.getName());
                    System.out.println("Fields: " + Arrays.toString(fields));
                    System.out.println("Values: " + Arrays.toString(values));
                    System.out.println("Time: " + new java.util.Date());
                    System.out.println("✅ PUBLISHER IS DEFINITELY WORKING!");
                }

                public void onInsert(MkvChain chain, MkvRecord record) {
                    System.out.println("➕ New record: " + record.getName());
                }

                public void onDelete(MkvChain chain, MkvRecord record) {
                    System.out.println("➖ Record deleted: " + record.getName());
                }

                public void onSupply(MkvChain chain, String recordName, int recordIndex, MkvChainAction action) {
                    System.out.println("📊 Supply: " + recordName + " action: " + action);
                }
            };

            chain.subscribe(listener);
            System.out.println("✅ Subscribed to chain for real-time updates");
            System.out.println("Waiting for live data from publisher...");
            System.out.println("(You should see updates every ~1.2 seconds)");

            // Wait for updates
            for (int i = 0; i < 30; i++) {
                Thread.sleep(1000);
                System.out.print(".");
            }

        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("Error: " + e.getLocalizedMessage() + ", cause: " + e.getCause() + ", class=" + e.getClass().toString());
            System.exit(1);
        }
    }
}