package com.scm.fi.kafka;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.scm.fi.Ion2KafkaPubApp;
import com.scm.fi.avro.JacksonMapper;
import com.scm.fi.common.utils.MessageGenerator;
import com.scm.fi.configs.AsyncConfig;
import com.scm.fi.groovy.GroovyEvaluation;
import com.scm.fi.sa.kafka.producer.KafkaPublish;
import com.scm.fi.services.FieldMappingService;
import com.scm.fi.services.KafkaPublisherService;
import com.scm.fi.services.StartupService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.time.LocalDate;

import static org.junit.jupiter.api.Assertions.assertNotNull;

@SpringBootTest(classes = Ion2KafkaPubApp.class) // Specify your main application class
@ActiveProfiles("test") // Use this if you have a specific profile for tests
class MessageGeneratorTest
{
    @Mock
    private KafkaPublish kafkaPublish;
    @Mock
    private FieldMappingService fieldMappingService;
    @Mock // is used to add mock versions of the specified beans to the application context. Any existing bean of the same type defined in the context will be replaced with the mock.
    private StartupService startupService;
    @Mock
    private GroovyEvaluation groovyEvaluation;
    @Mock
    private KafkaPublisherService kafkaPublisherService;
    @Mock
    private AsyncConfig asyncConfig;

    private static final Logger log = LoggerFactory.getLogger(MessageGeneratorTest.class);
    private static final LocalDate eodDate = LocalDate.now();
    private static final String batchName = "Batch Name";
    private MessageGenerator messageGenerator;
    private String batchId;
    private ObjectMapper mapper;

    @BeforeEach
    public void setup() {
        messageGenerator = new MessageGenerator(eodDate, batchName);
        batchId = messageGenerator.getBatchId();

// Use the shared ObjectMapper instance
        mapper = JacksonMapper.getInstance();
        JavaTimeModule module = new JavaTimeModule();
        mapper.registerModule(module);
        mapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        mapper.configure(DeserializationFeature.ADJUST_DATES_TO_CONTEXT_TIME_ZONE, false);
    }

    @Test
    public void testGetBatchId() {
        String batchId = messageGenerator.getBatchId();
        log.info("batchId: {}", batchId + " have to be not null.");
        assertNotNull(batchId, "Have to be not null!");
    }
}
