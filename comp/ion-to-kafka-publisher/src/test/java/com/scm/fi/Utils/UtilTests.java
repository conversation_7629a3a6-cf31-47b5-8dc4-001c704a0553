package com.scm.fi.Utils;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.Logger;
import com.scm.fi.common.util.MkvUtils;
import com.scm.fi.common.util.Pair;
import com.scm.fi.common.util.Utils;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.assertEquals;

//@ExtendWith(SpringExtension.class) //the JUnit 5 equivalent to JUnit 4's @RunWith(SpringRunner.class), enabling support for Spring features in the test.
// tells Spring Boot to look for a main configuration class (one with @SpringBootApplication, for instance) and use that to start a Spring application context.
@ComponentScan({"com.scm.fi.Utils"})
@ActiveProfiles("test") // Use this if you have a specific profile for tests
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class UtilTests {
    Logger log = (Logger) LoggerFactory.getLogger(MkvUtils.class);
    final int MAX_PRECISION = 8;

    @Test
    @Order(1)
    public void passwordDecryptionTest() {
        String encryptedPassword = ":5F EF FD D0 AF FA ED 67 94 C8 D1 A1 A9 83 AB 3D";
        String serviceName = "PUB";
        String expectedPassword = "fietoapp_123";
        String decryptedPassword = MkvUtils.decryptJinitPassword(encryptedPassword, serviceName);
        // System.out.printf("encryptedPassword: '%s' -> Decrypted password: '%s', expectedPassword: '%s'.%n\n",
        //        encryptedPassword, decryptedPassword, expectedPassword);
        assertEquals(expectedPassword, decryptedPassword);
        String encryptedPassword2 = ":5F EF FD D0 AF FA ED 67 94 C8 D1 A1 A9 83 AB";
        Level originalLevel = log.getLevel();
        log.setLevel(Level.OFF); // Suppress logging to avoid expected error messages
        decryptedPassword = MkvUtils.decryptJinitPassword(encryptedPassword2, serviceName);
        log.setLevel(originalLevel);
        assertEquals(null, decryptedPassword);
    }

    @Test
    @Order(2)
    public void testDoubleToString() {
        @SuppressWarnings("unchecked")
        Pair<String, Double>[] edgeCases  = new Pair[] {
                new Pair<>("6349070.0", 6349069.999999999),
                new Pair<>("10.0",            9.9999999999),
                new Pair<>("0.0", 0.0),
                new Pair<>("0.1", 0.1),
                new Pair<>("0.12", 0.12),
                new Pair<>("0.123", 0.123),
                new Pair<>("0.123", 0.123),
                new Pair<>("188951.2",          188951.19999999998),
                new Pair<>("6789.12345679",     6789.123456789123456789),
                new Pair<>("6789.12345679",     6789.123456789129456789),
                new Pair<>("56789.12345679",    56789.123456789123456789),
                new Pair<>("456789.12345679",   456789.123456789123456789),
                new Pair<>("3456789.12345679",  3456789.123456789123456789),
                new Pair<>("3456789.12345679",  3456789.123456789123456789),
                new Pair<>("123456789.12345679",123456789.123456789123456789),
                new Pair<>("123456789.12345679",123456789.123456789123456785),
                new Pair<>("123456789.12345675",123456789.12345674912345),
                new Pair<>("123456789.1234567", 123456789.1234567),
                new Pair<>("9999.99", 9999.99),
                new Pair<>("125.81506847",    125.815068469999999),
                new Pair<>("125.81506847",    125.81506846999999),
                new Pair<>("125.81506847",    125.8150684699999),
                new Pair<>("125.81506847",    125.815068469999),
                new Pair<>("125.81506847",    125.81506846999),
                new Pair<>("125.81506847",    125.8150684699),
                new Pair<>("34.0",            34.0000000001),
                new Pair<>("34.0",            34.0000000004),
                new Pair<>("34.00000001",     34.000000005),
                new Pair<>("34.00000001",     34.000000008),
                new Pair<>("34.00000001",     34.00000001),
                // Special values
                new Pair<>("NaN", Double.NaN),
                new Pair<>("Infinity", Double.POSITIVE_INFINITY),
                new Pair<>("-Infinity", Double.NEGATIVE_INFINITY),
                // Very large/small numbers
                new Pair<>("1234567890123456.0", 1234567890123456.0),
                new Pair<>("0.00000001", 0.00000001), // Very small value
                // Negatives, especially around zero
                new Pair<>("0.0", -0.0), // Negative zero
                new Pair<>("-0.1", -0.1),
                new Pair<>("-34.00000001", -34.000000005),
                // Rounding at boundary cases
                new Pair<>("0.5", 0.49999999995), // Just below half, should round to 0.50
                new Pair<>("0.5", 0.5),            // Exactly half
                new Pair<>("0.5", 0.50000000001), // Just above half
                // Numbers with lots of 9s (rounding cascade)
                new Pair<>("1.0", 0.99999999999),
                new Pair<>("10.0", 9.99999999999),
                new Pair<>("100.0", 99.99999999999),
                // Precision boundary at exactly 2 decimal places
                new Pair<>("5.2", 5.20),
                new Pair<>("5.2", 5.200000000),
                // Repeating decimals
                new Pair<>("0.33333333", 1.0/3.0),
                new Pair<>("0.66666667", 2.0/3.0),
                // Min/Max values
                new Pair<>("179769313486231570000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000.0", Double.MAX_VALUE),
                new Pair<>("0.0", Double.MIN_VALUE),
                // Cases where stripTrailingZeros might affect behavior
                new Pair<>("123.0", 123.0),
                new Pair<>("123.4", 123.4000),
                new Pair<>("123.45", 123.450),
                // Mixed precision around 2 digits
                new Pair<>("1.99", 1.99),
                new Pair<>("1.999", 1.999),
                new Pair<>("1.99999999", 1.99999999),
                new Pair<>("2.0",        1.999999999),
                // Values close to integer boundaries
                new Pair<>("999.99999999", 999.99999999),
                new Pair<>("1000.0",       999.999999999),
                new Pair<>("0.0",          1.0E-9),
                new Pair<>("0.00000001",   1.0E-8),
                // Values with exactly MAX_PRECISION decimal places
                new Pair<>("0.13456789", 0.1345678901), // 11 digits
                new Pair<>("0.12345679",  0.12345678951), // 11+4 digits (should truncate)
                new Pair<>("6349070.0", 6349069.999999999),
                new Pair<>("7246560.0", 7246559.999999999)
        };
        System.out.println("Max precision after dot: " + MAX_PRECISION);
        for (Pair<String, Double> num : edgeCases) {
            String result = Utils.doubleToString(num.getValue().doubleValue(), MAX_PRECISION, false);
            System.out.print("double=" + num.getValue() + ",\t converted to String='" + result + "', \texpected='" + num.getKey() + "'");
            if(num.getKey().equals(result))
                System.out.println(" - OK");
            else
                System.out.println(" - ERROR");
            assertEquals(num.getKey(), result);
        }
        System.out.println("\n*************With Exponent **************************");
        Pair<String, Double>[] edgeCasesExp  = new Pair[] {
                new Pair<>("0.0", 0.0),
                new Pair<>("0.1", 0.1),
                new Pair<>("0.12", 0.12),
                new Pair<>("0.123", 0.123),
                new Pair<>("0.123", 0.123),
                new Pair<>("188951.2",          188951.19999999998),
                new Pair<>("6789.12345679",     6789.123456789123456789),
                new Pair<>("6789.12345679",     6789.123456789129456789),
                new Pair<>("56789.12345679",    56789.123456789123456789),
                new Pair<>("456789.12345679",   456789.123456789123456789),
                new Pair<>("3456789.12345679",  3456789.123456789123456789),
                new Pair<>("3456789.12345679",  3456789.123456789123456789),
                new Pair<>("1.2345678912345679E8",123456789.123456789123456789),
                new Pair<>("1.2345678912345679E8",123456789.123456789123456785),
                new Pair<>("1.2345678912345675E8",123456789.12345674912345),
                new Pair<>("1.234567891234567E8",123456789.1234567),
                new Pair<>("9999.99", 9999.99),
                new Pair<>("125.81506847",    125.815068469999999),
                new Pair<>("125.81506847",    125.81506846999999),
                new Pair<>("125.81506847",    125.8150684699999),
                new Pair<>("125.81506847",    125.815068469999),
                new Pair<>("125.81506847",    125.81506846999),
                new Pair<>("125.81506847",    125.8150684699),
                new Pair<>("34.0",            34.000000001),
                new Pair<>("34.0",            34.000000004),
                new Pair<>("34.00000001",     34.000000005),
                new Pair<>("34.00000001",     34.000000008),
                new Pair<>("34.0",            34.000000001),
                // Special values
                new Pair<>("NaN", Double.NaN),
                new Pair<>("Infinity", Double.POSITIVE_INFINITY),
                new Pair<>("-Infinity", Double.NEGATIVE_INFINITY),
                // Very large/small numbers
                new Pair<>("1.234567890123456E15", 1234567890123456.0),
                new Pair<>("1.0E-9", 0.000000001), // Very small value
                // Negatives, especially around zero
                new Pair<>("0.0", -0.0), // Negative zero
                new Pair<>("-0.1", -0.1),
                new Pair<>("-34.00000001", -34.000000005),
                // Rounding at boundary cases
                new Pair<>("0.5", 0.499999995), // Just below half, should round to 0.50
                new Pair<>("0.5", 0.5),            // Exactly half
                new Pair<>("0.5", 0.500000001), // Just above half
                // Numbers with lots of 9s (rounding cascade)
                new Pair<>("1.0", 0.999999999),
                new Pair<>("10.0", 9.999999999),
                new Pair<>("100.0", 99.999999999),
                // Precision boundary at exactly 2 decimal places
                new Pair<>("5.2", 5.20),
                new Pair<>("5.2", 5.20000000),
                // Repeating decimals
                new Pair<>("0.33333333", 1.0/3.0),
                new Pair<>("0.66666667", 2.0/3.0),
                // Min/Max values
                new Pair<>("1.7976931348623157E308", Double.MAX_VALUE),
                new Pair<>("4.9E-324", Double.MIN_VALUE),
                // Cases where stripTrailingZeros might affect behavior
                new Pair<>("123.0", 123.0),
                new Pair<>("123.4", 123.4000),
                new Pair<>("123.45", 123.450),
                // Mixed precision around 2 digits
                new Pair<>("1.99", 1.99),
                new Pair<>("1.999", 1.999),
                new Pair<>("1.99999999",   1.99999999),
                new Pair<>("2.0",          1.999999999),
                // Values close to integer boundaries
                new Pair<>("999.99999999",  999.99999999),
                new Pair<>("1000.0",        999.999999999),
                new Pair<>("1.0E-12",       1.0E-12),
                new Pair<>("1.0E-11",  1.0E-11),
                // Values with exactly MAX_PRECISION decimal places
                new Pair<>("0.12345679", 0.1234567890), // 11 digits
                new Pair<>("0.12345679", 0.1234567890), // 11+4 digits (should truncate)
                // Values with MAX_PRECISION+1 decimal places to test rounding
                new Pair<>("0.12345679", 0.123456789015), // Rounds to 0.12345678902 with HALF_UP
                new Pair<>("6349070.0", 6349069.999999999),
                new Pair<>("7246560.0", 7246559.999999999)
        };
        for (Pair<String, Double> num : edgeCasesExp) {
            String result = Utils.doubleToString(num.getValue().doubleValue(), MAX_PRECISION, true);
            System.out.print("double=" + num.getValue() + ",\t converted to String='" + result + "', \texpected='" + num.getKey() + "'");
            if(num.getKey().equals(result))
                System.out.println(" - OK");
            else
                System.out.println(" - ERROR");
            assertEquals(num.getKey(), result);
        }
    }
}
