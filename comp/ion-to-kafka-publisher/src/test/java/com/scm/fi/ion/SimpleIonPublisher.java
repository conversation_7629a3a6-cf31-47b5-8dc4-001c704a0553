package com.scm.fi.ion;

import com.iontrading.mkv.*;
import com.iontrading.mkv.enums.MkvFieldType;
import com.iontrading.mkv.exceptions.MkvException;
import com.iontrading.mkv.helper.MkvSupplyFactory;
import com.iontrading.mkv.qos.MkvQoS;
import com.scm.fi.common.util.MkvLogger;
import com.scm.fi.common.util.Utils;

import java.util.Random;
/**
 * This sample shows how to leverage the Ultra Low Latency message passing on same-host connections.
 * The application code is a standard Publisher code,
 * since the usage of Ultra Low Latency message passing is completely transparent to the application.
 * Publisher and Subscriber needs to be running on the same-host and connected with one another.
 * To enable the Ultra Low Latency data transfer among components running on the same host
 * both the Publisher and Subscriber component shall be configured with the following setting:
 *   mkv.ultra_low_latency = 1
 * A shared memory driver process needs to be running on the same host:
 * see the "Java API Ultra Low Latency Driver" package associated with Java API product
 * on the ION Tracker Website, for further information.
 * Latency performance can be inspected using the ION Performance Meter tool,
 * focusing on the E2E Latencies table.
 */
public class SimpleIonPublisher
{
    private static MkvLogger log = null;
    private Random random = new Random();
    private String instruments[] = new String[] {
            "EUR_ab3m_25y", "EUR_ab6m_14y", "EUR_ab6m_01y", "EUR_ab3m_03y",
            "EUR_ab3m_05y", "EUR_ab6m_08y", "EUR_ab6m_02y", "EUR_ab3m_20y"
    };
    private int incrementalSupplyFields[];
    private String incSupplyFieldNames[];

    private MkvType type;
    private MkvChain chain;
    private MkvPattern pattern;
    private MkvRecord records[];

    public static void main(String[] args) {
        String configPath = "comp/ion-to-kafka-publisher/src/test/resources/mkv_IonTestPublisher.jinit";
        String jmkvArgs[] = new String[] { "-init", configPath};
        // Verify config file exists
        java.io.File configFile = new java.io.File(configPath);
        if (!configFile.exists()) {
            System.err.println("Configuration file not found: " + configFile.getAbsolutePath());
            return;
        }

        MkvQoS qos = new MkvQoS();
        qos.setArgs(jmkvArgs);

        try {
            /* Register the component onto the ION Platform */
            Mkv mkv = Mkv.start(qos);
            System.out.println("SimpleIonPublisher Start");
            log =  new MkvLogger();
            log.setLogLevel(MkvLogger.LOG_LEVEL.TRACE);
            String source = mkv.getProperties().getProperty("source");
            System.out.println("Source property: '" + source + "'");

            //log.setLogLevel(MkvLogger.LOG_LEVEL.DEBUG);
            log.info("sysUtilsKind=" + Mkv.sysUtilsKind() + ", LastRegistrationDaemon:" + mkv.getLastRegistrationDaemon() +
                ", Component: " + Utils.getComponentName() + ", version: " + mkv.getApplicationVersion());
            // Add connection verification
            if (mkv.getLastRegistrationDaemon() == null) {
                log.error("Failed to connect to ION platform - no registration daemon");
                return;
            }
            // Try to publish a simple type
            MkvType testType = new MkvType("TestType", new String[]{"field1"}, new MkvFieldType[]{MkvFieldType.STR});
            testType.publish();
            System.out.println("TestType published successfully");

            /* Start business level activity */
            new SimpleIonPublisher();
        } catch (MkvException e) {
            e.printStackTrace();
        }
    }

    public SimpleIonPublisher() throws MkvException {
        try
        {
            publishAll();
            startIncrementalSupplyThread();
        } catch (MkvException e) {
            log.error("Failed to initialize publisher: " + e.getMessage(), e);
            throw e;
        }
    }

    private void publishAll() throws MkvException {
        try {
            String source = Mkv.getInstance().getProperties().getProperty("source");
            System.out.println("Publishing with source: '" + source + "'");

            if (source == null || source.trim().isEmpty()) {
                System.out.println("❌ WARNING: Source property is null or empty!");
            }

            // Publish type with verification
            type = new MkvType("Type",
                    new String[] {
                            "Id", "Trader", "Active", "Mid", "NLegs",
                            "L0_InstrumentId", "L0_MidPrice", "L0_MidYield",
                            "L1_InstrumentId", "L1_MidPrice", "L1_MidYield"
                    },
                    new MkvFieldType[] {
                            MkvFieldType.STR, MkvFieldType.STR, MkvFieldType.INT, MkvFieldType.REAL, MkvFieldType.INT,
                            MkvFieldType.STR, MkvFieldType.REAL, MkvFieldType.REAL,
                            MkvFieldType.STR, MkvFieldType.REAL, MkvFieldType.REAL
                    });

            type.publish();
            System.out.println("✅ Type published: " + type.getName());
            log.debug("Publish type: " +  type.getName());

            incrementalSupplyFields = new int[] {
                    type.getFieldIndex("L0_MidPrice"), type.getFieldIndex("L0_MidYield"),
                    type.getFieldIndex("L1_MidPrice"), type.getFieldIndex("L1_MidYield")
            };
            incSupplyFieldNames = new String[] {"L0_MidPrice", "L0_MidYield", "L1_MidPrice", "L1_MidYield"};

            // Publish chain with verification
            String chainName = "EUR.QUOTE." + source + ".QUOTE";
            chain = new MkvChain(chainName, "Type");
            chain.publish();
            System.out.println("✅ Chain published: " + chainName);
            log.debug("Publish Chain: " +  chain.getName());

            // Verify chain was published (wait a moment first)
            Thread.sleep(2000);
            try {
                MkvChain verifyChain = new MkvChain(chainName, "Type");
                System.out.println("✅ Chain verification successful: " + verifyChain.getName());
            } catch (Exception e) {
                System.out.println("❌ Chain verification failed: " + e.getMessage());
                System.out.println("Attempted chain name: " + chainName);
            }

            pattern = new MkvPattern("EUR.QUOTE." + source + ".", "Type");
            pattern.publish();
            System.out.println("✅ Pattern published: " + pattern.getName());
            log.debug("Publish pattern: " +  pattern.getName());

            /* Instantiate all the records and add them to the chain */
            records = new MkvRecord[instruments.length];
            for (int i = 0; i < instruments.length; ++i) {
                records[i] = new MkvRecord("EUR.QUOTE." + source + "." + instruments[i], "Type");
                records[i].publish();
                System.out.println("✅ Record published: " + records[i].getName());
                log.debug("Publish record: " +  records[i].getName());

                supplyAll(records[i]);
                log.debug("supplyAll record: " +  records[i].getOrig() + ", type:" + records[i].getType());

                chain.add(records[i].getName());
            }

            System.out.println("✅ All publications completed successfully!");

        } catch (MkvException e) {
            System.out.println("❌ Publication failed: " + e.getMessage());
            log.error("Failed during publishAll: " + e.getMessage(), e);
            e.printStackTrace();
            throw e;
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        log.info("publishAll finished.");
    }

    private void startIncrementalSupplyThread() {
        /* Start an independent thread for incremental record supplies */
        (new Thread() {
            public void run()
            {
                log.info("Start startIncrementalSupplyThread");
                while (true) {
                    try {
                        for (MkvRecord record : records) {
                            Utils.sleepForMS(1200, 0);
                            supply(record);
                        }
                    } catch(Exception e) {
                        e.printStackTrace();
                    }
                    Utils.sleepForMS(2000, 0);
                }
            }
        }).start();
    } // startIncrementalSupplyThread

    private void supplyAll(MkvRecord record) throws MkvException {
        Object [] val = new Object[] {
                record.getName(), "Trader1", 1, random.nextDouble(), 2,
                "Leg0", random.nextDouble(), random.nextDouble(),
                "Leg1", random.nextDouble(), random.nextDouble()};

        log.debug("Supply record: " + record.getName() + ", values: " + Utils.toString(val));
        /* Supply the initial record snapshot */
        record.supply(MkvSupplyFactory.create(val));
    }

    private void supply(MkvRecord record) throws MkvException {
        Object [] val = new Object[] {
                random.nextDouble(), random.nextDouble(),
                random.nextDouble(), random.nextDouble()
        };
        log.info("Supply record: " + record.getName() + ", fields: " + Utils.toString(incSupplyFieldNames));
        log.debug("Values: " + Utils.toString(val));
        /* Perform incremental record supply */
        record.supply(incSupplyFieldNames, val);
    }
}