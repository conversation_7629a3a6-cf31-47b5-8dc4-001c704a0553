package com.scm.fi.groovy;

import com.scm.fi.Ion2KafkaPubApp;
import com.scm.fi.avro.AvroJsonUtil;
import com.scm.fi.common.util.SAKafkaUtils;
import com.scm.fi.configs.Ion2KafkaFieldMapping;
import com.scm.fi.sa.kafka.producer.KafkaCounters;
import com.scm.fi.sa.kafka.producer.KafkaPublish;
import com.scm.fi.sa.kafka.serializers.plugins.Row;
import com.scm.fi.services.StartupService;
import com.scotia.gcm.sa.v1.MessageHeader;
import com.scotia.gcm.sa.v1.position.PositionMessage;
import com.scotia.gcm.sa.v1.pretrade.OrderMessage;
import com.scotia.gcm.sa.v1.pretrade.RfqMessage;
import org.apache.avro.specific.SpecificRecordBase;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.extension.ExtensionContext;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.ArgumentsProvider;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.mockito.Mock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.Instant;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(SpringExtension.class) //the JUnit 5 equivalent to JUnit 4's @RunWith(SpringRunner.class), enabling support for Spring features in the test.
// tells Spring Boot to look for a main configuration class (one with @SpringBootApplication, for instance) and use that to start a Spring application context.
@SpringBootTest(classes = Ion2KafkaPubApp.class) // Specify your main application class
@ComponentScan({"com.scm.fi.groovy"})
@ActiveProfiles("test") // Use this if you have a specific profile for tests
@Import({KafkaPublish.class})
class GroovyEvaluationTest
{
    private static final Logger log = LoggerFactory.getLogger(GroovyEvaluationTest.class);

    @Mock
    // is used to add mock versions of the specified beans to the application context. Any existing bean of the same type defined in the context will be replaced with the mock.
    private StartupService startupService;
    @Mock
    private KafkaPublish kafkaPublish;

    @Autowired
    private GroovyEvaluation groovyEval;
    private static String TSFromJsonTestFiles = "2024-05-23T00:59:12.788Z";
    private KafkaCounters kafkaCounters = new KafkaCounters();

    //@BeforeAll
    //public static void setup() {
    //    System.setProperty("java.vm.options", "-XX:+EnableDynamicAgentLoading");
    //}

    static class CustomArgumentsProvider implements ArgumentsProvider
    {
        @Override
        public Stream<? extends Arguments> provideArguments(ExtensionContext context) {
        // mapping folder is for Groovy scripts supports java 17
        return Stream.of(
            //Arguments.of("json/row-trade-tor.json",    "trade/trade-tor.groovy",           TradeMessage.class,    "ist2.enterprise.mmi-tor.trade.realtime.v1"));
            //Arguments.of("json/row-trade-ny.json",     "trade/trade-ny.groovy",            TradeMessage.class,    "ist2.enterprise.mmi-ny.trade.realtime.v1"),
            //Arguments.of("json/row-trade-lon.json",    "trade/trade-lon.groovy",           TradeMessage.class,    "ist2.enterprise.mmi-lon.trade.realtime.v1"),
            Arguments.of("json/row-rfq-lon.json",        "rfq/rfq-lon.groovy", RfqMessage.class,          "ist2.landing.mmi-tor.instrument.realtime"),
            Arguments.of("json/row-instrument-tor.json", "instrument/instrument-tor.groovy", Row.class,          "ist2.landing.mmi-tor.instrument.realtime"),
            Arguments.of("json/row-position-tor.json", "position/position-tor.groovy",     PositionMessage.class, "ist2.enterprise.mmi-tor.position.realtime.v1"),
            Arguments.of("json/row-position-lon.json", "position/position-lon.groovy",     PositionMessage.class, "ist2.enterprise.mmi-lon.position.realtime.v1"),
            Arguments.of("json/row-position-ny.json",  "position/position-ny.groovy",      PositionMessage.class, "ist2.enterprise.mmi-ny.position.realtime.v1"),
            Arguments.of("json/row-order-tor.json",    "order/order-tor.groovy",           OrderMessage.class,    "ist2.enterprise.mmi-tor.order.realtime.v1"),
            Arguments.of("json/row-order-ny.json",     "order/order-ny.groovy",            OrderMessage.class,    "ist2.enterprise.mmi-ny.order.realtime.v1"),
            Arguments.of("json/row-order-lon.json",    "order/order-lon.groovy",           OrderMessage.class,    "ist2.enterprise.mmi-lon.order.realtime.v1"));
        }
    } //CustomArgumentsProvider

    @ParameterizedTest
    @ArgumentsSource(CustomArgumentsProvider.class)
    void targetObjectBuildTest(String rowInputJsonFile, String groovyMappingScript, Class<?> targetClass, String targetTopic)
    {
        SpecificRecordBase targetMessage = null;
        try
        {
            // GroovyEvaluation(@Value("${app.config.path}") String appConfigPath, @Value("${app.config.mapping}") String mappingScriptFolder) {
            if(null == groovyEval)
                groovyEval = new GroovyEvaluation("", groovyMappingScript, "");
            log.info("\n***************************************** Row=" + rowInputJsonFile + " for: " + groovyMappingScript + " " +
                    "Started ********************************************");
            targetMessage = genTargetTestObject(rowInputJsonFile, groovyMappingScript, targetClass);

            if (targetMessage != null) {
                log.info("Mapping groovy script was evaluated successfully: " + groovyMappingScript);
                // get(0) - means get header
                Instant ts = ((MessageHeader)targetMessage.get(0)).getSourceSystemCreationTimestamp();
                Instant expectedTS = Instant.parse(TSFromJsonTestFiles);
                String ErrorMsg = "Time Stamp from the header is: " + ts + ", expected TS: " + expectedTS;
                if(!expectedTS.equals(ts)) {
                    log.error(ErrorMsg);
                }
                assertEquals(expectedTS, ts, ErrorMsg);

                String messageId = SAKafkaUtils.getMessageId(targetMessage);
                kafkaPublish.send(messageId, targetTopic, targetMessage, null, kafkaCounters);
                log.info("{} - test message was send to Kafka topic: {}, message class: {}", messageId, targetTopic, targetClass.getName());
                avroTests(targetMessage, targetClass, rowInputJsonFile);
            }
            else {
                String ErrorMsg = "Mapping groovy script was FAILED to evaluate: " + groovyMappingScript;
                log.error(ErrorMsg);
                fail(ErrorMsg);
            }
        }
        catch(Exception ex){
            ex.printStackTrace();
            log.error("Mapping groovy script was FAILED to evaluate: " + groovyMappingScript + ": ", ex);
            fail(ex.getMessage());
        }
    } //groovyEval

    /**
     * Serialization/DeSerialization test
     * @param targetObj - object for Serialization/DeSerialization
     * @return true - test succeeded
     */
    boolean avroTests(SpecificRecordBase targetObj, Class<?> targetClass, String rowInputJsonFile) {
        SpecificRecordBase generatedObj = null;
        try {
            if(null == targetObj || null == targetClass) {
                log.error("targetObj is null! targetClass={}", targetClass);
                return false;
            }
            MessageHeader header = ((MessageHeader)targetObj.get(0));
            if(null == header) {
                log.error("Header is null!!" );
                return false;
            }

            String jsonStr = AvroJsonUtil.avroObjToJson(targetObj);
            generatedObj = AvroJsonUtil.jsonToAvroObj(jsonStr, targetClass);
            if(targetObj.equals(generatedObj)) {
                log.info("{}: {} -> JSON -> {} -> Equal", rowInputJsonFile, targetClass.getCanonicalName(), targetClass.getCanonicalName());
                return true;
            }
            assertEquals(targetObj, generatedObj, "Avro Serialization/DeSerialization failed: source object not equal to target!");
        } catch(Exception ex) {
            log.error("rowInputJsonFile: {}: Avro Serialization/DeSerialization failed. Target class: {} ", rowInputJsonFile, targetClass.getCanonicalName(), ex);
            assertFalse(true, "Avro Serialization/DeSerialization failed");
        }
        return false;
    }

    /**
     *
     * @param rowFileName - input json file
     * @return Rom landing object
     * @throws Exception - throws all exceptions
     */
    public Row rowObjectReadFromJson(final String rowFileName) throws Exception {
        Row row;
        File rowJsonFile = null;
        try {
            // Read row-position-tor.json file into a string
            ClassLoader classLoader = getClass().getClassLoader();
            URL rowFileUrl = classLoader.getResource(rowFileName);
            rowJsonFile = new File(rowFileUrl.getFile());
            Path rowJsonPath = rowJsonFile.toPath();
            log.info("rowInputJsonFile=" + rowJsonPath.toAbsolutePath());
            String rowJson = Files.newBufferedReader(rowJsonPath).lines().collect(Collectors.joining("\n"));
            // Convert row-position-tor.json to a Java object
            //row = (Row)AvroJsonUtil.fromJson(rowJson, Row.class);
            row = (Row)AvroJsonUtil.jsonToAvroObj(rowJson, Row.class);
        } catch (NullPointerException e) {
            if (rowJsonFile == null)
                log.error("rowObjectReadFromJson: Can not find row file: " + rowFileName + " :" + e.getMessage());
            else
                log.error("rowObjectReadFromJson: " + e.getMessage());
            throw e;
        } catch (IOException e) {
            log.error("rowObjectReadFromJson: ", e);
            throw e;
        }
        return row;
    } // rowObjectReadFromJson()

    private SpecificRecordBase genTargetTestObject(final String rowInputJsonFile, final String groovyMappingScript, Class<?> targetClass)
    {
        SpecificRecordBase targetMessage = null;
        try {
            // Step#1: Convert input json (row object input for groovy scripts) file (like row-position-tor.json) to row object
            Row row = rowObjectReadFromJson(rowInputJsonFile);
            Ion2KafkaFieldMapping map = new Ion2KafkaFieldMapping();
            map.setEnable(true);
            map.setTargetClassName(targetClass.getName());
            map.setGroovyMappingScript(groovyMappingScript);
            map.setChain("CAD.TEST.CHAIN.NAME");
            //groovyEval.setAppConfigPath("config/");
            groovyEval.setIonToKafkaAvroSerializerMsgIdGen(map.getTargetClassName(), map.getGroovyMappingScript());
            targetMessage = groovyEval.targetObjectBuild(row);
        }
        catch(Exception ex)
        {
            log.error("genTargetTestObject: ", ex);
        }
        return targetMessage;
    } // genTargetTestObject()
}
