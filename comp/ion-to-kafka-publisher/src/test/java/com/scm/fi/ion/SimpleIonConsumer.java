package com.scm.fi.ion;

import com.iontrading.mkv.Mkv;
import com.iontrading.mkv.MkvChain;
import com.iontrading.mkv.MkvRecord;
import com.iontrading.mkv.MkvType;
import com.iontrading.mkv.enums.MkvChainAction;
import com.iontrading.mkv.events.MkvChainListener;
import com.iontrading.mkv.qos.MkvQoS;

import java.util.Arrays;

public class SimpleIonConsumer {
    public static void main(String[] args) {
        try {
            String jmkvArgs[] = new String[] {
                    "-init", "comp/ion-to-kafka-publisher/src/test/resources/mkv_IonTestConsumer.jinit"
            };

            MkvQoS qos = new MkvQoS();
            qos.setArgs(jmkvArgs);
            Mkv mkv = Mkv.start(qos);

            System.out.println("Connected, waiting for publisher data...");
            Thread.sleep(3000);
            String source = mkv.getProperties().getProperty("source");
            System.out.println("Source property: '" + source + "'");

            // Subscribe to the chain for real-time updates
            MkvChain chain = new MkvChain("EUR.QUOTE.ION_TEST_PUB_TOR.QUOTE", "Type");

            MkvChainListener listener = new MkvChainListener() {
                public void onUpdate(MkvChain chain, MkvRecord record, String[] fields, Object[] values) {
                    System.out.println("\n🔄 LIVE UPDATE RECEIVED!");
                    System.out.println("Record: " + record.getName());
                    System.out.println("Fields: " + Arrays.toString(fields));
                    System.out.println("Values: " + Arrays.toString(values));
                    System.out.println("Time: " + new java.util.Date());
                }

                public void onInsert(MkvChain chain, MkvRecord record) {
                    System.out.println("➕ New record added: " + record.getName());
                }

                public void onDelete(MkvChain chain, MkvRecord record) {
                    System.out.println("➖ Record deleted: " + record.getName());
                }

                public void onSupply(MkvChain chain, String recordName, int recordIndex, MkvChainAction action) {
                    System.out.println("📊 Supply event: " + recordName + " action: " + action);
                }
            };

            chain.subscribe(listener);
            System.out.println("✅ Subscribed to chain, listening for updates...");
            System.out.println("You should see live updates every ~1.2 seconds per record");
            System.out.println("Press Ctrl+C to stop");

            // Keep running
            while (true) {
                Thread.sleep(1000);
            }

        } catch (Exception e) {
            System.out.println("Error: " + e.getLocalizedMessage() + ", cause: " + e.getCause() + ", class=" + e.getClass().toString());
            e.printStackTrace();
            System.exit(1);
        }
    }

    public static String[] getFieldNames(MkvRecord record) {
        MkvType type = record.getMkvType();
        if(null == type)
            return new String[0];
        String[] fieldNames = new String[type.size()];
        // Try to get field names
        for (int i = 0; i < type.size(); i++) {
            fieldNames[i] = type.getFieldName(i);
        }
        return fieldNames;
    }
    public static String[] getFieldValues(MkvRecord record) {
        MkvType type = record.getMkvType();
        if(null == type)
            return new String[0];
        String[] fieldValues = new String[type.size()];
        // Try to get field names
        for (int i = 0; i < type.size(); i++) {
            fieldValues[i] = record.getValue(i).getString();
        }
        return fieldValues;
    }

}