package com.scm.fi.ion;

import com.iontrading.mkv.Mkv;
import com.iontrading.mkv.MkvChain;
import com.iontrading.mkv.MkvRecord;
import com.iontrading.mkv.enums.MkvChainAction;
import com.iontrading.mkv.events.MkvChainListener;
import com.iontrading.mkv.qos.MkvQoS;
import com.scm.fi.common.util.MkvLogger;

import java.util.Arrays;

public class SimpleIonConsumer {
    private static MkvLogger log;

    public static void main(String[] args) {
        try {
            String jmkvArgs[] = new String[] {
                    "-init", "comp/ion-to-kafka-publisher/src/test/resources/mkv_IonTestConsumer.jinit"
            };

            MkvQoS qos = new MkvQoS();
            qos.setArgs(jmkvArgs);
            Mkv mkv = Mkv.start(qos);

            log = new MkvLogger();
            log.setLogLevel(MkvLogger.LOG_LEVEL.DEBUG);

            System.out.println("Consumer connected to: " + mkv.getLastRegistrationDaemon());

            // Create the chain
            MkvChain chain = new MkvChain("EUR.QUOTE.ION_TEST_PUB_TOR.QUOTE", "Type");

            // Create listener - remove @Override annotations to avoid signature issues
            MkvChainListener listener = new MkvChainListener() {
                public void onUpdate(MkvChain chain, MkvRecord record, String[] fields, Object[] values) {
                    System.out.println("=== RECEIVED UPDATE ===");
                    System.out.println("Record: " + record.getName());
                    System.out.println("Fields: " + Arrays.toString(fields));
                    System.out.println("Values: " + Arrays.toString(values));
                    System.out.println("Timestamp: " + System.currentTimeMillis());
                    System.out.println("========================");
                }

                public void onInsert(MkvChain chain, MkvRecord record) {
                    System.out.println("NEW RECORD INSERTED: " + record.getName());
                }

                public void onDelete(MkvChain chain, MkvRecord record) {
                    System.out.println("RECORD DELETED: " + record.getName());
                }

                public void onSupply(MkvChain chain, String recordName, int recordIndex, MkvChainAction action) {
                    System.out.println("SUPPLY EVENT: " + recordName + " index: " + recordIndex + " action: " + action);
                }
            };

            // Subscribe with the listener
            chain.subscribe(listener);

            log.info("Subscribed to chain: " + chain.getName());

            // Wait a moment for subscription to complete
            Thread.sleep(2000);

            // Check existing records in the chain
            MkvRecord[] records = chain.getRecords();
            System.out.println("Found " + records.length + " existing records in chain");

            for (MkvRecord record : records) {
                System.out.println("Existing record: " + record.getName());

                // Get current values
                try {
                    String[] fieldNames = {"L0_MidPrice", "L0_MidYield", "L1_MidPrice", "L1_MidYield"};
                    Object[] currentValues = record.getValues(fieldNames);
                    System.out.println("  Current values: " + Arrays.toString(currentValues));
                } catch (Exception e) {
                    System.out.println("  Could not get values: " + e.getMessage());
                }
            }

            System.out.println("Consumer started, waiting for updates...");
            System.out.println("Press Ctrl+C to stop");

            // Keep running to receive updates
            while (true) {
                Thread.sleep(1000);
            }

        } catch (Exception e) {
            System.err.println("Consumer failed: " + e.getMessage());
            e.printStackTrace();
        }
    }
}