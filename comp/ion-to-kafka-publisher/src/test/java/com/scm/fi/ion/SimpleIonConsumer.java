package com.scm.fi.ion;

import com.iontrading.mkv.Mkv;
import com.iontrading.mkv.MkvRecord;
import com.iontrading.mkv.MkvType;
import com.iontrading.mkv.qos.MkvQoS;

public class SimpleIonConsumer {
    public static void main(String[] args) {
        try {
            String jmkvArgs[] = new String[] {
                    "-init", "comp/ion-to-kafka-publisher/src/test/resources/mkv_IonTestConsumer.jinit"
            };

            MkvQoS qos = new MkvQoS();
            qos.setArgs(jmkvArgs);
            Mkv mkv = Mkv.start(qos);

            System.out.println("Consumer connected");
            Thread.sleep(3000);

            // Test one specific record
            String recordName = "EUR.QUOTE.ION_TEST_PUB_TOR.EUR_ab3m_25y";

            try {
                MkvRecord record = new MkvRecord(recordName, "Type");
                System.out.println("✅ Record accessible: " + recordName);

                // Get all field names
                String[] fields = getFieldNames(record);
                System.out.println("Total fields: " + fields.length);

                // Print first 10 fields and their values
                for (int i = 0; i < Math.min(10, fields.length); i++) {
                    if (fields[i] != null) {
                        try {
                            Object value = record.getValue(i);
                            System.out.println("  [" + i + "] " + fields[i] + " = " + value);
                        } catch (Exception e) {
                            System.out.println("  [" + i + "] " + fields[i] + " = error: " + e.getMessage());
                        }
                    }
                }

                // Try specific fields by name
                System.out.println("\nTrying specific fields:");
                String[] targetFields = {"L0_MidPrice", "L0_MidYield", "L1_MidPrice", "L1_MidYield"};

                for (String fieldName : targetFields) {
                    try {
                        int index = -1;
                        // Find field index manually
                        for (int i = 0; i < fields.length; i++) {
                            if (fieldName.equals(fields[i])) {
                                index = i;
                                break;
                            }
                        }

                        if (index >= 0) {
                            Object value = record.getValue(index);
                            System.out.println("  " + fieldName + " = " + value);
                        } else {
                            System.out.println("  " + fieldName + " = not found");
                        }

                    } catch (Exception e) {
                        System.out.println("  " + fieldName + " = error: " + e.getMessage());
                    }
                }

                System.out.println("\n✅ PUBLISHER IS WORKING! Data is accessible.");

            } catch (Exception e) {
                System.out.println("❌ Consumer failed: " + e.getMessage() + ", cause: " + e.getCause() + ", class=" + e.getClass().toString());
            }

        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("❌ Consumer failed: " + e.getMessage() + ", cause: " + e.getCause() + ", class=" + e.getClass().toString());
            System.exit(1);
        }
    }
    public static String[] getFieldNames(MkvRecord record) {
        MkvType type = record.getMkvType();
        if(null == type)
            return new String[0];
        String[] fieldNames = new String[type.size()];
        // Try to get field names
        for (int i = 0; i < type.size(); i++) {
            fieldNames[i] = type.getFieldName(i);
        }
        return fieldNames;
    }
    public static String[] getFieldValues(MkvRecord record) {
        MkvType type = record.getMkvType();
        if(null == type)
            return new String[0];
        String[] fieldValues = new String[type.size()];
        // Try to get field names
        for (int i = 0; i < type.size(); i++) {
            fieldValues[i] = record.getValue(i).getString();
        }
        return fieldValues;
    }
}