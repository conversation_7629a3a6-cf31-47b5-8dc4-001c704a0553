mkv.component = ION_TEST_CONSUMER_TOR
mkv.user = apppub
mkv.pwd = apppub123  
#mkv.cshost = sbisvrwm406
#mkv.csport = 24001
#mkv.listen = 24062
mkv.CSHOST=sdusvrwm0128.dev.ib.tor.scotiabank.com
mkv.CSPORT=24001
mkv.LISTEN = *

mkv.logsdir = log/ION_TEST_CONSUMER_TOR
mkv.ZoneId = America/New_York
mkv.logLevel = DEBUG
mkv.logPrintTime = 1
mkv.logPrintThreadName = 1
mkv.logName=ION_TEST_CONSUMER_TOR.log
mkv.logToScreen=true
mkv.source=ION_TEST_CONSUMER_TOR
mkv.thread.pool.size=1
mkv.timer.timeout = 300
mkv.db.connection.pwd=:5F EF FD D0 AF FA ED 67 94 C8 D1 A1 A9 83 AB 3D
mkv.env = UAT
mkv.PWD= :4F 7C 36 27 12 40 86 EF BC A4 4A BD D9 F4 8D CB
mkv.platform.serviceName=PUB

# SSL settings (same as publisher)
mkv.ssl = false
mkv.ssl.enabled = false
mkv.use_ssl = false
mkv.connection.ssl = false
mkv.daemon.ssl = false

# Debug settings
mkv.debug = 1
mkv.debuglevel = 3
mkv.logLevel = DEBUG
mkv.logToScreen = true

# Connection settings
mkv.connection.timeout = 30000
mkv.connection.retry = 3
mkv.timer.timeout = 30
