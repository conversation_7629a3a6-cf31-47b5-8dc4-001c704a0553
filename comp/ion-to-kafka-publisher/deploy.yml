# Properties for CiaD Jenkins deploy pipelines ***************

deployScriptFile: deploy.sh
scriptCommand: -e ${serverName} -f ${deployArtifact} -v ${artifactVersion}

devServers:
  -
    name: DEV
    #address: sctfidlpa004trb.options-it.com
    address: ***************
    deployer:  gfidev_mso_sct
    deployLocation: /cm/gfi/apps/deployment
    deployByDefault: false

stagingServers:
  -
    name: UAT
    address: ***************
    deployer: gfiqa_mso_sct
    deployLocation: /cm/gfi/deployment/
    deployByDefault: false

prodServers:
  -
    name: PRD
    address: ***************
    deployer: gfiprod_mso_sct
    deployLocation: /cm/gfi/deployment/
