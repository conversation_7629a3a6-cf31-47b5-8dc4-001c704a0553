# ION to Kafka Publisher Component

## Overview
A high-performance Java 17/Spring Boot 3.x component that publishes real-time market data from ION Trading Platform (MMI) 
chains to Kafka topics. This enterprise-grade application supports high-throughput data streaming with built-in redundancy,
error handling, and monitoring capabilities.

## Key Features
- **High Performance**: Supports up to 472 transactions/second sustained throughput
- **Real-time Data Streaming**: Publishes ION chain data to Kafka topics in real-time
- **Database Redundancy**: Oracle DB backup for failed Kafka publications with automatic republishing
- **Multi-threading**: Configurable thread pools for optimal performance on 56-core servers
- **Async Processing**: Non-blocking database operations with bounded queues and overflow protection
- **Dynamic Load Adaptation**: Automatically adjusts batch sizes and processing based on load patterns
- **Enterprise Security**: Kerberos authentication, SSL/TLS encryption, and comprehensive security headers
- **Monitoring & Observability**: Built-in metrics, health checks, and Angular-based dashboard

## Application limitations:
1. It supports all (515) SpecificRecordBase based classes ONLY from sa-schema (v1.144) library as target classes to publish on Kafka
2. If class is not in the list (like to publish on landing topic), it has to be manually implemented (See: Row Landing object - corresponding to Ion Chains on Detail Design page)
3. it can publish to original chain only: it can not do merge to existing chain (published from another component the same time)

## Architecture

### Core Components
1. **ION Data Ingestion**: Subscribes to ION Trading Platform chains
2. **Async Database Layer**: High-performance async database operations with 2GB memory-bounded queues
3. **Kafka Publisher**: Publishes processed data to Kafka topics with guaranteed delivery
4. **Web Dashboard**: Angular-based monitoring interface
5. **Database Redundancy**: Oracle DB for backup and republishing failed records

### Technology Stack
- **Runtime**: OpenJDK 17 (RedHat distribution)
- **Framework**: Spring Boot 3.x with Spring Data JPA
- **Database**: Oracle 19c with HikariCP connection pooling
- **Messaging**: Apache Kafka with Confluent Schema Registry
- **Frontend**: Angular 19.1.6 with TypeScript
- **Build**: Maven 3.x
- **Security**: Kerberos (GSSAPI), SSL/TLS

## Project Structure
ion-kafka-publisher/
├── comp/
│   ├── ion-to-kafka-publisher/          # Main application component
│   ├── ion-kafka-common-lib/            # Shared library
│   └── kafka-serializers-plugins/       # Kafka serialization plugins
├── config/                              # Configuration files
│   ├── mmi-ny/                         # New York configurations
│   ├── mmi-tor/                        # Toronto configurations
│   ├── mmi-lon/                        # London configurations
│   ├── kafka/                          # Kafka security configs
│   ├── mapping/                        # Data mapping configurations
│   └── db/                             # Database schemas
└── docs/                               # Documentation

## Related Projects
1. **ion-to-kafka-publisher** - Real-time ION to Kafka publisher (this component)
2. **csv-to-ion-kafka-publisher** - CSV file processing and publishing
3. **ion-kafka-common-lib** - Shared utilities and common functionality
4. **kafka-serializers-plugins** - Kafka serialization plugins for target objects

**Repository**: https://bitbucket.agile.bns/projects/GFI/repos/ion-kafka-publisher/browse

## Full list of new components implemented by this solution:
TOR: KAFKA_PUB1_IST_SS_TOR KAFKA_PUB1_TOR_SS KAFKA_PUB2_IST_SS_TOR KAFKA_PUB2_TOR_SS KAFKA_PUB3_IST_SS_TOR KAFKA_PUB3_TOR_SS KAFKA_PUB4_TOR_IST_SS KAFKA_PUB4_TOR_SS KAFKA_PUB_TRADES_SPLIT_TOR
NY: KAFKA_PUB1_NY_IST_SS KAFKA_PUB1_NY_SS KAFKA_PUB2_IST_SS_NY KAFKA_PUB2_NY_SS KAFKA_PUB3_NY_IST_SS KAFKA_PUB3_NY_SS KAFKA_PUB4_NY_IST_SS KAFKA_PUB4_NY_SS KAFKA_PUB_TRADES_SPLIT_NY
LON: KAFKA_PUB_TRADES_SPLIT_LON KAFKA_PUB_TRADES_SPLIT_IST_LON

Note: In order to publish status, those components needs to be added to PUBLISH_INCLUDE_ORIGINATOR settings router field on:
- ROUTERM_KAFKA_NY for ALl NY components
- ROUTERM_KAFKA_TOR for ALl TOR components
- ROUTERM_KAFKA_LON for ALl LON components 

## Quick Start

### Prerequisites
- **Java 17** (RedHat OpenJDK recommended)
- **Maven 3.6+** for building
- **Oracle Database 19c** access with appropriate schemas
- **Apache Kafka** cluster with Confluent Schema Registry
- **ION Trading Platform** connectivity and credentials
- **Kerberos** authentication setup for Kafka

### Build and Run

```bash
# Clone the repository
git clone https://bitbucket.agile.bns/projects/GFI/repos/ion-kafka-publisher.git
cd ion-kafka-publisher

# Build the project
mvn clean install

# Run with specific configuration (New York example)
java -jar comp/ion-to-kafka-publisher/target/ion-to-kafka-publisher-*.jar \
  -init config/mmi-ny/KAFKA_PUB2_NY_SS/uat2uat/KAFKA_PUB2_NY_WIN_SS.jinit \
  --spring.profiles.active=uat-kafka_pub2_ny_ss \
  --spring.config.location=file:./config/mmi-ny/KAFKA_PUB2_NY_SS/uat2uat/
```

### UAT Command Line Example
```bash
exec /opt/bns/ion/NY/bin/JAVA/JDK/x64/openjdk-17.0.4.1.1-1/bin/java \
  -Djavax.security.auth.useSubjectCredsOnly=true \
  -Dspring.devtools.restart.enabled=false \
  -server -XX:+UseZGC -Xrs -Xms1024m -Xmx4096m -Xss2m \
  -Dloader.path=/opt/bns/ion/NY/bin/KAFKA_PUB/KAFKA_PUB2_NY_SS/kafka-serializers-plugins-1.0.20.jar \
  -Djavax.net.ssl.trustStore=/opt/bns/ion/NY/bin/common/cacerts \
  -Djavax.net.ssl.trustStorePassword=changeit \
  -XX:+HeapDumpOnOutOfMemoryError \
  -XX:HeapDumpPath=/opt/bns/ion/NY/log/KAFKA_PUB/KAFKA_PUB2_NY_SS/heapdump-%t.hprof \
  -XX:+ExitOnOutOfMemoryError \
  -Xlog:gc*,gc+heap=info,gc+ergo=info:/opt/bns/ion/NY/log/KAFKA_PUB/KAFKA_PUB2_NY_SS/gc-%t.log:time,level,tags:filecount=3,filesize=100M \
  -XX:StartFlightRecording=name=DailyRecording,settings=profile,delay=30s,disk=true,dumponexit=true,filename=/opt/bns/ion/NY/log/KAFKA_PUB/KAFKA_PUB2_NY_SS/recording.jfr,maxsize=1G,maxage=2d \
  -Djava.security.auth.login.config=/opt/bns/ion/NY/bin/common/kafka_client_jaas_uat.conf \
  -Djava.security.krb5.conf=/opt/bns/ion/NY/bin/common/krb5_uat.conf \
  -Dsun.security.krb5.disableReferrals=true \
  -jar /opt/bns/ion/NY/bin/KAFKA_PUB/KAFKA_PUB2_NY_SS/ion-to-kafka-publisher-1.0.20.jar \
  --spring.profiles.active=uat-kafka_pub2_ny_ss \
  --spring.config.location=file:/opt/bns/ion/NY/bin/KAFKA_PUB/KAFKA_PUB2_NY_SS/config/ \
  -init /opt/bns/ion/NY/wrk/KAFKA_PUB/KAFKA_PUB2_NY_SS/mkvDS_sdusvrwm0128_NY_KAFKA_PUB2_NY_SS.jinit \
  2>> /opt/bns/ion/NY/log/KAFKA_PUB/KAFKA_PUB2_NY_SS/errors.log
```

## Configuration

### Configuration Layers
The application uses a multi-layered configuration approach:

1. **ION Configuration** (`.jinit` files): ION Trading Platform connection settings
2. **Spring Configuration** (`.yml` files): Application, Kafka, and database settings
3. **Data Mapping** (`.json` files): ION chain to Kafka topic mappings
4. **Security Configuration**: Kerberos, SSL certificates, and authentication
5. **Async Database Configuration**: High-performance database layer settings

### ION Configuration Example

`KAFKA_PUB2_NY_WIN_SS.jinit`:
```properties
mkv.component = KAFKA_PUB2_NY_WIN_SS
mkv.user = apppub
mkv.pwd = apppub123
mkv.CSHOST = sdusvrwm0128.dev.ib.tor.scotiabank.com
mkv.CSPORT = 24001
mkv.LISTEN = 0
mkv.ssl = false
mkv.connection.timeout = 30000
source = KAFKA_PUB2_NY_WIN_SS
```

### Spring Configuration Example
`application-uat-kafka_pub2_ny_ss.yml`:
```yaml
app:
  ionComponentName: KAFKA_PUB2_NY_WIN_SS
  skipDbOperations: false
  republishRecordsFromDB: true
  dataRemovalHours: 48

  # Async Database Configuration
  async:
    database:
      queue:
        maxMemoryMB: 2048
        overflowThreshold: 0.99
        enableOverflowProtection: true
      batch:
        defaultBatchSize: 500
        maxWaitTimeMs: 5000
        processingThreads: 8
      loadDetection:
        mode: auto
        highVolumeThreshold: 100.0
        lowVolumeThreshold: 10.0

spring:
  kafka:
    bootstrap-servers: dp.uat.bns:9030
    producer:
      acks: all
      compression.type: lz4
      batch-size: 131072
      buffer-memory: *********
      retries: 7200
      client-id: ion-mmi_kafka@BNS

  datasource:
    driver-class-name: oracle.jdbc.OracleDriver
    url: *********************************************
    username: FIENYAPP
    password: ":38 5D 52 50 B9 D5 5B 6A B9 60 CA 5F 6F 68 55 5F"
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
```

### Data Mapping Configuration
`dataExtract.json` - Maps ION chains to Kafka topics:
```json
{
  "chains": [
    {
      "chainName": "EUR.QUOTE.KAFKA_PUB2_NY_WIN_SS.QUOTE",
      "kafkaTopic": "mmi-ny-eur-quote",
      "targetClass": "com.bns.sa.schema.v1.EurQuote",
      "fields": ["L0_MidPrice", "L0_MidYield", "L1_MidPrice", "L1_MidYield"]
    }
  ]
}
```
## Performance and Monitoring

### Performance Specifications

- **Sustained Throughput**: Up to 472 transactions/second for 23-30 hours
- **Memory Efficiency**: 2GB bounded queue with overflow protection
- **CPU Utilization**: Optimized for 56-core server architecture
- **Latency**: Sub-10ms queue latency under normal load
- **Scalability**: Linear performance scaling with increased load

### Async Database Layer Features

- **High-Performance Queuing**: Memory-bounded priority queue with overflow protection
- **Batch Processing**: Dynamic batch sizing based on load patterns
- **Circuit Breaker**: Automatic database failure detection and recovery
- **Retry Logic**: Exponential backoff for temporary failures
- **Dead Letter Queue**: Persistent storage for permanently failed records

### Monitoring Dashboard

The application includes an Angular-based monitoring dashboard accessible at:
- **URL**: `http://localhost:8080/dashboard`
- **Features**: Real-time metrics, queue status, throughput graphs, error tracking

### Health Checks

- **Application Health**: `/actuator/health`
- **Database Status**: `/actuator/health/db`
- **Kafka Connectivity**: `/actuator/health/kafka`
- **ION Platform Status**: `/actuator/health/ion`

## Development

### Angular Dashboard Development

To build the Angular monitoring dashboard:

**Prerequisites:**
- Angular CLI: 19.1.6
- NodeJS: 18.20.6
- Package Manager: npm 10.8.2

**Setup:**
```bash
# Configure proxy for corporate network
npm config set proxy http://proxyprd.scotia-capital.com:8080
npm config set https-proxy http://proxyprd.scotia-capital.com:8080

# Install Angular CLI
npm install -g @angular/cli@19.1.6

# Navigate to dashboard directory
cd src/main/resources/static/dashboard

# Install dependencies
npm install

# Build for production
ng build --configuration production

# Development server
ng serve --proxy-config proxy.conf.json
```

### Testing

**Unit Tests:**
```bash
mvn test
```

**Integration Tests:**
```bash
mvn verify -P integration-tests
```

**Performance Tests:**
```bash
mvn test -P performance-tests
```

### Debugging

**Enable Debug Logging:**
```yaml
logging:
  level:
    com.scm.fi: DEBUG
    com.scm.fi.db.async: TRACE
```

**JVM Debug Parameters:**
```bash
-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5005
```

## Deployment

### Environment-Specific Configurations

The application supports multiple environments and regions:

**Environments:**
- `dev` - Development environment
- `uat` - User Acceptance Testing
- `prod` - Production environment

**Regions:**
- `ny` - New York
- `tor` - Toronto
- `lon` - London

### Configuration Structure

```
config/
├── mmi-ny/                    # New York configurations
│   ├── KAFKA_PUB2_NY_SS/
│   │   ├── dev/
│   │   ├── uat2uat/
│   │   └── prod/
├── mmi-tor/                   # Toronto configurations
├── mmi-lon/                   # London configurations
├── kafka/                     # Kafka security configs
│   ├── dev/
│   ├── uat/
│   └── prod/
├── mapping/                   # Data mapping files
└── db/                        # Database schemas
```

### Security Configuration
**Kerberos Setup:**
```properties
# kafka_client_jaas_uat.conf
KafkaClient {
    com.sun.security.auth.module.Krb5LoginModule required
    useKeyTab=true
    storeKey=true
    keyTab="/path/to/kafka.keytab"
    principal="<EMAIL>";
};
```

**SSL Configuration:**
```properties
# Trust store configuration
javax.net.ssl.trustStore=config/kafka/cacerts
javax.net.ssl.trustStorePassword=changeit
javax.net.ssl.trustStoreType=JKS
```

## Troubleshooting

### Common Issues
**1. ION Connection Issues**
```
Error: MkvObjectNotAvailableException
Solution: Check ION platform connectivity and credentials
```

**2. Kafka Authentication Failures**
```
Error: SASL authentication failed
Solution: Verify Kerberos configuration and keytab files
```

**3. Database Connection Issues**
```
Error: Connection timeout
Solution: Check Oracle database connectivity and HikariCP settings
```

**4. Memory Issues**
```
Error: OutOfMemoryError
Solution: Increase heap size or adjust queue memory limits
```

### Log Analysis

**Key Log Patterns:**
```bash
# Successful ION connection
grep "Register.*SUCCESS" logs/application.log

# Kafka publishing success
grep "Successfully published" logs/application.log

# Database operation metrics
grep "Batch.*completed" logs/application.log

# Error patterns
grep -E "ERROR|FAILED|Exception" logs/application.log
```

## Support and Documentation
- **Confluence**: https://confluence.agile.bns/pages/viewpage.action?pageId=*********
- **Repository**: https://bitbucket.agile.bns/projects/GFI/repos/ion-kafka-publisher/browse
- **Issue Tracking**: JIRA GFI project: https://jira.agile.bns/browse/GFI-5318
- **Team Contact**: GFI

## License
Copyright © 2024 Bank of Nova Scotia. All rights reserved.
This software is proprietary and confidential. Unauthorized copying, distribution, or use is strictly prohibited.
