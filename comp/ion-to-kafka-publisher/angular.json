{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"ion-kafka-metrics": {"projectType": "application", "schematics": {}, "root": "", "sourceRoot": "src/main/webapp", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "src/main/resources/static", "index": "src/main/webapp/index.html", "main": "src/main/webapp/main.ts", "polyfills": "src/main/webapp/polyfills.ts", "tsConfig": "tsconfig.json", "assets": ["src/main/webapp/assets"], "styles": ["src/main/webapp/styles.css"], "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "1mb"}], "outputHashing": "none", "fileReplacements": [{"replace": "src/main/webapp/environments/environment.ts", "with": "src/main/webapp/environments/environment.prod.ts"}]}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"browserTarget": "ion-kafka-metrics:build:production"}, "development": {"browserTarget": "ion-kafka-metrics:build:development"}}, "defaultConfiguration": "development"}}}}, "cli": {"analytics": "557fc41c-adb6-4868-bbc2-b25b49b06a5e"}}