#!/bin/ksh

PROGRAM=`basename $0`
USAGE="Usage: ${PROGRAM} [ -e server name ] [ -f file name] [ -v version] "

########################################################################
# get command line options
########################################################################
while getopts e:E:f:F:v:V: c
do
    case $c in
        e | E) SERVER=$OPTARG
        	echo "Server: ${SERVER}"
        	;;
        f | F) PKG_NAME=$OPTARG
        	echo "File name: ${PKG_NAME}"
        	;;
        v | V) VERSION=$OPTARG
        	echo "Version: ${VERSION}"
        	;;   
        \?) echo "${USAGE}"
            exit 1;;
    esac
done

## Default deployment directory, it should be the same as defined in deploy.yml
DEPLOY_BASE_DIR=`pwd`
APP_NAME=`echo ${PKG_NAME}|cut -f1 -d'.'`
## Set variables, RELEASE_VERSION must be set based on each release
RELEASE_VERSION="${APP_NAME}_${VERSION}"
PKG_BASE_DIR=${DEPLOY_BASE_DIR}/$RELEASE_VERSION

# check the tar file
if [[ ! -e "$DEPLOY_BASE_DIR/$PKG_NAME" ]];
then
    echo "`date`: No tar file"
    exit 1
fi

## make sure the archive directory exists
if [ ! -d $DEPLOY_BASE_DIR/archive ];
then
    mkdir $DEPLOY_BASE_DIR/archive
fi

# If the same version exists, archive the existing package folder, then re-deploy it again
if [ -d $PKG_BASE_DIR ];
then
    cd ${DEPLOY_BASE_DIR}
    tar -cvf ${DEPLOY_BASE_DIR}/archive/${RELEASE_VERSION}_`date +"%Y%m%d%H%M%S"`.tar ${DEPLOY_BASE_DIR}/${RELEASE_VERSION}
    \rm -r ${DEPLOY_BASE_DIR}/${RELEASE_VERSION}
    echo "`date`: archived ${RELEASE_VERSION}"
fi
mkdir ${PKG_BASE_DIR}

cd ${PKG_BASE_DIR}
(
    echo "Server: ${SERVER}"
    echo "File name: ${PKG_NAME}"
    echo "Version: ${VERSION}"

    ########################################################################
    # Uncompress the tar file
    ########################################################################
    tar -xvf ${DEPLOY_BASE_DIR}/${PKG_NAME}

    ########################################################################
    # deploy Util scipts if existed
    ########################################################################
    if [ -d deployUtil ]; then
        chmod +x deployUtil/*
        cp -f deployUtil/* ${DEPLOY_BASE_DIR}
    fi
    
    ########################################################################
    # Add execute permission on release script for exec
    ########################################################################
    RELEASE_VER=`echo ${VERSION}|cut -f1 -d'-'`
    RELEASE_SCRIPT=${PKG_BASE_DIR}/deploy-${RELEASE_VER}.sh

    chmod u+x ${RELEASE_SCRIPT} 
    . ${RELEASE_SCRIPT} ${PKG_BASE_DIR} 

) >> ${PKG_BASE_DIR}/release.log 2>&1

exit 0
