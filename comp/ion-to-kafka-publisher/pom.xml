<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>com.scm.fi.fi-ion-kafka-publisher</groupId>
	<artifactId>ion-to-kafka-publisher</artifactId>
	<packaging>jar</packaging>
	<name>ion-to-kafka-publisher</name>
    <url>https://confluence.agile.bns/pages/viewpage.action?pageId=780770282</url>
	<description>MMI Ion Component to migrate data from Ion Chains to Kafka</description>
    <!-- this pom.xml file will create a fat-jar (uber-jar). The Spring Boot Maven Plugin is creating an executable jar file,
    which includes all the dependencies required to run the application. org.apache.maven.plugins:maven-compiler-plugin
    plugin ensures that the code is compiled with the specified Java version. Command to build the fat jar is:
    mvn clean package -->
    <parent>
        <groupId>com.scm.fi</groupId>
        <artifactId>ion-kafka-publisher-dist</artifactId>
        <version>1.0.20</version>
        <relativePath>../../pom.xml</relativePath> <!-- Adjust this path according to your project structure -->
    </parent>

	<dependencies>
        <dependency>
            <groupId>com.scm.fi.fi-ion-kafka-publisher</groupId>
            <artifactId>ion-kafka-common-lib</artifactId>
        </dependency>
        <dependency>
            <!-- https://af.cds.bns/ui/native/libs-release/com/iontrading/jmkv/168p3/ -->
            <!-- https://af.cds.bns:443/artifactory/libs-release/com/iontrading/jmkv/168p3 -->
            <groupId>com.iontrading</groupId>
            <artifactId>jmkv</artifactId>
            <!-- java 17 is tested by provider for version 168p7 -->
            <version>${jmkv.version}</version>
        </dependency>

        <dependency>
            <!-- https://af.cds.bns/ui/native/libs-release/ch/qos/logback/logback-classic/1.5.12/ -->
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <version>${logback-classic.version}</version>
        </dependency>
        <dependency>
            <!-- https://af.cds.bns/artifactory/libs-release/org/apache/logging/log4j/log4j-core/2.19.0/ -->
            <!-- https://af.cds.bns/ui/native/libs-release/org/apache/logging/log4j/log4j-core/2.24.3/ -->
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-core</artifactId>
            <version>${log4j-core.version}</version>
        </dependency>
        <dependency>
                <!-- https://af.cds.bns/ui/native/libs-release/io/projectreactor/reactor-core -->
            <groupId>io.projectreactor</groupId>
            <artifactId>reactor-core</artifactId>
            <version>${reactor-core.version}</version>
        </dependency>
        <dependency>
            <!-- https://af.cds.bns/ui/native/libs-release/org/projectlombok/lombok/1.18.30/ -->
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
            <version>${lombok.version}</version>
        </dependency>
        <!-- https://af.cds.bns/ui/native/libs-release/io/confluent/kafka-avro-serializer/7.5.1/ -->
        <dependency>
            <groupId>io.confluent</groupId>
            <artifactId>kafka-avro-serializer</artifactId>
            <version>${kafka-avro-serializer.version}</version> <!-- latest version that supports Apache Avro 1.11.1 (required for sa-schema-v1) -->
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.avro</groupId>
                    <artifactId>avro</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.kafka</groupId>
                    <artifactId>kafka-clients</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.codehaus.jackson</groupId>
                    <artifactId>kafka-clients</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.codehaus.jackson</groupId>
                    <artifactId>jackson-mapper-asl</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-compress</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-databind</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.zookeeper</groupId>
                    <artifactId>zookeeper</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.scm.common.services</groupId>
            <artifactId>scm-common</artifactId>
            <version>${scm-common.version}</version>
        </dependency>
        <!-- https://af.cds.bns/artifactory/libs-release/com/fasterxml/jackson/core/jackson-databind/2.15.3 -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>${jackson.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-avro</artifactId>
            <version>${jackson.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
            <version>${jackson.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-databind</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-annotations</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <!-- https://af.cds.bns/artifactory/libs-release/org/yaml/snakeyaml/2.2 -->
            <groupId>org.yaml</groupId>
            <artifactId>snakeyaml</artifactId>
            <version>${snakeyaml.version}</version>
        </dependency>
        <dependency>
            <!-- https://af.cds.bns/artifactory/libs-release/com/bns/sa/idgenerator -->
            <groupId>com.bns.sa</groupId>
            <artifactId>idgenerator</artifactId>
            <version>${idgenerator.version}</version>
        </dependency>

        <!-- JUnit Jupiter (for JUnit 5 tests) -->
        <dependency>
            <!-- https://af.cds.bns/ui/native/libs-release/org/junit/jupiter/junit-jupiter-engine/5.7.2/ -->
            <!-- https://af.cds.bns/ui/native/libs-release/org/junit/platform/commons/util -->
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-engine</artifactId>
            <version>${junit.version}</version>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.apiguardian</groupId>
                    <artifactId>apiguardian-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <version>${junit.version}</version>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.apiguardian</groupId>
                    <artifactId>apiguardian-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.opentest4j</groupId>
                    <artifactId>opentest4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <!-- https://af.cds.bns/artifactory/libs-release/org/junit/jupiter/junit-jupiter-params/5.10.0/ -->
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-params</artifactId>
            <version>${junit.version}</version>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.junit.jupiter</groupId>
                    <artifactId>junit-jupiter-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.typesafe</groupId>
            <artifactId>config</artifactId>
            <version>${config.version}</version>
        </dependency>
        <dependency>
            <!-- https://af.cds.bns/ui/native/libs-release/javax/annotation/javax.annotation-api/1.3.2/ -->
            <groupId>javax.annotation</groupId>
            <artifactId>javax.annotation-api</artifactId>
            <version>${javax.annotation-api.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-autoconfigure</artifactId>
        </dependency>
        <!-- Spring Test -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.junit.vintage</groupId>
                    <artifactId>junit-vintage-engine</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.vaadin.external.google</groupId>
                    <artifactId>android-json</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <!-- https://af.cds.bns/ui/native/libs-release/org/springframework/kafka/spring-kafka -->
            <groupId>org.springframework.kafka</groupId>
            <artifactId>spring-kafka</artifactId>
            <version>${spring-kafka.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- https://af.cds.bns/ui/native/libs-release/org/apache/kafka/kafka-streams/3.6.2/ -->
        <dependency>
            <groupId>org.apache.kafka</groupId>
            <artifactId>kafka-streams</artifactId>
            <version>${kafka-streams.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <dependency>
            <!-- https://af.cds.bns/ui/native/libs-release/org/springframework/boot/spring-boot-configuration-processor -->
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <scope>runtime</scope>
            <optional>true</optional>
        </dependency>

        <!-- Oracle JDBC driver -->
        <dependency>
            <!-- https://af.cds.bns/artifactory/libs-release/com/oracle/database/jdbc/ojdbc10/19.20.0.0/ -->
            <groupId>com.oracle.database.jdbc</groupId>
            <artifactId>ojdbc10</artifactId>
            <version>${ojdbc10.version}</version>
        </dependency>
        <dependency>
            <!-- https://af.cds.bns/artifactory/libs-release/com/zaxxer/HikariCP -->
            <groupId>com.zaxxer</groupId>
            <artifactId>HikariCP</artifactId>
                <version>${HikariCP.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <!-- https://af.cds.bns/artifactory/libs-release/org/json/json -->
            <groupId>org.json</groupId>
            <artifactId>json</artifactId>
            <version>${json.version}</version>
        </dependency>
        <!-- Groovy dependency 2.4.9 - compatible with java 8 and 3.0.17 (pom) - compatible with java 17-->
        <!-- https://af.cds.bns/ui/native/libs-release/org/apache/groovy/groovy-all/4.0.23/ -->
        <dependency>
            <groupId>org.apache.groovy</groupId>
            <artifactId>groovy-all</artifactId>
            <version>${groovy.version}</version>
            <type>pom</type>
            <exclusions>
                <exclusion>
                    <groupId>junit</groupId>
                    <artifactId>junit</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <!-- https://af.cds.bns/artifactory/libs-release/com/bns/sa/sa-schema-v1/ -->
            <!-- Included org.apache.avro (1.11.3): avro + avro-tools library's as well -->
            <groupId>com.bns.sa</groupId>
            <artifactId>sa-schema-v1</artifactId>
            <version>${sa-schema-v1.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.avro</groupId>
                    <artifactId>avro</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.avro</groupId>
                    <artifactId>avro-tools</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>junit</groupId>
                    <artifactId>junit</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-databind</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.bns.sa</groupId>
            <artifactId>sa-enums</artifactId>
            <version>${sa-enums.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.elasticsearch</groupId>
                    <artifactId>elasticsearch</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>junit</groupId>
                    <artifactId>junit</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.avro</groupId>
                    <artifactId>avro</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-databind</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.confluent</groupId>
                    <artifactId>kafka-schema-registry-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.avro</groupId>
            <artifactId>avro</artifactId>
            <version>${avro.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-compress</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-jpa</artifactId>
        </dependency>
        <dependency>
            <!-- https://af.cds.bns/ui/native/libs-release/org/springframework/boot/spring-boot-starter-web -->
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <!-- https://af.cds.bns/ui/native/libs-release/org/springframework/spring-web -->
            <!-- https://repo1.maven.org/maven2/org/springframework/spring-web -->
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
            <version>${spring-web.version}</version>
        </dependency>
        <dependency>
            <groupId>com.scm.fi.fi-ion-kafka-publisher</groupId>
            <artifactId>csv-to-ion-kafka-publisher</artifactId>

            <scope>test</scope>
        </dependency>
        <dependency>
            <!-- https://af.cds.bns/ui/native/libs-release/com/h2database/h2 -->
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <version>${h2.version}</version>
            <!--scope>runtime</scope-->
        </dependency>
        <!-- https://af.cds.bns:443/artifactory/libs-release/org/springframework/boot/spring-boot-loader/ -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-loader</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>

	<build>
        <plugins>
            <plugin>
                <!-- https://af.cds.bns/artifactory/libs-release/org/springframework/boot/spring-boot-maven-plugin-->
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <!-- setting forces Spring Boot to use PropertiesLauncher -->
                    <layout>ZIP</layout>
                    <mainClass>com.scm.fi.Ion2KafkaPubApp</mainClass>
                    <excludes>
                        <exclude>
                            <groupId>com.scm.fi.sa.kafka.serializers.plugins.kafka-serializers-plugins</groupId>
                            <artifactId>kafka-serializers-plugins</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
            </plugin>
            <plugin>
                <!-- https://af.cds.bns/ui/native/libs-release/org/apache/maven/plugins/maven-surefire-plugin-->
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>${maven-surefire-plugin.version}</version>
            </plugin>
            <plugin>
                <!-- https://af.cds.bns/artifactory/libs-release/org/apache/maven/plugins/maven-compiler-plugin -->
                <!-- https://repo1.maven.org/maven2/org/apache/maven/plugins/maven-compiler-plugin/3.13.0/ -->
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.version}</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>