[{"comment": ["ionFields - corresponding fields on chain to evaluate target Kafka object (targetClassName)"], "name": "RT_MMI_POSITION", "chain": "CAD.CM_POSITION.POSITION_TOR.POSITION", "enable": true, "statusRecord": "CAD.CM_STATUS.POSITION_TOR.GSTATUS", "table": "KAFKA_PUB", "keys": ["messageId"], "businessIdField": "Id", "targetClassName": "com.scotia.gcm.sa.v1.position.PositionMessage", "groovyMappingScript": "position/position-tor.groovy", "targetTopic": "ist2.enterprise.mmi-tor.position.realtime.v1", "targetControlTopic": "ist2.enterprise.mmi-tor.position.realtime.control.v1", "targetESIndice": "ist2.enterprise.mmi-tor.position.realtime.v1-1", "ionFields": ["Id", "Date", "BookId", "InstrumentId", "NetTradingPos", "NetTradingPosNominal", "PositionAge", "SettledPos", "SettledPosNominal", "SettledPosNominalPrevious", "SettledPos<PERSON>revious", "SODPos", "SODPosNominal", "StatusStr", "TradeCount", "Trader", "UnsettledPos", "UnsettledPosNominal", "ValueTodayBuyAvg", "ValueTodaySellAvg", "Volume", "VolumeTodayBuy", "VolumeTodaySell"]}, {"comment": ["Chain generated by csv-to-ion-kafka-publisher"], "name": "RT_MMI_POSITION2", "chain": "CAD.CM_POSITION.POSITION_TOR_2.POSITION", "enable": true, "statusRecord": "CAD.CM_STATUS.POSITION_TOR2.GSTATUS", "table": "KAFKA_PUB", "keys": ["messageId"], "businessIdField": "Id", "targetClassName": "com.scotia.gcm.sa.v1.position.PositionMessage", "groovyMappingScript": "position/position-tor.groovy", "targetTopic": "ist2.enterprise.mmi-tor.position.realtime.v1", "targetControlTopic": "ist2.enterprise.mmi-tor.position.realtime.control.v1", "targetESIndice": "ist2.enterprise.mmi-tor.position.realtime.v1-1", "ionFields": ["Id", "Date", "BookId", "InstrumentId", "NetTradingPos", "NetTradingPosNominal", "PositionAge", "SettledPos", "SettledPosNominal", "SettledPosNominalPrevious", "SettledPos<PERSON>revious", "SODPos", "SODPosNominal", "StatusStr", "TradeCount", "Trader", "UnsettledPos", "UnsettledPosNominal", "ValueTodayBuyAvg", "ValueTodaySellAvg", "Volume", "VolumeTodayBuy", "VolumeTodaySell"]}, {"comment": [""], "name": "RT_MMI_INSTRUMENT", "chain": "CAD.CM_INSTRUMENT.REFDATA_TOR.INSTRUMENT", "enable": true, "statusRecord": "CAD.CM_STATUS.REFDATA_TOR.GSTATUS", "table": "KAFKA_PUB", "keys": ["messageId"], "businessIdField": "Id", "targetClassName": "com.scm.fi.sa.kafka.serializers.plugins.Row", "groovyMappingScript": "instrument/instrument-tor.groovy", "targetTopic": "ist2.landing.mmi-tor.instrument.realtime", "targetControlTopic": "ist.landing.mmi-tor.instrument.realtime.control-1", "targetESIndice": "ist2.landing.mmi-tor.instrument.realtime-1", "ionFields": ["Id", "InstrumentId", "InstrumentMappingId", "SecurityTypeStr", "InsType", "InsTypeStr", "InsSubType", "InsSubTypeStr", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "CusipBBG", "ADPCode", "Desc", "Code", "Country", "CurrencyStr", "DateIssue", "DateMaturity", "DateInterestAccrual", "DateFirstCoupon", "DateLastCoupon", "CouponInterest", "CouponFreq", "CouponFreqStr", "DayCntConvention", "DayCntConventionStr", "SettlDays", "Exchange", "Issuer", "IssuerId", "IssuerShortName", "RatingStandardPoors", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "DateAnnounce", "DateSettl", "DateFirstDelivery", "DateLastDelivery", "DateLastTrade", "DateStop", "DateOptionExpire", "QtyNominal", "ValueTypeStr", "UnderlyingCode", "UnderlyingId", "UnderlyingDesc", "FutureSettlTypeStr", "IndexLinkedFlag", "IndexTicker", "FloaterFlag", "F<PERSON><PERSON><PERSON><PERSON><PERSON>", "Factor", "QtyIssue", "QtyRemaining", "FuturesCTDDesc", "PriceMultiplier", "IndustryGroup", "IndustrySector", "RIC", "CouponCalcType", "CouponCalcTypeStr", "CallableFlag", "OptionTypeStr", "PriceStrike", "StatusStr", "BondCallOptionStyleStr", "CallNotifyDays", "CallNotifyDaysType", "CallNotifyDaysTypeStr", "AccrualDayCntConvention", "AccrualDayCntConventionStr", "CallFeature", "CashFlowCount", "CouponScheduleDate", "S0_StubPeriodType", "CalculationType", "WI", "MBSPoolId", "STRIPTypeStr", "BondCalcType", "EomAdjustFlag", "CollateralType", "PutableFlag", "Ticker", "FixToFloatFlag", "WIRollFlag", "DateStart", "DateFinalMaturity", "RedemptionValue", "TOR_TSXEligibleFlag", "Series", "IssuerIndustry", "UnderlyingSecurityTypeDesc", "FutureProductCode", "BondDefaultFlag", "CountryOfIssue", "CodeTypeStr", "DateDated", "FactorDate", "FloaterIndex", "FloaterResetDate", "AccountingConvention", "PaymentDateAdjTypeStr", "PriceTypeStr", "RatingDBRS", "ResetFreq", "FutureDeliverableBondsCusips", "FutureDeliverableBondsISINs", "WAC", "WAM", "GLClass", "ZeroCouponFlag", "CalculationTypeDescription", "CompoundingFreqStr", "CompoundingFreq", "IsAmortiser", "FloaterResetFreq", "FutureCalcTypeStr", "PerpetualFlag", "FeedToPXE1", "InsertionSource", "InsertionDate", "PriceTick", "QtyTick", "IsCovered", "IsSecured", "IndexRatio", "NextCallDate", "MBSPoolFlag", "ScotiaId", "Called", "MBSBalOnMty"]}, {"comment": [""], "name": "RT_MMI_AD_IBL", "chain": "CAD.AD_IBL.ADS_TOR.AD_IBL", "enable": true, "statusRecord": "CAD.CM_STATUS.REFDATA_TOR.GSTATUS", "table": "KAFKA_PUB", "keys": ["messageId"], "businessIdField": "Id", "targetClassName": "com.scm.fi.sa.kafka.serializers.plugins.Row", "groovyMappingScript": "instrument/instrument-analytic-tor.groovy", "targetTopic": "ist2.landing.mmi-tor.instrument.realtime", "targetControlTopic": "ist.landing.mmi-tor.instrument.realtime.control-1", "targetESIndice": "ist2.landing.mmi-tor.instrument.realtime-1", "ionFields": ["SettleAccrualDayCntConvention", "MortgageLiquidationParameter", "PartialPrepaymentRate", "DayCntConvention", "MortgageLiquidationModel", "AccrualDayCntConventionStr", "SettleAccrualDayCntConventionStr", "MoneyMarketDayCntConventionStr", "PenaltyInterestPaymentRatio", "BalancePriorMaturity1M", "DayCntConventionStr", "MortgageLiquidationModelStr", "BalancePriorMaturity2M", "BalancePriorMaturity3M", "BalancePriorMaturity4M", "BalancePriorMaturity5M", "Id", "InterestAdjustmentDate", "AccrualDayCntConvention", "MoneyMarketDayCntConvention"]}, {"comment": [""], "name": "RT_MMI_ORDER", "chain": "CAD.CM_ORDER.MX_OM_TOR.ORDER", "enable": false, "keys": ["messageId"], "statusRecord": "CAD.CM_STATUS.REFDATA_TOR.GSTATUS", "table": "KAFKA_PUB", "businessIdField": "Id", "targetClassName": "com.scotia.gcm.sa.v1.pretrade.OrderMessage", "groovyMappingScript": "order/order-tor.groovy", "targetTopic": "ist2.enterprise.mmi-tor.order.realtime.v1", "targetControlTopic": "ist2.enterprise.mmi-tor.order.realtime.control.v1", "targetESIndice": "ist2.enterprise.mmi-tor.order.realtime.v1-1", "ionFields": ["Id", "OrderNo", "Trader", "Date", "Time", "TrdDateLast", "TrdTimeLast", "ExpireDate", "ExpireTime", "StatusStr", "TradingStatusStr", "InstrumentId", "CurrencyStr", "TypeStr", "TimeInForceStr", "VerbStr", "Value", "ValueTypeStr", "StopPrice", "TrdValueLast", "TrdQtyLast", "QtyFill", "QtyGoal", "QtyStatusStr"]}, {"comment": [""], "name": "RT_MMI_TRADE", "chain": "CAD.CM_TRADE.TRADESERVER_TOR_2.TRADECAPTURE", "enable": false, "statusRecord": "CAD.CM_STATUS.TRADESERVER_TOR.GSTATUS", "table": "KAFKA_PUB", "keys": ["messageId"], "businessIdField": "Id", "targetClassName": "com.scotia.gcm.sa.v1.trade.TradeMessage", "groovyMappingScript": "trade/trade-tor.groovy", "targetTopic": "ist2.enterprise.mmi-tor.trade.realtime.v1", "targetControlTopic": "ist2.enterprise.mmi-tor.trade.realtime.control.v1", "targetESIndice": "ist2.enterprise.mmi-tor.trade.realtime.v1-1", "ionFields": ["Id", "RevId", "TradeId", "OrderId", "QuoteId", "TVTransactionIdCode", "DealId", "InstrumentId", "ExternalId1", "ExternalIdSrc1", "Desc", "SecurityTypeStr", "InsType", "FutureIsin", "Code", "OriginalSource", "MIC", "Trader", "CreateUserId", "OriginalCreateUserId", "SalesRepId", "Sales<PERSON>erson", "DateSettl", "SettlTradeNetMoney", "CurrencyStr", "WaiverFlag", "ExecutionMktId", "InvestmentMktId", "MiFID2Exemption", "DateMktCreationUTC", "TimeMktCreationUTC", "TypeStr", "ScotiaInternal", "DateTrade", "DateOriginalCreateStamp", "TimeOriginalCreate", "DateModified", "TimeModified", "DateCreateStamp", "TimeCreate", "Status", "TradeSubStatusStr", "BookId", "LegalEntity", "CP<PERSON>ey", "CPName", "CounterpartyLEI", "CPShortName", "OriginalSource", "ClearingHouse", "CommissionTotal", "CommissionAmount", "DateStatusChange", "TimeStatusChange", "SolicitedFlag", "SynthRef", "TradeStatusStr", "TradeSubStatusStr", "RetailADPCode", "RetailFlag", "SalesCheckedUserId", "ADPBlotterCode", "ADPSTPStatus", "ADPTrailerCode", "IMPACTStatusMessage", "DateStatusChangeStamp", "LegNo", "CancelledFlag", "FreeText", "ExecutionMktIdType", "SettlDays", "DealType", "RefAssetName", "RefAssetAcctMethod", "InsType", "InsTypeStr", "InsSubType", "TradeSplitFlagStr", "TradeSplitId", "TradeSplitPct", "TradeSplitStatusStr", "<PERSON><PERSON><PERSON>", "TotalMoney", "FXRate", "FXRateInverted", "IsCrossCurrencySettl", "Price", "DateMaturity", "VerbStr", "S0_CurrencyStr", "QtyNominal", "Qty", "QtyNominal", "Code", "Desc", "CPAccountId", "AggressedStr", "ExternalStr", "UnderlyingId", "UnderlyingCode", "OptionStyle", "DateOptionExpire", "OptionTypeStr", "PriceStrike", "Code", "StrikeTypeStr", "UnderlyingId", "UnderlyingCode", "OptionStyle", "VerbStr", "DateOptionExpire", "OptionTypeStr", "PriceStrike", "PriceDirty", "Yield", "AccruedAmount", "AccruedGivenStr", "PriceClean", "CouponInterest", "Discount", "IndexFactor", "WIStr", "AccruedValue", "Factor"]}]