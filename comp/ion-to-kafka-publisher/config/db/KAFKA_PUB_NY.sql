Prompt << Creating table: KAFKA_PUB for NY >>
DROP TABLE KAFKA_PUB CASCADE CONSTRAINTS;
-- JSON_HEADER & JSON_TAGS:
-- using BLOBs for storing JSON data is the recommended approach, as it ensures that the data is stored and retrieved
-- as raw binary data, without any character set conversions or potential data corruption.
CREATE TABLE KAFKA_PUB
(
    AUTO_ID NUMBER GENERATED ALWAYS AS IDENTITY (START WITH 1) NOT NULL, -- PK To track creation order
    MESSAGEID                     VARCHAR2(64) NOT NULL, -- Corresponding to header.messageId (Another PK)
    CHAIN                         VARCHAR2(80) NOT NULL, -- Source ION Chain Name
    TOPIC                         VARCHAR2(80) NOT NULL, -- Target Kafka Topic Name
    TARGETCLASS                   VARCHAR2(80) NOT NULL, -- Target Class Name
    TS                            TIMESTAMP NOT NULL,    -- Corresponding to header.sourceSystemCreationTimestamp
    BUSINESSID                    VARCHAR2(64) NOT NULL, -- Corresponding to header.businessId
    CHAINFIELDS    BLOB CONSTRAINT ensure_json_chainfields_is_json  CHECK (CHAINFIELDS IS JSON (STRICT)), -- Corresponding to nativeData.chainFields.* (Map<String, String> chainFields)
    TARGETOBJECT   BLOB CONSTRAINT ensure_json_targetobject_is_json  CHECK (TARGETOBJECT IS JSON (STRICT)), -- Corresponding to nativeData.tags.* (Map<String, String> tags)
    scm_last_upd_user_id          VARCHAR2(30) default user not null,           -- User name that added this record
    IONCOMPONENT                  VARCHAR2(30) default 'KAKA_PUB_NY' not null, -- Ion Component name that added this record
    is_published                  NUMBER(1) DEFAULT 0                           -- 1 - if record was published successfully to Kafka
)   tablespace fieny_data;

alter   table KAFKA_PUB add
        constraint PK_KAFKA_PUB
        primary key (AUTO_ID)
        using index
        tablespace fieny_index;

-- Create indexes
CREATE INDEX IDX_KAFKA_PUB_MESSAGE_ID ON KAFKA_PUB(MESSAGEID) tablespace fieny_index;
CREATE INDEX IDX_KAFKA_PUB_TS ON KAFKA_PUB(TS) tablespace fieny_index;
CREATE INDEX IDX_KAFKA_PUB_CHAIN ON KAFKA_PUB(CHAIN) tablespace fieny_index;
CREATE INDEX IDX_KAFKA_PUB_TOPIC ON KAFKA_PUB(TOPIC) tablespace fieny_index;
CREATE INDEX IDX_KAFKA_PUB_BUSINESS_ID ON KAFKA_PUB(BUSINESSID) tablespace fieny_index;
CREATE INDEX IDX_KAFKA_PUB_CHAIN_TOPIC_MESSAGE_ID ON KAFKA_PUB (CHAIN, TOPIC, MESSAGEID) tablespace fieny_index;
CREATE INDEX IDX_KAFKA_PUB_TOPIC_MESSAGE_ID ON KAFKA_PUB (TOPIC, MESSAGEID) tablespace fieny_index;
CREATE INDEX IDX_KAFKA_PUB_ION_COMPONENT ON KAFKA_PUB (IONCOMPONENT) tablespace fieny_index;
CREATE INDEX IDX_KAFKA_PUB_TS_ION_COMPONENT_ID ON KAFKA_PUB (TS, IONCOMPONENT, AUTO_ID) TABLESPACE fieny_index;
CREATE INDEX IDX_KAFKA_PUB_REPUBLISH ON KAFKA_PUB(IS_PUBLISHED, TOPIC, AUTO_ID) TABLESPACE fieny_index;

-- Create public synonym if it does not exist
BEGIN
    EXECUTE IMMEDIATE 'CREATE OR REPLACE PUBLIC SYNONYM KAFKA_PUB FOR fienymso.KAFKA_PUB';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -955 THEN
            RAISE;
        END IF;
END;
/

-- Add table comments
COMMENT ON TABLE KAFKA_PUB IS 'Stores messages for Kafka republishing with JSON sa-schema object';
COMMENT ON COLUMN KAFKA_PUB.AUTO_ID IS 'Auto-incrementing primary key';
COMMENT ON COLUMN KAFKA_PUB.MESSAGEID IS 'Unique message identifier';
COMMENT ON COLUMN KAFKA_PUB.CHAIN IS 'Source ION chain name';
COMMENT ON COLUMN KAFKA_PUB.TOPIC IS 'Target Kafka topic';
COMMENT ON COLUMN KAFKA_PUB.TARGETCLASS IS 'Java class name for sa-schema object';
COMMENT ON COLUMN KAFKA_PUB.TS IS 'Message timestamp';
COMMENT ON COLUMN KAFKA_PUB.BUSINESSID IS 'Business identifier';
COMMENT ON COLUMN KAFKA_PUB.CHAINFIELDS IS 'fields from source chain';
COMMENT ON COLUMN KAFKA_PUB.TARGETOBJECT IS 'JSON sa-schema object';
COMMENT ON COLUMN KAFKA_PUB.IONCOMPONENT IS 'Ion Component name that added this record';
COMMENT ON COLUMN KAFKA_PUB.IS_PUBLISHED IS 'Publication status: 0=Not published, 1=Published';

-- Grant permissions
grant select, insert, update, delete on fienymso.KAFKA_PUB to rl_fieny_updt;
grant   select on fienymso.KAFKA_PUB to rl_fieny_read;

-- select AUTO_ID, messageId, CHAIN, TS, BUSINESSID, TO_CLOB (json_header), to_clob(JSON_TAGS) from KAFKA_PUB where chain='CAD.CM_POSITION.POSITION_TOR.POSITION' order by auto_id desc
