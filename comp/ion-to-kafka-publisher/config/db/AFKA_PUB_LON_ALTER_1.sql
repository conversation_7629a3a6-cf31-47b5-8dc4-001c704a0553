-- <PERSON>AF<PERSON>_PUB_LON_ALTER_1.sql
--  How long Oracle will wait to acquire locks, not how long it will hold them
ALTER SESSION SET DDL_LOCK_TIMEOUT = 600;  -- up to 10 minutes

-- Drop existing index
DROP INDEX IDX_KAFKA_PUB_TS_IONCOMPONENT;

-- Create new covering index (can run while application is active)
CREATE INDEX IDX_KAFKA_PUB_TS_ION_COMPONENT_ID ON KAFKA_PUB(TS, IONCOMPONENT, AUTO_ID)  TABLESPACE fieuk_index ONLINE;

-- Update table statistics after index creation
-- ANALYZE TABLE KAFKA_PUB COMPUTE STATISTICS;