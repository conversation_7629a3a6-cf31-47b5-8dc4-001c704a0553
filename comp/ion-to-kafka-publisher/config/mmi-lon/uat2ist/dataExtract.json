[{"comment": ["1. Retired? mkv.scotia.guile.plugins.kafka-trade-producer2,  ionFields - corresponding fields on chain to evaluate target Kafka object (targetClassName)"], "name": "RT_MMI_ORDER", "chain": "EUR.CM_ORDER.ICE_FUTURES.ORDER", "enable": true, "keys": ["messageId"], "statusRecord": "EUR.CM_ORDER.ICE_FUTURES.ORDER.GSTATUS", "table": "KAFKA_PUB", "businessIdField": "Id", "targetClassName": "com.scotia.gcm.sa.v1.pretrade.OrderMessage", "groovyMappingScript": "order/order-tor.groovy", "targetTopic": "ist2.source.mmi-lon.order.realtime.v1", "targetControlTopic": "ist.landing.mmi-lon.price.eod.control", "targetESIndice": "ist2.source.mmi-lon.order.realtime.v1-1-r1", "ionFields": ["Id", "OrderNo", "Trader", "Date", "Time", "TrdDateLast", "TrdTimeLast", "ExpireDate", "ExpireTime", "StatusStr", "TradingStatusStr", "InstrumentId", "CurrencyStr", "TypeStr", "TimeInForceStr", "VerbStr", "Value", "ValueTypeStr", "StopPrice", "TrdValueLast", "TrdQtyLast", "QtyFill", "QtyGoal", "QtyStatusStr"]}, {"comment": ["2.Retired? mkv.scotia.guile.plugins.kafka-trade-producer2i"], "name": "RT_MMI_ORDER", "chain": "EUR.CM_ORDER.ICE_FUTURES_IRL.ORDER", "enable": true, "keys": ["messageId"], "statusRecord": "EUR.CM_ORDER.ICE_FUTURES_IRL.ORDER.GSTATUS", "table": "KAFKA_PUB", "businessIdField": "Id", "targetClassName": "com.scotia.gcm.sa.v1.pretrade.OrderMessage", "groovyMappingScript": "order/order-tor.groovy", "targetTopic": "ist2.source.mmi-lon.order.realtime.v1", "targetControlTopic": "ist.landing.mmi-lon.price.eod.control", "targetESIndice": "ist2.source.mmi-lon.order.realtime.v1-1-r1", "ionFields": ["Id", "OrderNo", "Trader", "Date", "Time", "TrdDateLast", "TrdTimeLast", "ExpireDate", "ExpireTime", "StatusStr", "TradingStatusStr", "InstrumentId", "CurrencyStr", "TypeStr", "TimeInForceStr", "VerbStr", "Value", "ValueTypeStr", "StopPrice", "TrdValueLast", "TrdQtyLast", "QtyFill", "QtyGoal", "QtyStatusStr"]}, {"comment": ["3. Retired? mkv.scotia.guile.plugins.kafka-data-source3"], "name": "RT_MMI_ORDER", "chain": "EUR.CM_ORDER.EUREX_ETI.ORDER", "enable": true, "keys": ["messageId"], "statusRecord": "EUR.CM_ORDER.EUREX_ETI.ORDER.GSTATUS", "table": "KAFKA_PUB", "businessIdField": "Id", "targetClassName": "com.scotia.gcm.sa.v1.pretrade.OrderMessage", "groovyMappingScript": "order/order-tor.groovy", "targetTopic": "ist2.source.mmi-lon.order.realtime.v1", "targetControlTopic": "ist.landing.mmi-lon.price.eod.control", "targetESIndice": "ist2.source.mmi-lon.order.realtime.v1-1-r1", "ionFields": ["Id", "OrderNo", "Trader", "Date", "Time", "TrdDateLast", "TrdTimeLast", "ExpireDate", "ExpireTime", "StatusStr", "TradingStatusStr", "InstrumentId", "CurrencyStr", "TypeStr", "TimeInForceStr", "VerbStr", "Value", "ValueTypeStr", "StopPrice", "TrdValueLast", "TrdQtyLast", "QtyFill", "QtyGoal", "QtyStatusStr"]}, {"comment": ["3. Retired? mkv.scotia.guile.plugins.kafka-data-source3i"], "name": "RT_MMI_ORDER", "chain": "EUR.CM_ORDER.EUREX_ETI.ORDER", "enable": true, "keys": ["messageId"], "statusRecord": "EUR.CM_ORDER.EUREX_ETI.ORDER.GSTATUS", "table": "KAFKA_PUB", "businessIdField": "Id", "targetClassName": "com.scotia.gcm.sa.v1.pretrade.OrderMessage", "groovyMappingScript": "order/order-tor.groovy", "targetTopic": "ist2.source.mmi-lon.order.realtime.v1", "targetControlTopic": "ist.landing.mmi-lon.price.eod.control", "targetESIndice": "ist2.source.mmi-lon.order.realtime.v1-1-r1", "ionFields": ["Id", "OrderNo", "Trader", "Date", "Time", "TrdDateLast", "TrdTimeLast", "ExpireDate", "ExpireTime", "StatusStr", "TradingStatusStr", "InstrumentId", "CurrencyStr", "TypeStr", "TimeInForceStr", "VerbStr", "Value", "ValueTypeStr", "StopPrice", "TrdValueLast", "TrdQtyLast", "QtyFill", "QtyGoal", "QtyStatusStr"]}, {"comment": ["4. Retired in Apr-2023 (0 records in prod)? mkv.scotia.guile.plugins.kafka-data-source3u"], "name": "RT_MMI_ORDER", "chain": "EUR.CM_ORDER.EUREX_ETI_UK.ORDER", "enable": true, "keys": ["messageId"], "statusRecord": "EUR.CM_ORDER.EUREX_ETI_UK.ORDER.GSTATUS", "table": "KAFKA_PUB", "businessIdField": "Id", "targetClassName": "com.scotia.gcm.sa.v1.pretrade.OrderMessage", "groovyMappingScript": "order/order-tor.groovy", "targetTopic": "ist2.source.mmi-lon.order.realtime.v1", "targetControlTopic": "ist.landing.mmi-lon.price.eod.control", "targetESIndice": "ist2.source.mmi-lon.order.realtime.v1-1-r1", "ionFields": ["Id", "OrderNo", "Trader", "Date", "Time", "TrdDateLast", "TrdTimeLast", "ExpireDate", "ExpireTime", "StatusStr", "TradingStatusStr", "InstrumentId", "CurrencyStr", "TypeStr", "TimeInForceStr", "VerbStr", "Value", "ValueTypeStr", "StopPrice", "TrdValueLast", "TrdQtyLast", "QtyFill", "QtyGoal", "QtyStatusStr"]}, {"comment": ["5. Retired in Apr-2023 (last record: Dec-2022)? mkv.scotia.guile.plugins.kafka-data-source4"], "name": "RT_MMI_ORDER", "chain": "EUR.CM_ORDER.EBM.ORDER", "enable": true, "keys": ["messageId"], "statusRecord": "EUR.CM_ORDER.EBM.ORDER.GSTATUS", "table": "KAFKA_PUB", "businessIdField": "Id", "targetClassName": "com.scotia.gcm.sa.v1.pretrade.OrderMessage", "groovyMappingScript": "order/order-tor.groovy", "targetTopic": "ist2.source.mmi-lon.order.realtime.v1", "targetControlTopic": "ist.landing.mmi-lon.price.eod.control", "targetESIndice": "ist2.source.mmi-lon.order.realtime.v1-1-r1", "ionFields": ["Id", "OrderNo", "Trader", "Date", "Time", "TrdDateLast", "TrdTimeLast", "ExpireDate", "ExpireTime", "StatusStr", "TradingStatusStr", "InstrumentId", "CurrencyStr", "TypeStr", "TimeInForceStr", "VerbStr", "Value", "ValueTypeStr", "StopPrice", "TrdValueLast", "TrdQtyLast", "QtyFill", "QtyGoal", "QtyStatusStr"]}, {"comment": ["6. mkv.scotia.guile.plugins.kafka-data-source4i"], "name": "RT_MMI_ORDER", "chain": "EUR.CM_ORDER.EBM_IRL.ORDER", "enable": true, "keys": ["messageId"], "statusRecord": "EUR.CM_ORDER.EBM_IRL.ORDER.GSTATUS", "table": "KAFKA_PUB", "businessIdField": "Id", "targetClassName": "com.scotia.gcm.sa.v1.pretrade.OrderMessage", "groovyMappingScript": "order/order-tor.groovy", "targetTopic": "ist2.source.mmi-lon.order.realtime.v1", "targetControlTopic": "ist.landing.mmi-lon.price.eod.control", "targetESIndice": "ist2.source.mmi-lon.order.realtime.v1-1-r1", "ionFields": ["Id", "OrderNo", "Trader", "Date", "Time", "TrdDateLast", "TrdTimeLast", "ExpireDate", "ExpireTime", "StatusStr", "TradingStatusStr", "InstrumentId", "CurrencyStr", "TypeStr", "TimeInForceStr", "VerbStr", "Value", "ValueTypeStr", "StopPrice", "TrdValueLast", "TrdQtyLast", "QtyFill", "QtyGoal", "QtyStatusStr"]}, {"comment": ["7. Retired in Apr-2023 (0 records in prod)? mkv.scotia.guile.plugins.kafka-data-source4u"], "name": "RT_MMI_ORDER", "chain": "EUR.CM_ORDER.EBM_UK.ORDER", "enable": true, "keys": ["messageId"], "statusRecord": "EUR.CM_ORDER.EBM_UK.ORDER.GSTATUS", "table": "KAFKA_PUB", "businessIdField": "Id", "targetClassName": "com.scotia.gcm.sa.v1.pretrade.OrderMessage", "groovyMappingScript": "order/order-tor.groovy", "targetTopic": "ist2.source.mmi-lon.order.realtime.v1", "targetControlTopic": "ist.landing.mmi-lon.price.eod.control", "targetESIndice": "ist2.source.mmi-lon.order.realtime.v1-1-r1", "ionFields": ["Id", "OrderNo", "Trader", "Date", "Time", "TrdDateLast", "TrdTimeLast", "ExpireDate", "ExpireTime", "StatusStr", "TradingStatusStr", "InstrumentId", "CurrencyStr", "TypeStr", "TimeInForceStr", "VerbStr", "Value", "ValueTypeStr", "StopPrice", "TrdValueLast", "TrdQtyLast", "QtyFill", "QtyGoal", "QtyStatusStr"]}, {"comment": ["8. Retired in Apr-2023 (last record: Dec-2022)? mkv.scotia.guile.plugins.kafka-data-source5"], "name": "RT_MMI_ORDER", "chain": "EUR.CM_ORDER.BTEC.ORDER", "enable": true, "keys": ["messageId"], "statusRecord": "EUR.CM_ORDER.BTEC.ORDER.GSTATUS", "table": "KAFKA_PUB", "businessIdField": "Id", "targetClassName": "com.scotia.gcm.sa.v1.pretrade.OrderMessage", "groovyMappingScript": "order/order-tor.groovy", "targetTopic": "ist2.source.mmi-lon.order.realtime.v1", "targetControlTopic": "ist.landing.mmi-lon.price.eod.control", "targetESIndice": "ist2.source.mmi-lon.order.realtime.v1-1-r1", "ionFields": ["Id", "OrderNo", "Trader", "Date", "Time", "TrdDateLast", "TrdTimeLast", "ExpireDate", "ExpireTime", "StatusStr", "TradingStatusStr", "InstrumentId", "CurrencyStr", "TypeStr", "TimeInForceStr", "VerbStr", "Value", "ValueTypeStr", "StopPrice", "TrdValueLast", "TrdQtyLast", "QtyFill", "QtyGoal", "QtyStatusStr"]}, {"comment": ["9. Retired in Apr-2023 (last record: Dec-2022)? mkv.scotia.guile.plugins.kafka-data-source5i"], "name": "RT_MMI_ORDER", "chain": "EUR.CM_ORDER.BTEC_IRL.ORDER", "enable": true, "keys": ["messageId"], "statusRecord": "EUR.CM_ORDER.BTEC_IRL.ORDER.GSTATUS", "table": "KAFKA_PUB", "businessIdField": "Id", "targetClassName": "com.scotia.gcm.sa.v1.pretrade.OrderMessage", "groovyMappingScript": "order/order-tor.groovy", "targetTopic": "ist2.source.mmi-lon.order.realtime.v1", "targetControlTopic": "ist.landing.mmi-lon.price.eod.control", "targetESIndice": "ist2.source.mmi-lon.order.realtime.v1-1-r1", "ionFields": ["Id", "OrderNo", "Trader", "Date", "Time", "TrdDateLast", "TrdTimeLast", "ExpireDate", "ExpireTime", "StatusStr", "TradingStatusStr", "InstrumentId", "CurrencyStr", "TypeStr", "TimeInForceStr", "VerbStr", "Value", "ValueTypeStr", "StopPrice", "TrdValueLast", "TrdQtyLast", "QtyFill", "QtyGoal", "QtyStatusStr"]}, {"comment": ["10. (last record: Nov-2024): mkv.scotia.guile.plugins.kafka-data-source5u"], "name": "RT_MMI_ORDER", "chain": "EUR.CM_ORDER.BTEC_UK.ORDER", "enable": true, "keys": ["messageId"], "statusRecord": "EUR.CM_ORDER.BTEC_UK.ORDER.GSTATUS", "table": "KAFKA_PUB", "businessIdField": "Id", "targetClassName": "com.scotia.gcm.sa.v1.pretrade.OrderMessage", "groovyMappingScript": "order/order-tor.groovy", "targetTopic": "ist2.source.mmi-lon.order.realtime.v1", "targetControlTopic": "ist.landing.mmi-lon.price.eod.control", "targetESIndice": "ist2.source.mmi-lon.order.realtime.v1-1-r1", "ionFields": ["Id", "OrderNo", "Trader", "Date", "Time", "TrdDateLast", "TrdTimeLast", "ExpireDate", "ExpireTime", "StatusStr", "TradingStatusStr", "InstrumentId", "CurrencyStr", "TypeStr", "TimeInForceStr", "VerbStr", "Value", "ValueTypeStr", "StopPrice", "TrdValueLast", "TrdQtyLast", "QtyFill", "QtyGoal", "QtyStatusStr"]}, {"comment": ["11. Retired? (0 records in prod): mkv.scotia.guile.plugins.kafka-data-source5us"], "name": "RT_MMI_ORDER", "chain": "USD.CM_ORDER.BTEC_US.ORDER", "enable": true, "keys": ["messageId"], "statusRecord": "USD.CM_ORDER.BTEC_US.ORDER.GSTATUS", "table": "KAFKA_PUB", "businessIdField": "Id", "targetClassName": "com.scotia.gcm.sa.v1.pretrade.OrderMessage", "groovyMappingScript": "order/order-tor.groovy", "targetTopic": "ist2.source.mmi-lon.order.realtime.v1", "targetControlTopic": "ist.landing.mmi-lon.price.eod.control", "targetESIndice": "ist2.source.mmi-lon.order.realtime.v1-1-r1", "ionFields": ["Id", "OrderNo", "Trader", "Date", "Time", "TrdDateLast", "TrdTimeLast", "ExpireDate", "ExpireTime", "StatusStr", "TradingStatusStr", "InstrumentId", "CurrencyStr", "TypeStr", "TimeInForceStr", "VerbStr", "Value", "ValueTypeStr", "StopPrice", "TrdValueLast", "TrdQtyLast", "QtyFill", "QtyGoal", "QtyStatusStr"]}, {"comment": ["12. mkv.scotia.guile.plugins.kafka-data-source5usuk"], "name": "RT_MMI_ORDER", "chain": "USD.CM_ORDER.BTEC_US_UK.ORDER", "enable": true, "keys": ["messageId"], "statusRecord": "USD.CM_ORDER.BTEC_US_UK.ORDER.GSTATUS", "table": "KAFKA_PUB", "businessIdField": "Id", "targetClassName": "com.scotia.gcm.sa.v1.pretrade.OrderMessage", "groovyMappingScript": "order/order-tor.groovy", "targetTopic": "ist2.source.mmi-lon.order.realtime.v1", "targetControlTopic": "ist.landing.mmi-lon.price.eod.control", "targetESIndice": "ist2.source.mmi-lon.order.realtime.v1-1-r1", "ionFields": ["Id", "OrderNo", "Trader", "Date", "Time", "TrdDateLast", "TrdTimeLast", "ExpireDate", "ExpireTime", "StatusStr", "TradingStatusStr", "InstrumentId", "CurrencyStr", "TypeStr", "TimeInForceStr", "VerbStr", "Value", "ValueTypeStr", "StopPrice", "TrdValueLast", "TrdQtyLast", "QtyFill", "QtyGoal", "QtyStatusStr"]}, {"comment": ["13. mkv.scotia.guile.plugins.kafka-data-source6"], "name": "RT_MMI_ORDER", "chain": "USD.CM_ORDER.DWAS_OM_UK.ORDER", "enable": true, "keys": ["messageId"], "statusRecord": "USD.CM_ORDER.DWAS_OM_UK.ORDER.GSTATUS", "table": "KAFKA_PUB", "businessIdField": "Id", "targetClassName": "com.scotia.gcm.sa.v1.pretrade.OrderMessage", "groovyMappingScript": "order/order-tor.groovy", "targetTopic": "ist2.source.mmi-lon.order.realtime.v1", "targetControlTopic": "ist.landing.mmi-lon.price.eod.control", "targetESIndice": "ist2.source.mmi-lon.order.realtime.v1-1-r1", "ionFields": ["Id", "OrderNo", "Trader", "Date", "Time", "TrdDateLast", "TrdTimeLast", "ExpireDate", "ExpireTime", "StatusStr", "TradingStatusStr", "InstrumentId", "CurrencyStr", "TypeStr", "TimeInForceStr", "VerbStr", "Value", "ValueTypeStr", "StopPrice", "TrdValueLast", "TrdQtyLast", "QtyFill", "QtyGoal", "QtyStatusStr"]}, {"comment": ["14. Retired? (0 records in prod): mkv.scotia.guile.plugins.kafka-data-source6e"], "name": "RT_MMI_ORDER", "chain": "USD.CM_ORDER.DWAS_OM_EU.ORDER", "enable": true, "keys": ["messageId"], "statusRecord": "USD.CM_ORDER.DWAS_OM_EU.ORDER.GSTATUS", "table": "KAFKA_PUB", "businessIdField": "Id", "targetClassName": "com.scotia.gcm.sa.v1.pretrade.OrderMessage", "groovyMappingScript": "order/order-tor.groovy", "targetTopic": "ist2.source.mmi-lon.order.realtime.v1", "targetControlTopic": "ist.landing.mmi-lon.price.eod.control", "targetESIndice": "ist2.source.mmi-lon.order.realtime.v1-1-r1", "ionFields": ["Id", "OrderNo", "Trader", "Date", "Time", "TrdDateLast", "TrdTimeLast", "ExpireDate", "ExpireTime", "StatusStr", "TradingStatusStr", "InstrumentId", "CurrencyStr", "TypeStr", "TimeInForceStr", "VerbStr", "Value", "ValueTypeStr", "StopPrice", "TrdValueLast", "TrdQtyLast", "QtyFill", "QtyGoal", "QtyStatusStr"]}, {"comment": ["15. Retired? (last records in production: Apr-2023): mkv.scotia.guile.plugins.kafka-data-source7"], "name": "RT_MMI_ORDER", "chain": "EUR.CM_ORDER.MMF.ORDER", "enable": true, "keys": ["messageId"], "statusRecord": "EUR.CM_ORDER.MMF.ORDER.GSTATUS", "table": "KAFKA_PUB", "businessIdField": "Id", "targetClassName": "com.scotia.gcm.sa.v1.pretrade.OrderMessage", "groovyMappingScript": "order/order-tor.groovy", "targetTopic": "ist2.source.mmi-lon.order.realtime.v1", "targetControlTopic": "ist.landing.mmi-lon.price.eod.control", "targetESIndice": "ist2.source.mmi-lon.order.realtime.v1-1-r1", "ionFields": ["Id", "OrderNo", "Trader", "Date", "Time", "TrdDateLast", "TrdTimeLast", "ExpireDate", "ExpireTime", "StatusStr", "TradingStatusStr", "InstrumentId", "CurrencyStr", "TypeStr", "TimeInForceStr", "VerbStr", "Value", "ValueTypeStr", "StopPrice", "TrdValueLast", "TrdQtyLast", "QtyFill", "QtyGoal", "QtyStatusStr"]}, {"comment": ["16. mkv.scotia.guile.plugins.kafka-data-source8"], "name": "RT_MMI_ORDER", "chain": "EUR.CM_ORDER.TT_FIX_LON.ORDER", "enable": true, "keys": ["messageId"], "statusRecord": "EUR.CM_ORDER.TT_FIX_LON.ORDER.GSTATUS", "table": "KAFKA_PUB", "businessIdField": "Id", "targetClassName": "com.scotia.gcm.sa.v1.pretrade.OrderMessage", "groovyMappingScript": "order/order-tor.groovy", "targetTopic": "ist2.source.mmi-lon.order.realtime.v1", "targetControlTopic": "ist.landing.mmi-lon.price.eod.control", "targetESIndice": "ist2.source.mmi-lon.order.realtime.v1-1-r1", "ionFields": ["Id", "OrderNo", "Trader", "Date", "Time", "TrdDateLast", "TrdTimeLast", "ExpireDate", "ExpireTime", "StatusStr", "TradingStatusStr", "InstrumentId", "CurrencyStr", "TypeStr", "TimeInForceStr", "VerbStr", "Value", "ValueTypeStr", "StopPrice", "TrdValueLast", "TrdQtyLast", "QtyFill", "QtyGoal", "QtyStatusStr"]}, {"comment": ["17. mkv.scotia.guile.plugins.kafka-data-source11"], "name": "RT_MMI_ORDER", "chain": "EUR.CM_R4Q.BLOOMBERG_BTS.RFQ", "enable": true, "keys": ["messageId"], "statusRecord": "EUR.CM_R4Q.BLOOMBERG_BTS.RFQ.GSTATUS", "table": "KAFKA_PUB", "businessIdField": "Id", "targetClassName": "com.scotia.gcm.sa.v1.pretrade.RfqMessage", "groovyMappingScript": "order/order-tor.groovy", "targetTopic": "ist2.source.mmi-lon.rfq.realtime.v1", "targetControlTopic": "ist.landing.mmi-lon.price.eod.control", "targetESIndice": "ist2.source.mmi-lon.rfq.realtime.v1-1-r1", "ionFields": ["Id", "OrderNo", "Trader", "Date", "Time", "TrdDateLast", "TrdTimeLast", "ExpireDate", "ExpireTime", "StatusStr", "TradingStatusStr", "InstrumentId", "CurrencyStr", "TypeStr", "TimeInForceStr", "VerbStr", "Value", "ValueTypeStr", "StopPrice", "TrdValueLast", "TrdQtyLast", "QtyFill", "QtyGoal", "QtyStatusStr"]}, {"comment": ["18. mkv.scotia.guile.plugins.kafka-data-source11i"], "name": "RT_MMI_ORDER", "chain": "EUR.CM_R4Q.BLOOMBERG_BTS_IRL.RFQ", "enable": true, "keys": ["messageId"], "statusRecord": "EUR.CM_R4Q.BLOOMBERG_BTS_IRL.RFQ.GSTATUS", "table": "KAFKA_PUB", "businessIdField": "Id", "targetClassName": "com.scotia.gcm.sa.v1.pretrade.RfqMessage", "groovyMappingScript": "order/order-tor.groovy", "targetTopic": "ist2.source.mmi-lon.rfq.realtime.v1", "targetControlTopic": "ist.landing.mmi-lon.price.eod.control", "targetESIndice": "ist2.source.mmi-lon.rfq.realtime.v1-1-r1", "ionFields": ["Id", "OrderNo", "Trader", "Date", "Time", "TrdDateLast", "TrdTimeLast", "ExpireDate", "ExpireTime", "StatusStr", "TradingStatusStr", "InstrumentId", "CurrencyStr", "TypeStr", "TimeInForceStr", "VerbStr", "Value", "ValueTypeStr", "StopPrice", "TrdValueLast", "TrdQtyLast", "QtyFill", "QtyGoal", "QtyStatusStr"]}, {"comment": ["19. mkv.scotia.guile.plugins.kafka-data-source12"], "name": "RT_MMI_ORDER", "chain": "USD.CM_R4Q.BLOOMBERG_UST.RFQ", "enable": true, "keys": ["messageId"], "statusRecord": "USD.CM_R4Q.BLOOMBERG_UST.RFQ.GSTATUS", "table": "KAFKA_PUB", "businessIdField": "Id", "targetClassName": "com.scotia.gcm.sa.v1.pretrade.RfqMessage", "groovyMappingScript": "order/order-tor.groovy", "targetTopic": "ist2.source.mmi-lon.rfq.realtime.v1", "targetControlTopic": "ist.landing.mmi-lon.price.eod.control", "targetESIndice": "ist2.source.mmi-lon.rfq.realtime.v1-1-r1", "ionFields": ["Id", "OrderNo", "Trader", "Date", "Time", "TrdDateLast", "TrdTimeLast", "ExpireDate", "ExpireTime", "StatusStr", "TradingStatusStr", "InstrumentId", "CurrencyStr", "TypeStr", "TimeInForceStr", "VerbStr", "Value", "ValueTypeStr", "StopPrice", "TrdValueLast", "TrdQtyLast", "QtyFill", "QtyGoal", "QtyStatusStr"]}, {"comment": ["20. Retired? (Last record Aug-2019): mkv.scotia.guile.plugins.kafka-data-source14"], "name": "RT_MMI_ORDER", "chain": "EUR.CM_R4Q.BLOOMBERG_IRS.RFQ", "enable": true, "keys": ["messageId"], "statusRecord": "EUR.CM_R4Q.BLOOMBERG_IRS.RFQ.GSTATUS", "table": "KAFKA_PUB", "businessIdField": "Id", "targetClassName": "com.scotia.gcm.sa.v1.pretrade.RfqMessage", "groovyMappingScript": "order/order-tor.groovy", "targetTopic": "ist2.source.mmi-lon.rfq.realtime.v1", "targetControlTopic": "ist.landing.mmi-lon.price.eod.control", "targetESIndice": "ist2.source.mmi-lon.rfq.realtime.v1-1-r1", "ionFields": ["Id", "OrderNo", "Trader", "Date", "Time", "TrdDateLast", "TrdTimeLast", "ExpireDate", "ExpireTime", "StatusStr", "TradingStatusStr", "InstrumentId", "CurrencyStr", "TypeStr", "TimeInForceStr", "VerbStr", "Value", "ValueTypeStr", "StopPrice", "TrdValueLast", "TrdQtyLast", "QtyFill", "QtyGoal", "QtyStatusStr"]}, {"comment": ["21. Retired? (0 records in prod): mkv.scotia.guile.plugins.kafka-data-source18"], "name": "RT_MMI_ORDER", "chain": "USD.CM_R4Q.BLOOMBERG_BOLT.RFQ", "enable": true, "keys": ["messageId"], "statusRecord": "USD.CM_R4Q.BLOOMBERG_BOLT.RFQ.GSTATUS", "table": "KAFKA_PUB", "businessIdField": "Id", "targetClassName": "com.scotia.gcm.sa.v1.pretrade.RfqMessage", "groovyMappingScript": "order/order-tor.groovy", "targetTopic": "ist2.source.mmi-lon.rfq.realtime.v1", "targetControlTopic": "ist.landing.mmi-lon.price.eod.control", "targetESIndice": "ist2.source.mmi-lon.rfq.realtime.v1-1-r1", "ionFields": ["Id", "OrderNo", "Trader", "Date", "Time", "TrdDateLast", "TrdTimeLast", "ExpireDate", "ExpireTime", "StatusStr", "TradingStatusStr", "InstrumentId", "CurrencyStr", "TypeStr", "TimeInForceStr", "VerbStr", "Value", "ValueTypeStr", "StopPrice", "TrdValueLast", "TrdQtyLast", "QtyFill", "QtyGoal", "QtyStatusStr"]}, {"comment": ["22. Retired? (Last record in prod: Jan-2023): mkv.scotia.guile.plugins.kafka-data-source18i"], "name": "RT_MMI_ORDER", "chain": "EUR.CM_R4Q.BLOOMBERG_BOLT.RFQ", "enable": true, "keys": ["messageId"], "statusRecord": "EUR.CM_R4Q.BLOOMBERG_BOLT.RFQ.GSTATUS", "table": "KAFKA_PUB", "businessIdField": "Id", "targetClassName": "com.scotia.gcm.sa.v1.pretrade.RfqMessage", "groovyMappingScript": "order/order-tor.groovy", "targetTopic": "ist2.source.mmi-lon.rfq.realtime.v1", "targetControlTopic": "ist.landing.mmi-lon.price.eod.control", "targetESIndice": "ist2.source.mmi-lon.rfq.realtime.v1-1-r1", "ionFields": ["Id", "OrderNo", "Trader", "Date", "Time", "TrdDateLast", "TrdTimeLast", "ExpireDate", "ExpireTime", "StatusStr", "TradingStatusStr", "InstrumentId", "CurrencyStr", "TypeStr", "TimeInForceStr", "VerbStr", "Value", "ValueTypeStr", "StopPrice", "TrdValueLast", "TrdQtyLast", "QtyFill", "QtyGoal", "QtyStatusStr"]}, {"comment": ["23. mkv.scotia.guile.plugins.kafka-data-source20"], "name": "RT_MMI_ORDER", "chain": "EUR.CM_TRADE.TRADESERVER.TRADECAPTURE", "enable": true, "keys": ["messageId"], "statusRecord": "EUR.CM_TRADE.TRADESERVER.TRADECAPTURE.GSTATUS", "table": "KAFKA_PUB", "businessIdField": "Id", "targetClassName": "com.scotia.gcm.sa.v1.pretrade.RfqMessage", "groovyMappingScript": "order/order-tor.groovy", "targetTopic": "ist2.source.mmi-lon.rfq.realtime.v1", "targetControlTopic": "ist.landing.mmi-lon.price.eod.control", "targetESIndice": "ist2.source.mmi-lon.rfq.realtime.v1-1-r1", "ionFields": ["Id", "OrderNo", "Trader", "Date", "Time", "TrdDateLast", "TrdTimeLast", "ExpireDate", "ExpireTime", "StatusStr", "TradingStatusStr", "InstrumentId", "CurrencyStr", "TypeStr", "TimeInForceStr", "VerbStr", "Value", "ValueTypeStr", "StopPrice", "TrdValueLast", "TrdQtyLast", "QtyFill", "QtyGoal", "QtyStatusStr"]}, {"comment": ["24. mkv.scotia.guile.plugins.kafka-data-source21"], "name": "RT_MMI_ORDER", "chain": "EUR.CM_TRADE.TRADESERVER.TRADECAPTURE", "enable": true, "keys": ["messageId"], "statusRecord": "EUR.CM_TRADE.TRADESERVER.TRADECAPTURE.GSTATUS", "table": "KAFKA_PUB", "businessIdField": "Id", "targetClassName": "com.scotia.gcm.sa.v1.pretrade.OrderMessage", "groovyMappingScript": "order/order-tor.groovy", "targetTopic": "ist2.source.mmi-lon.order.realtime.v1", "targetControlTopic": "ist.landing.mmi-lon.price.eod.control", "targetESIndice": "ist2.source.mmi-lon.order.realtime.v1-1-r1", "ionFields": ["Id", "OrderNo", "Trader", "Date", "Time", "TrdDateLast", "TrdTimeLast", "ExpireDate", "ExpireTime", "StatusStr", "TradingStatusStr", "InstrumentId", "CurrencyStr", "TypeStr", "TimeInForceStr", "VerbStr", "Value", "ValueTypeStr", "StopPrice", "TrdValueLast", "TrdQtyLast", "QtyFill", "QtyGoal", "QtyStatusStr"]}]