# Router name (to connect this component using inOut connection) to publish: ROUTER_M_KAFKA
# Component to replace: KAFKA_PRE_TRADE_UK, SS - means Strategical Solution
mkv.COMPONENT=KAFKA_PRE_TRADE_UK_SS
#mkv.CSHOST=sduionln002.lon.bns
mkv.CSHOST=sduionln006.lon.bns
mkv.CSPORT=24002
mkv.DBDIR=db/KAFKA_PRE_TRADE_UK_SS
mkv.LISTEN=*
mkv.LOGSDIR=log/KAFKA_PRE_TRADE_UK_SS
#mkv.pwd = apppub123
mkv.user=apppub
#mkv.PWD=:CA 12 A3 D4 1D C2 84 47 39 77 40 DE E5 A5 3D 50
mkv.PWD=:44 C2 FA 40 94 D9 73 F0 A7 97 72 6C EB 81 99 A8
mkv.currency=CAD
mkv.source=KAFKA_PRE_TRADE_UK_SS
mkv.usemsecs=3
mkv.entitlement=0
#mkv.ZoneId = America/New_York
mkv.logLevel = DEBUG
#mkv.logLevel = INFO
mkv.logsdays=5
mkv.logPrintTime = 1
mkv.logPrintThreadName = 1

# Enables traces and logging on log files and define tracing detail.
# Setting mkv.debug to a value different from 0 enables all tracing level >= DEBUG.
# -1 Maximum detail: adds supplied fields.
# 0 Logging disabled.
# 10 Adds supply events.
# 100 Minimum detail. Enables log for: errors, connectivity events, statistics, platform events, and settings.
# 30 Adds object publishing events.
# 50 Adds transaction and function events.
# 90 Adds subscription events.
# Min: -10000, Max: 10000
mkv.debug = 50
mkv.logName=KAFKA_PRE_TRADE_UK_SS.log
mkv.logToScreen=true

mkv.platform.serviceName=PUB
# period – time in seconds between successive Inner class for handling retry logic executions
mkv.retryInterval=180
mkv.thread.pool.size=1
mkv.timer.timeout = 300
 
