KafkaClient {
   com.sun.security.auth.module.Krb5LoginModule required
   principal="mmi_kafka@BNS"
   useKeyTab=true
   client=true
   keyTab="/cm/gfi/apps/mkv/binaries/KAFKA_PUB_SS/KAFKA_PUB_TRADES_SPLIT_LON/config/mmi_kafka.keytab"
   serviceName="kafka";
};
Client {
   com.sun.security.auth.module.Krb5LoginModule required
   principal="mmi_kafka@BNS"
   useKeyTab=true
   client=true
   keyTab="/cm/gfi/apps/mkv/binaries/KAFKA_PUB_SS/KAFKA_PUB_TRADES_SPLIT_LON/config/mmi_kafka.keytab"
   serviceName="zookeeper";
};
