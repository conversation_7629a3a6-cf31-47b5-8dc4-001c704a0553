[{"comment": ["ionFields - corresponding fields on chain to evaluate target Kafka object (targetClassName)"], "name": "RT_MMI_TRADESPLIT", "chain": "EUR.CM_TRADESPLIT.TRADESERVER.TRADESPLIT", "enable": true, "statusRecord": "EUR.CM_TRADESPLIT.TRADESERVER.TRADESPLIT.GSTATUS", "table": "KAFKA_PUB", "keys": ["messageId"], "businessIdField": "Id", "targetClassName": "com.scm.fi.sa.kafka.serializers.plugins.Row", "conversionRules": null, "groovyMappingScript": "row", "region": "LON", "targetTopic": "prod.landing.mmi-lon.tradesplit.realtime", "targetControlTopic": "prod.landing.mmi-lon.tradesplit.realtime.control", "targetESIndice": "prod.landing.mmi-lon.tradesplit.realtime-1", "ionFields": ["*"]}]