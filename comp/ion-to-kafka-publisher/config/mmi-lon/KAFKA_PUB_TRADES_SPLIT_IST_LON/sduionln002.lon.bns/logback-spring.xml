<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <!--logger levels are set in order of verbosity: TRACE < DEBUG < INFO < WARN < ERROR -->
    <appender name="Console" class="ch.qos.logback.core.ConsoleAppender">
        <!--filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter-->
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSSXXX}: %-5level [%thread] %-3.33logger{39} : %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>DEBUG</level>
        </filter>
        <file>/cm/gfi/logs/mkv/custom/KAFKA_PUB_SS/KAFKA_PUB_TRADES_SPLIT_IST_LON/KAFKA_PUB_TRADES_SPLIT_IST_LON.log</file>
        <encoder>
            <pattern>%d{yy-MM-dd HH:mm:ss.SSS} %-5level [%thread] %logger{36} - %msg%n</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>/cm/gfi/logs/mkv/custom/KAFKA_PUB_SS/KAFKA_PUB_TRADES_SPLIT_IST_LON/KAFKA_PUB_TRADES_SPLIT_IST_LON.%d{yyyy-MM-dd}.%i.gz</fileNamePattern>
            <maxFileSize>150MB</maxFileSize>
            <maxHistory>60</maxHistory>
        </rollingPolicy>
    </appender>

    <!-- New Appender for Errors -->
    <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <file>/cm/gfi/logs/mkv/custom/KAFKA_PUB_SS/KAFKA_PUB_TRADES_SPLIT_IST_LON/KAFKA_PUB_TRADES_SPLIT_IST_LON-Errors.log</file>
        <encoder>
            <pattern>%d{yy-MM-dd HH:mm:ss.SSS} %-5level [%thread] %logger{36} - %msg%n</pattern>
        </encoder>
		<triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<maxFileSize>150MB</maxFileSize>
		</triggeringPolicy>		
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>/cm/gfi/logs/mkv/custom/KAFKA_PUB_SS/KAFKA_PUB_TRADES_SPLIT_IST_LON/KAFKA_PUB_TRADES_SPLIT_IST_LON-Errors.%d{yyyy-MM-dd}.%i.gz</fileNamePattern>
            <maxFileSize>150MB</maxFileSize>
            <maxHistory>60</maxHistory>
        </rollingPolicy>
    </appender>

    <!-- Root Logger -->
    <root level="INFO">
        <appender-ref ref="Console"/>
        <appender-ref ref="FILE"/>
        <appender-ref ref="ERROR_FILE"/>
    </root>

    <!-- ProducerConfig.DEBUG_CONFIG, "all" -->
    <logger name="com.scm.fi" level="DEBUG" />
    <logger name="org.springframework.web.servlet.resource.ResourceHttpRequestHandler" level="ERROR" />
    <logger name="org.springframework.web.servlet.PageNotFound" level="ERROR" />
    <logger name="com.iontrading" level="INFO"/>
    <logger name="org.springframework" level="INFO" />
    <logger name="org.apache.coyote" level="INFO" />
    <logger name="org.springframework.web" level="INFO" />
    <logger name="org.apache.kafka.common.utils.AppInfoParser" level="WARN"/>
    <logger name="org.apache.kafka.common.internals.TransactionManager" level="WARN"/>
    <logger name="org.hibernate.internal.util" level="INFO" />
    <logger name="org.apache.kafka.clients.producer" level="WARN"/>
    <logger name="org.apache.kafka.clients.Metadata" level="WARN"/>
    <logger name="org.apache.kafka.clients" level="WARN"/>
    <logger name="org.apache.kafka.common.security.kerberos.KerberosLogin" level="WARN"/>
    <logger name="kafka-kerberos-refresh-thread" level="WARN"/>
    <logger name="org.hibernate.SQL" level="INFO"/>
    <logger name="org.hibernate.type.descriptor.sql.BasicBinder" level="INFO"/>
    <logger name="com.zaxxer.hikari.HikariConfig" level="INFO" />
    <logger name="com.zaxxer.hikari" level="INFO" />
    <logger name="com.ulisesbocchio.jasyptspringboot" level="WARN"/>
    <logger name="che.coyote.http11.Http11Processor" level="ERROR"/>
	<logger name="org.apache.kafka.common.security.kerberos" level="DEBUG"/>
	<logger name="org.apache.kafka.common.security.authenticator" level="DEBUG"/>
    <logger name="org.apache.coyote.http11.Http11Processor" level="ERROR" />
    <logger name="org.springframework.web.servlet.resource.ResourceHandlerUtils" level="ERROR" />
    <logger name="org.apache.coyote.http11.Http11Processor" level="ERROR" />
    <logger name="org.apache.catalina.core.StandardService" level="ERROR" />
    <logger name="org.apache.catalina" level="ERROR" />
    <logger name="org.springframework.web.servlet.mvc.support.DefaultHandlerExceptionResolver" level="ERROR" />
    <logger name="org.springframework.web.servlet.resource.ResourceHandlerUtils" level="ERROR" />
    <logger name="org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping" level="ERROR"/>
    <logger name="org.apache.tomcat.util.http.parser.Cookie" level="ERROR"/>

</configuration>
