# Router name (to connect this component using inOut connection) to subscribe to:
# CAD.CM_TRADESPLIT.TRADESERVER_TOR.TRADESPLIT chain: ROUTERM_M_PRICING
mkv.COMPONENT=KAFKA_PUB_TRADES_SPLIT_LON_WIN
mkv.CSHOST=sddvmionln01.lon.bns
mkv.CSPORT=24002
mkv.DBDIR=db/KAFKA_PUB_TRADES_SPLIT_LON
mkv.LISTEN=24007
mkv.LOGSDIR=log/KAFKA_PUB_TRADES_SPLIT_LON
mkv.user = apppub
mkv.PWD=:44 C2 FA 40 94 D9 73 F0 A7 97 72 6C EB 81 99 A8
#mkv.PWD = apppub123
mkv.env = DEV
mkv.currency=CAD
mkv.source=KAFKA_PUB_TRADES_SPLIT_LON_WIN
mkv.usemsecs=3
mkv.entitlement=0

#mkv.ZoneId = America/New_York
mkv.logLevel = DEBUG
mkv.debuglevel=1
mkv.logsdays=30
mkv.logPrintTime = 1
mkv.logPrintThreadName = 1

# Enables traces and logging on log files and define tracing detail.
# Setting mkv.debug to a value different from 0 enables all tracing level >= DEBUG.
# -1 Maximum detail: adds supplied fields.
# 0 Logging disabled.
# 10 Adds supply events.
# 100 Minimum detail. Enables log for: errors, connectivity events, statistics, platform events, and settings.
# 30 Adds object publishing events.
# 50 Adds transaction and function events.
# 90 Adds subscription events.
# Min: -10000, Max: 10000
mkv.DEBUG=50
mkv.logName=KAFKA_PUB_TRADES_SPLIT_LON.log
mkv.logToScreen=true
# period – time in seconds between successive Inner class for handling retry logic executions
mkv.retryInterval=180
mkv.thread.pool.size=1
mkv.timer.timeout = 300
 
mkv.platform.serviceName=PUB
mkv.db.connection.pwd=FIEUKAPP_123
