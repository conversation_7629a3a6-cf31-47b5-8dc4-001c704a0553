KafkaClient {
   com.sun.security.auth.module.Krb5LoginModule required
   principal="kafka_ist_producer@BNS"
   useKeyTab=true
   client=true
   keyTab="/cm/gfi/apps/mkv/binaries/KAFKA_PUB_SS/KAFKA_PUB_TRADES_SPLIT_IST_LON/config/kafka_ist_producer.keytab"
   serviceName="kafka";
};
Client {
   com.sun.security.auth.module.Krb5LoginModule required
   principal="kafka_ist_producer@BNS"
   useKeyTab=true
   client=true
   keyTab="/cm/gfi/apps/mkv/binaries/KAFKA_PUB_SS/KAFKA_PUB_TRADES_SPLIT_IST_LON/config/kafka_ist_producer.keytab"
   serviceName="zookeeper";
};