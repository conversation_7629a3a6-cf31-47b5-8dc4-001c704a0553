[{"comment": ["ionFields - corresponding fields on chain to evaluate target Kafka object (targetClassName).", "ionFields '*' value means to subscribe to all fields on current chain.", "groovyMappingScript '*' or 'Row' values - to generate flat Row target object (like landing topic)"], "name": "RT_MIFID_ORDER_BTEC_UK", "chain": "EUR.CM_ORDER.BTECREPOS_UK.ORDER", "enable": true, "statusRecord": "EUR.CM_STATUS.MIFID_ORDER_BTEC_UK.GSTATUS", "table": "KAFKA_PUB", "keys": ["messageId"], "businessIdField": "Id", "targetClassName": "com.scm.fi.sa.kafka.serializers.plugins.Row", "groovyMappingScript": "EUR.CM_ORDER.BTECREPOS_UK.ORDER.groovy", "targetTopic": "ist.source.mmi-lon.order.realtime", "ionFields": ["AuthorizedBy", "AccountId", "Active", "ActiveStr", "Attribute", "<PERSON><PERSON>", "BasisDecStatic", "BasisFmtStatic", "BasisTickStatic", "CancelReason", "CancelReasonCode", "CMAttribute", "CMAttributeStatus", "Code", "Completed", "CompNameOrigin", "CompTypeOrigin", "CurrencyStr", "CurrentAction", "CurrentActionStr", "CustomerInfo", "Date", "DateCreation", "DateMktCreationUTC", "DateUTC", "Desc", "Desk", "Discount", "DiscountDecStatic", "DiscountFmtStatic", "DiscountTickStatic", "ExecutionId", "ExecutionIdType", "ExecutionMktId", "ExecutionMktIdType", "ExemptionFlag", "ExpireDate", "ExpireTime", "External", "FreeText", "Id", "<PERSON><PERSON><PERSON><PERSON>", "InstrumentId", "Interference", "InvestmentId", "InvestmentIdType", "InvestmentMktId", "InvestmentMktIdType", "IsAON", "IsOvertrading", "LiquidityProvider", "MarketStatus", "OBDest", "OnlyBest", "OrderNo", "OrderTmpId", "OwnershipPolicy", "ParentId", "Price", "PriceDecStatic", "PriceFmtStatic", "PriceTickStatic", "QGConsistent", "QGConsistentStr", "QGMode", "QGWorking", "QtyFill", "QtyGoal", "QtyHit", "QtyHitDeleted", "QtyOvertraded", "QtyShown", "QtyStatus", "QtyStatusStr", "QtyTick", "QtyTot", "QtyTotReq", "RejectReason", "RejectReasonStr", "RequestRef", "RwtTag", "ShortSelling", "Spread", "SpreadDecStatic", "SpreadFmtStatic", "SpreadTickStatic", "Status", "StatusStr", "StopCond", "StopCondStr", "StopInstrumentId", "StopPrice", "Strategy", "Text", "Time", "TimeCreation", "TimeInForce", "TimeInForceStr", "TimeMktCreationUTC", "TimeStamp", "TimeStampEx", "TimeUpd", "TimeUpdEx", "Trader", "TraderLocation", "TradingStatus", "TradingStatusStr", "TrdCount", "TrdDateLast", "TrdQtyLast", "TrdTimeLast", "TrdValueAvg", "TrdValueLast", "Type", "TypeStr", "UserData", "UserTag", "UserText", "Value", "ValueDec", "ValueFmt", "ValueTick", "ValueType", "ValueTypeStr", "Verb", "VerbStr", "Warning", "YDiff", "YDiffDecStatic", "YDiffFmtStatic", "YDiffTickStatic", "Yield", "YieldDecStatic", "YieldFmtStatic", "YieldTickStatic"]}, {"comment": [""], "name": "RT_MIFID_ORDER_BTEC_IRL", "chain": "EUR.CM_ORDER.BTECREPOS_IRL.ORDER", "enable": true, "statusRecord": "EUR.CM_STATUS.MIFID_ORDER_BTEC_IRL.GSTATUS", "table": "KAFKA_PUB", "keys": ["messageId"], "businessIdField": "Id", "targetClassName": "com.scm.fi.sa.kafka.serializers.plugins.Row", "groovyMappingScript": "Row", "targetTopic": "ist.source.mmi-lon.order.realtime", "ionFields": ["*"]}]