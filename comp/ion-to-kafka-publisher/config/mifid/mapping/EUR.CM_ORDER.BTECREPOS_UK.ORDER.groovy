import java.text.SimpleDateFormat
import static Constants.*

class Constants {
        static final UNDEF = "NaN"
        static final SCHEMA_VERSION = "1.97"
        static final DEFAULT_DATE = "0"
        static final MESSAGE_TYPE = "NativeMessage"

        static stringNullCheck(String input) {
                if (input == "" || input == null)
                        return UNDEF
                else
                        return input
        }

        static getDate(String input, boolean mandatory) {
                try { // Deprecated: return Date.parse("yyyyMMdd", input)
                        def date = new SimpleDateFormat("yyyyMMdd").parse(input)
                        return input
                } catch(Exception ex) {
                        if (mandatory)
                                return DEFAULT_DATE
                        else
                                return ""
                }
        }
        static doubleValidityCheck(String input, boolean mandatory) {
                if (input == "Infinity" || input == "NaN" || input == "" || input == null)
                {
                        if (mandatory)
                                return 0.0
                        else
                                return null
                }
                else {
                        try {
                                return input.toDouble()
                        } catch(Exception e) {
                                if (mandatory)
                                        return 0.0
                                else
                                        return null
                        }
                }
        } // doubleValidityCheck()

        static intValidityCheck(String input, boolean mandatory) {
                if (input == "Infinity" || input == "NaN" || input == "" || input == null || input == "0")
                {
                        if (mandatory)
                                return 0
                        else
                                return null
                }
                else {
                        try {
                                return input.toInteger()
                        } catch(Exception e) {
                                if (mandatory)
                                        return 0
                                else
                                        return null
                        }
                }
        } // doubleValidityCheck()

}
[
        header  : [
                messageId                    : row.header.messageId,
                businessId                   : row.header.businessId,
                batchId                      : null,
                sourceSystem                 : row.header.sourceSystem,
                secondarySourceSystem        : row.header.secondarySourceSystem,
                sourceSystemCreationTimestamp: row.header.sourceSystemCreationTimestamp,
                sentBy                       : row.header.sentBy,
                sentTo                       : row.header.sentTo,
                messageType                  : MESSAGE_TYPE,
                schemaVersion                : SCHEMA_VERSION,
                processing                   : row.header.processing,
                recordOffset                 : null,
        ],
        nativeData: [
             tags : [
                AuthorizedBy : stringNullCheck(row.nativeData.tags.get('AuthorizedBy')),
                AccountId : stringNullCheck(row.nativeData.tags.get('AccountId')),
                Active : stringNullCheck(row.nativeData.tags.get('Active')),
                ActiveStr : stringNullCheck(row.nativeData.tags.get('ActiveStr')),
                Attribute : stringNullCheck(row.nativeData.tags.get('Attribute')),
                Basis : stringNullCheck(row.nativeData.tags.get('Basis')),
                BasisDecStatic : stringNullCheck(row.nativeData.tags.get('BasisDecStatic')),
                BasisFmtStatic : stringNullCheck(row.nativeData.tags.get('BasisFmtStatic')),
                BasisTickStatic : stringNullCheck(row.nativeData.tags.get('BasisTickStatic')),
                CancelReason : stringNullCheck(row.nativeData.tags.get('CancelReason')),
                CancelReasonCode : stringNullCheck(row.nativeData.tags.get('CancelReasonCode')),
                CMAttribute : stringNullCheck(row.nativeData.tags.get('CMAttribute')),
                CMAttributeStatus : stringNullCheck(row.nativeData.tags.get('CMAttributeStatus')),
                Code : stringNullCheck(row.nativeData.tags.get('Code')),
                Completed : stringNullCheck(row.nativeData.tags.get('Completed')),
                CompNameOrigin : stringNullCheck(row.nativeData.tags.get('CompNameOrigin')),
                CompTypeOrigin : stringNullCheck(row.nativeData.tags.get('CompTypeOrigin')),
                CurrencyStr : stringNullCheck(row.nativeData.tags.get('CurrencyStr')),
                CurrentAction : stringNullCheck(row.nativeData.tags.get('CurrentAction')),
                CurrentActionStr : stringNullCheck(row.nativeData.tags.get('CurrentActionStr')),
                CustomerInfo : stringNullCheck(row.nativeData.tags.get('CustomerInfo')),
                Date : stringNullCheck(row.nativeData.tags.get('Date')),
                DateCreation : stringNullCheck(row.nativeData.tags.get('DateCreation')),
                DateMktCreationUTC : stringNullCheck(row.nativeData.tags.get('DateMktCreationUTC')),
                DateUTC : stringNullCheck(row.nativeData.tags.get('DateUTC')),
                Desc : stringNullCheck(row.nativeData.tags.get('Desc')),
                Desk : stringNullCheck(row.nativeData.tags.get('Desk')),
                Discount : stringNullCheck(row.nativeData.tags.get('Discount')),
                DiscountDecStatic : stringNullCheck(row.nativeData.tags.get('DiscountDecStatic')),
                DiscountFmtStatic : stringNullCheck(row.nativeData.tags.get('DiscountFmtStatic')),
                DiscountTickStatic : stringNullCheck(row.nativeData.tags.get('DiscountTickStatic')),
                ExecutionId : stringNullCheck(row.nativeData.tags.get('ExecutionId')),
                ExecutionIdType : stringNullCheck(row.nativeData.tags.get('ExecutionIdType')),
                ExecutionMktId : stringNullCheck(row.nativeData.tags.get('ExecutionMktId')),
                ExecutionMktIdType : stringNullCheck(row.nativeData.tags.get('ExecutionMktIdType')),
                ExemptionFlag : stringNullCheck(row.nativeData.tags.get('ExemptionFlag')),
                ExpireDate : stringNullCheck(row.nativeData.tags.get('ExpireDate')),
                ExpireTime : stringNullCheck(row.nativeData.tags.get('ExpireTime')),
                External : stringNullCheck(row.nativeData.tags.get('External')),
                FreeText : stringNullCheck(row.nativeData.tags.get('FreeText')),
                Id : stringNullCheck(row.nativeData.tags.get('Id')),
                InDepth : stringNullCheck(row.nativeData.tags.get('InDepth')),
                InstrumentId : stringNullCheck(row.nativeData.tags.get('InstrumentId')),
                Interference : stringNullCheck(row.nativeData.tags.get('Interference')),
                InvestmentId : stringNullCheck(row.nativeData.tags.get('InvestmentId')),
                InvestmentIdType : stringNullCheck(row.nativeData.tags.get('InvestmentIdType')),
                InvestmentMktId : stringNullCheck(row.nativeData.tags.get('InvestmentMktId')),
                InvestmentMktIdType : stringNullCheck(row.nativeData.tags.get('InvestmentMktIdType')),
                IsAON : stringNullCheck(row.nativeData.tags.get('IsAON')),
                IsOvertrading : stringNullCheck(row.nativeData.tags.get('IsOvertrading')),
                LiquidityProvider : stringNullCheck(row.nativeData.tags.get('LiquidityProvider')),
                MarketStatus : stringNullCheck(row.nativeData.tags.get('MarketStatus')),
                OBDest : stringNullCheck(row.nativeData.tags.get('OBDest')),
                OnlyBest : stringNullCheck(row.nativeData.tags.get('OnlyBest')),
                OrderNo : stringNullCheck(row.nativeData.tags.get('OrderNo')),
                OrderTmpId : stringNullCheck(row.nativeData.tags.get('OrderTmpId')),
                OwnershipPolicy : stringNullCheck(row.nativeData.tags.get('OwnershipPolicy')),
                ParentId : stringNullCheck(row.nativeData.tags.get('ParentId')),
                Price : stringNullCheck(row.nativeData.tags.get('Price')),
                PriceDecStatic : stringNullCheck(row.nativeData.tags.get('PriceDecStatic')),
                PriceFmtStatic : stringNullCheck(row.nativeData.tags.get('PriceFmtStatic')),
                PriceTickStatic : stringNullCheck(row.nativeData.tags.get('PriceTickStatic')),
                QGConsistent : stringNullCheck(row.nativeData.tags.get('QGConsistent')),
                QGConsistentStr : stringNullCheck(row.nativeData.tags.get('QGConsistentStr')),
                QGMode : stringNullCheck(row.nativeData.tags.get('QGMode')),
                QGWorking : stringNullCheck(row.nativeData.tags.get('QGWorking')),
                QtyFill : stringNullCheck(row.nativeData.tags.get('QtyFill')),
                QtyGoal : stringNullCheck(row.nativeData.tags.get('QtyGoal')),
                QtyHit : stringNullCheck(row.nativeData.tags.get('QtyHit')),
                QtyHitDeleted : stringNullCheck(row.nativeData.tags.get('QtyHitDeleted')),
                QtyOvertraded : stringNullCheck(row.nativeData.tags.get('QtyOvertraded')),
                QtyShown : stringNullCheck(row.nativeData.tags.get('QtyShown')),
                QtyStatus : stringNullCheck(row.nativeData.tags.get('QtyStatus')),
                QtyStatusStr : stringNullCheck(row.nativeData.tags.get('QtyStatusStr')),
                QtyTick : stringNullCheck(row.nativeData.tags.get('QtyTick')),
                QtyTot : stringNullCheck(row.nativeData.tags.get('QtyTot')),
                QtyTotReq : stringNullCheck(row.nativeData.tags.get('QtyTotReq')),
                RejectReason : stringNullCheck(row.nativeData.tags.get('RejectReason')),
                RejectReasonStr : stringNullCheck(row.nativeData.tags.get('RejectReasonStr')),
                RequestRef : stringNullCheck(row.nativeData.tags.get('RequestRef')),
                RwtTag : stringNullCheck(row.nativeData.tags.get('RwtTag')),
                ShortSelling : stringNullCheck(row.nativeData.tags.get('ShortSelling')),
                Spread : stringNullCheck(row.nativeData.tags.get('Spread')),
                SpreadDecStatic : stringNullCheck(row.nativeData.tags.get('SpreadDecStatic')),
                SpreadFmtStatic : stringNullCheck(row.nativeData.tags.get('SpreadFmtStatic')),
                SpreadTickStatic : stringNullCheck(row.nativeData.tags.get('SpreadTickStatic')),
                Status : stringNullCheck(row.nativeData.tags.get('Status')),
                StatusStr : stringNullCheck(row.nativeData.tags.get('StatusStr')),
                StopCond : stringNullCheck(row.nativeData.tags.get('StopCond')),
                StopCondStr : stringNullCheck(row.nativeData.tags.get('StopCondStr')),
                StopInstrumentId : stringNullCheck(row.nativeData.tags.get('StopInstrumentId')),
                StopPrice : stringNullCheck(row.nativeData.tags.get('StopPrice')),
                Strategy : stringNullCheck(row.nativeData.tags.get('Strategy')),
                Text : stringNullCheck(row.nativeData.tags.get('Text')),
                Time : stringNullCheck(row.nativeData.tags.get('Time')),
                TimeCreation : stringNullCheck(row.nativeData.tags.get('TimeCreation')),
                TimeInForce : stringNullCheck(row.nativeData.tags.get('TimeInForce')),
                TimeInForceStr : stringNullCheck(row.nativeData.tags.get('TimeInForceStr')),
                TimeMktCreationUTC : stringNullCheck(row.nativeData.tags.get('TimeMktCreationUTC')),
                TimeStamp : stringNullCheck(row.nativeData.tags.get('TimeStamp')),
                TimeStampEx : stringNullCheck(row.nativeData.tags.get('TimeStampEx')),
                TimeUpd : stringNullCheck(row.nativeData.tags.get('TimeUpd')),
                TimeUpdEx : stringNullCheck(row.nativeData.tags.get('TimeUpdEx')),
                Trader : stringNullCheck(row.nativeData.tags.get('Trader')),
                TraderLocation : stringNullCheck(row.nativeData.tags.get('TraderLocation')),
                TradingStatus : stringNullCheck(row.nativeData.tags.get('TradingStatus')),
                TradingStatusStr : stringNullCheck(row.nativeData.tags.get('TradingStatusStr')),
                TrdCount : stringNullCheck(row.nativeData.tags.get('TrdCount')),
                TrdDateLast : stringNullCheck(row.nativeData.tags.get('TrdDateLast')),
                TrdQtyLast : stringNullCheck(row.nativeData.tags.get('TrdQtyLast')),
                TrdTimeLast : stringNullCheck(row.nativeData.tags.get('TrdTimeLast')),
                TrdValueAvg : stringNullCheck(row.nativeData.tags.get('TrdValueAvg')),
                TrdValueLast : stringNullCheck(row.nativeData.tags.get('TrdValueLast')),
                Type : stringNullCheck(row.nativeData.tags.get('Type')),
                TypeStr : stringNullCheck(row.nativeData.tags.get('TypeStr')),
                UserData : stringNullCheck(row.nativeData.tags.get('UserData')),
                UserTag : stringNullCheck(row.nativeData.tags.get('UserTag')),
                UserText : stringNullCheck(row.nativeData.tags.get('UserText')),
                Value : stringNullCheck(row.nativeData.tags.get('Value')),
                ValueDec : stringNullCheck(row.nativeData.tags.get('ValueDec')),
                ValueFmt : stringNullCheck(row.nativeData.tags.get('ValueFmt')),
                ValueTick : stringNullCheck(row.nativeData.tags.get('ValueTick')),
                ValueType : stringNullCheck(row.nativeData.tags.get('ValueType')),
                ValueTypeStr : stringNullCheck(row.nativeData.tags.get('ValueTypeStr')),
                Verb : stringNullCheck(row.nativeData.tags.get('Verb')),
                VerbStr : stringNullCheck(row.nativeData.tags.get('VerbStr')),
                Warning : stringNullCheck(row.nativeData.tags.get('Warning')),
                YDiff : stringNullCheck(row.nativeData.tags.get('YDiff')),
                YDiffDecStatic : stringNullCheck(row.nativeData.tags.get('YDiffDecStatic')),
                YDiffFmtStatic : stringNullCheck(row.nativeData.tags.get('YDiffFmtStatic')),
                YDiffTickStatic : stringNullCheck(row.nativeData.tags.get('YDiffTickStatic')),
                Yield : stringNullCheck(row.nativeData.tags.get('Yielda')),
                YieldDecStatic : stringNullCheck(row.nativeData.tags.get('YieldDecStatic')),
                YieldFmtStatic : stringNullCheck(row.nativeData.tags.get('YieldFmtStatic')),
                YieldTickStatic : stringNullCheck(row.nativeData.tags.get('YieldTickStatic')),
        ],
        data: null
    ]
]