import java.text.SimpleDateFormat
import static Constants.*

class Constants {
        static final UNDEF = "NaN"
        static final SCHEMA_VERSION = "1.97"
        static final DEFAULT_DATE = "0"
        static final MESSAGE_TYPE = "NativeMessage"

        static String stringNullCheck(String input) {
                if (input == "" || input == null)
                        return UNDEF
                else
                        return input
        }
}

[
        header  : [
                messageId                    : row.header.messageId,
                businessId                   : row.header.businessId,
                batchId                      : null,
                sourceSystem                 : row.header.sourceSystem,
                secondarySourceSystem        : row.header.secondarySourceSystem,
                sourceSystemCreationTimestamp: row.header.sourceSystemCreationTimestamp,
                sentBy                       : row.header.sentBy,
                sentTo                       : row.header.sentTo,
                messageType                  : MESSAGE_TYPE,
                schemaVersion                : SCHEMA_VERSION,
                processing                   : row.header.processing,
                recordOffset                 : null,
        ],
        nativeData: [
                tags : row.nativeData.tags.collectEntries { key, value ->
                        [(key) : Constants.stringNull<PERSON>heck(value)]
                },
                data: null
        ]
]
