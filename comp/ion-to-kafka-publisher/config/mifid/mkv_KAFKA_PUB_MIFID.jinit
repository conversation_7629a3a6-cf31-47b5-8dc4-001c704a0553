# Router name (to connect this component using inOut connection) to publish: ROUTER_M_KAFKA
mkv.COMPONENT=KAFKA_PUB_MIFID
mkv.CSHOST=sduionln006.lon.bns
mkv.CSPORT=24002
mkv.DBDIR=db/KAFKA_PUB_MIFID
mkv.LISTEN=24083
mkv.LOGSDIR=log/KAFKA_PUB_MIFID
mkv.user = apppub
# IST password does not need to be secure (mkv.pwd = apppub123) but we encrypt it only to verify decryption algorithm
#mkv.pwd = apppub123
mkv.pwd = :44 C2 FA 40 94 D9 73 F0 A7 97 72 6C EB 81 99 A8
mkv.currency=CAD
mkv.source=KAFKA_PUB_MIFID
mkv.usemsecs=3
mkv.entitlement=0

mkv.ZoneId = America/New_York
mkv.logLevel = DEBUG
#mkv.logLevel = INFO
mkv.logsdays=10
mkv.logPrintTime = 1
mkv.logPrintThreadName = 1

# Enables traces and logging on log files and define tracing detail.
# Setting mkv.debug to a value different from 0 enables all tracing level >= DEBUG.
# -1 Maximum detail: adds supplied fields.
# 0 Logging disabled.
# 10 Adds supply events.
# 100 Minimum detail. Enables log for: errors, connectivity events, statistics, platform events, and settings.
# 30 Adds object publishing events.
# 50 Adds transaction and function events.
# 90 Adds subscription events.
# Min: -10000, Max: 10000
mkv.debug = 100
mkv.logName=KAFKA_PUB_MIFID.log
mkv.logToScreen=true

mkv.platform.serviceName=PUB
# period – time in seconds between successive Inner class for handling retry logic executions
mkv.retryInterval=180
mkv.thread.pool.size=1
mkv.timer.timeout = 300
 
