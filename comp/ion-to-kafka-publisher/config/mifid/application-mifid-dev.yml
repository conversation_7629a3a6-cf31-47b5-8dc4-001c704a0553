########################################################################################################################
# Dynamic Configuration: Consider externalizing Kafka settings that might change frequently or depending on the
# environment (like bootstrap-servers, security settings) to avoid redeployment's for configuration changes.
# Kafka related parameters was used from KAFKA_PUB1_TOR component
########################################################################################################################
server:
  port: 8086                 # Application server port
  tomcat.threads.max: 200     # Maximum number of threads for Tomcat server
  tomcat.threads.min-spare: 1 # Minimum spare threads for Tomcat serverApplication Specific Properties

########################################################################################################################
# Custom Ion2KafkaPubApp Properties
########################################################################################################################
app:
  skipDbOperations: false
  skipDbOperationsInCaseOfError: true
  #queryGetChainsToRepublish: select distinct chain from KAFKA_PUB where is_published = 0 and TRUNC(ts) = TRUNC(SYSDATE)
  queryGetChainsToRepublish: select * from  KAFKA_PUB where is_published != 1 order by TOPIC, AUTO_ID
  # How many hours to keep records for redundancy in KAFKA_PUB table
  # To remove records from KAFKA_PUB that older than (in hours):
  dataRemovalHours: 96 # 4 days
  display-record-step: 200       # Step To display publishing records (to control log file size)
  isNaNValuesLog: false          # print "NaN values received for field..." warnings to the log (since it could be many)
  run-free-memory-thread: true   # Run Memory Diagnostic Thread (logFreeMemory)
  log-free-memory.interval: 120  # Call logFreeMemory() function every (seconds):
  refreshInterval: 5000          # Web GUI: Refresh interval in milliseconds for Metrics GUI (http://localhost:8080/)
  errorNumberToDisplay: 10       # Web GUI: How many last errors to shows on web GUI
  # data-time-zone: America/Toronto # Possible values are: America/Toronto, America/New_York, Europe/London, Asia/Singapore
  config:
    path: config/mifid/          # path to json mappings configuration files: value could be overwritten from command line by: ION_KAFKA_CONFIG
    mapping: config/mifid/mapping/     # path to Groovy mapping scripts (from source (Row) to target object)
    data-extract-mappings: dataExtract.json # Chains, Field list to subscribe to Ion and corresponding Kafka topic
########################################################################################################################
# Asynchronous Execution Configuration:
# Parameters for AsyncConfig: to run all methods with @Async
########################################################################################################################
async:
  executor:
    corePoolSize: 8                   # Core pool size of executor, for a maximum performance this number should be equal to logical processors#
    maxPoolSize: 8                    # Maximum pool size of executor
    queueCapacity: 500                # Capacity of the executor queue
    threadNamePrefix: KafkaPublisher- # Prefix for thread names created by the executor: Related Thread will be named like KafkaPublisher-1,2,3,..

########################################################################################################################
# Spring related properties
########################################################################################################################
spring:
  application.name: Ion2KafkaPubApp
  main.banner-mode: off
  main.allow-bean-definition-overriding: true
  groovy.template.check-template-location: false # To disable GroovyTemplateAutoConfiguration
  task:
    execution.thread-name-prefix: task-
    scheduling:
      thread-name-prefix: scheduling-
      shutdown.await-termination: true
  ######################################################################################################################
  # Spring Kafka settings:
  #   https://confluence.agile.bns/display/ATH/How+To+Migrate+to+NEW+IST+Kafka
  #   https://confluence.agile.bns/pages/viewpage.action?pageId=264801581
  #   https://confluence.agile.bns/pages/viewpage.action?pageId=265587791&showComments=true
  # Kafka related VM Parameters:
  #   -Djavax.security.auth.useSubjectCredsOnly=true
  #   -Dsun.security.krb5.disableReferrals=true
  #   -Djava.security.auth.login.config=D:/Utils/SA/KAFKA/kafka_client_jaas.conf
  #   -Djava.security.krb5.conf=D:/Utils/SA/KAFKA/krb5_ist.conf
  #   -Dspring.kafka.ssl.trust-store-location=file:d:\\kafka\\dev\\kafka.client.truststore.jks
  #   -Dspring.kafka.ssl.trust-store-password=kafkaclientssl
  ######################################################################################################################
  kafka:
    bootstrap-servers: dp.ist.bns:9040
    #mkv.scotia.guile.plugin-defaults.GenericKafkaProducer.brokers = dp.uat.bns:9030
    properties:
      # Kafka producer will use SASL (Simple Authentication and Security Layer) for authentication and SSL (Secure Sockets Layer) for encryption1.
      security.protocol: SASL_SSL
      # The Schema Registry is a service that provides a RESTful interface for storing and retrieving Avro schemas.
      # It maintains a versioned history of all schemas, provides compatibility settings, and allows evolution of schemas over time.
      schema.registry.url: https://dp.ist.bns:1443
    #ssl: # - moved to VM command line parameters
      # trust-store-location: "file:\\d:\\kafka\\dev\\kafka.client.truststore.jks"
      #trust-store-location: "file:/tmp/kshiff/IonKafkaPublisher/config/kafka/devkafka.client.truststore.jks"
      #trust-store-password: kafkaclientssl
    producer:
      buffer-memory: 437248000 # 437M - used to hold the records that are waiting to be sent to the Kafka broker
      # Acknowledgment level: all- producer.send() call will wait for all the brokers (Kafka servers) to respond that
      # they have received the message. This is the most robust setting for guaranteed message delivery but also the most
      # costly (involves multiple servers to write to disk and respond before the producer gets the acknowledgment)
      acks: all
      #compression.type: lz4
      # properties.enable.idempotence: true  # true -  ensures that duplicates are not introduced due to unexpected retries.
      # properties.enable.idempotence: true  # true -  ensures that duplicates are not introduced due to unexpected retries.
      # This identifier will be included in the metadata sent to the broker, making it easier to distinguish between
      # different producers in server logs and monitoring tools.
      client-id: kafka_ist_producer@BNS
      retries: 7200 # How many times the producer will attempt to send a message before marking it as failed.
      # How many bytes (which translates to messages) can be packaged into a single network request to a Kafka server,
      # thereby reducing network traffic and increasing performance. It defaults to 16384 bytes
      batch-size: 65536
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: io.confluent.kafka.serializers.KafkaAvroSerializer
      properties:
        linger.ms: 500 # Number of milliseconds a producer is willing to wait before sending a batch to the Kafka
        # true - producer will ensure that exactly one copy of each message is written in the stream.
        # false - producer retries due to broker failures, etc., may write duplicates of the retried message in the
        # stream. Note that enabling idempotence requires max.in.flight.requests.per.connection to be less than or
        # equal to 5 (with message ordering preserved for any allowable value), retries to be greater than 0,
        # and acks must be 'all'.
        enable.idempotence: true
        retry.backoff.ms: 1000      # the producer will wait between retries (for individual operations) in ms
        # How many requests can be made in parallel to any partition1. Determines the maximum number of unacknowledged
        # requests the client will send on a single connection before blocking.
        # To strictly ensure ordering set it to 1 and properties.enable.idempotence: true
        # For stress test csv publisher this value should be 10 (or more), for other cases "1"
        max.in.flight.requests.per.connection: 5  # Must be <=5 for idempotence
        # The maximum amount of time the producer will wait for a response from the Kafka broker for each request.
        # If the broker does not respond within this timeout, the producer will consider the request as failed
        # delivery timeout ms=(request timeout ms+linger ms)×retries
        # delivery.timeout.ms should be >= than linger.ms + request.timeout.ms
        request.timeout.ms: 2600000 # Increase for Debug Mode
        delivery.timeout.ms: 3000000           # Record will be failed if they can’t be delivered in 46 minutes
        # maximum idle time for a connection to remain open without sending or receiving any data.
        # If a connection is idle for longer than this period, the client will proactively close it. This helps manage
        # resources by closing unused connections and can improve scalability in environments with many clients.
        connections.max.idle.ms: 540000  # 9 minutes
        # The max.block.ms property specifies the maximum amount of time (in milliseconds) the KafkaProducer send()
        # and partitionsFor() methods will block. This occurs when the producer's send buffer is full, and the producer
        # can't accept more records due to backpressure from the broker or network issues.
        # If the time limit is exceeded, a TimeoutException is thrown.
        max.block.ms: 60000 # 1 minute
        # The delay in ms between consecutive connection attempts made by the producer or consumer when they lose their
        # connection to the Kafka broker. Should be balanced to handle transient errors without overwhelming the system.
        # retry.backoff.ms property is specific to retries for individual operations, while reconnect.backoff.ms is for
        # reconnections when the producer or consumer loses the connection to the broker.
        reconnect.backoff.ms: 1000
        # maximum amount of time to wait when exponentially backing off between reconnection attempts
        reconnect.backoff.max.ms: 10000
########################################################################################################################
# Spring DB related properties
################################################## ######################################################################
  datasource.driver-class-name: oracle.jdbc.OracleDriver
  datasource.url: *********************************************
  datasource.username: fietoapp
  datasource.password: fietoapp_123
  jpa.show-sql: false
  jpa.open-in-view: false
  jpa.hibernate.ddl-auto: none
  sql.init.mode: never
  # controls how frequently HikariCP will attempt to keep a connection alive, in order to prevent it from being timed out by the database or network infrastructure

#!!com.scm.fi.MMI2ESRecon.elasticsearch.ElasticSearchConnection
elasticsearch:
  # ES LOGIN related values
  url: https://dp.ist.bns:9200
  username: "s1562013"
  key: "w02fwF/E2G6w/Put8A9gNA=="
  passwordEncrypted: "4AqSlT4wMfszLS4kkd90DHNUCAIxtACD"
  maxRetryTimeout: 600000    # MAX_RETRY_TIMEOUT: 10 * 60 * 1000 - 10 minutes
  connectionTimeout: 600000  # CONNECTION_TIMEOUT: 10 * 60 * 1000 - 10 minutes
  # An optional timeout to control how long search is allowed to take in seconds
  queryTimeout: 60
  # The number of search hits to return. Defaults to 10.
  maxNumberOfRecords: 100
  # The scroll timeout controls how long in minutes Elasticsearch will keep the search context alive. This allows
  # retrieving large result sets via multiple scroll requests.
  scrollTimeout: 20
  # Query to run against each Indice (by up to 1024 ids)
  # to get list of records with founded Ids
  esQuery2GetByMessageId: >
    {
      "_source": false,
      "query": {
        "ids": {
          "values": ["{{arrayIds}}"]
        }
      },
      "script_fields": {
        "exists": {
          "script": {
            "source": "doc['_id'].value == params.id",
            "params": {
              "id": ["{{arrayIds}}"]
            }
          }
        }
      }
    }


########################################################################################################################
# spring-boot-starter-actuator related properties: /actuator/health
########################################################################################################################
management:
  endpoints:
    enabled-by-default: true
    web.exposure.include: 'health,info,configprops'  # http://localhost:8083/actuator/configprops
  endpoint:
    configprops.enabled: true
    info.enabled: true

kafka:
  # parameters to generate MessageId to publish (usually empty)
  GenericKafkaProducer.sa-secondary-source-system:
  GenericKafkaProducer.sa-message-id-prefix: mmi-tor
  # 1 - means to use separate thread for each source csv file, 0 - means to use single thread to publish all csv files
  isKafkaMultiThreadingPublishing: false
