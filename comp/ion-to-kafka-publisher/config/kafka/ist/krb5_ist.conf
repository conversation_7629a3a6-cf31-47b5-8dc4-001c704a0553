[libdefaults]
  #Parameter removed since it is not supports open jdk 17
  #renew_lifetime = 7d
  forwardable = true
  default_realm = BNS
  ticket_lifetime = 24h
  dns_lookup_realm = false
  dns_lookup_kdc = false
  #default_ccache_name = c:/temp/krb5cache/krb5cc_%{uid}
  #default_tgs_enctypes = aes des3-cbc-sha1 rc4 des-cbc-md5
  #default_tkt_enctypes = aes des3-cbc-sha1 rc4 des-cbc-md5

[domain_realm]
  BNS = BNS

[realms]
  BNS = {
    admin_server = lvappi01180.cloud.bns
    kdc = lvappi01180.cloud.bns
  }
