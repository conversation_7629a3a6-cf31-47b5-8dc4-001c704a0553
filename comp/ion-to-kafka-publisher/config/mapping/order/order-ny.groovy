import java.text.SimpleDateFormat
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter

import static Constants.*

class Constants {
	static final UNKNOWN = "Unknown"
    static final String SCHEMA_VERSION = "1.144"
	static final DEFAULT_DATE = "19000101"
	static final SOURCE_EXTERNAL_ID_TYPE = "MARKET_INSTRUMENT_ID"
	static final MESSAGE_TYPE_ORDER = "OrderMessage"
	static final TIMEZONE_CITY = "America/New_York"
	static final QUANTITY_TYPE = "UNIT"

    static stringNullCheck(String input) {
        if (input == "" || input == null)
            return UNKNOWN
        else
            return input
    }

	static getDate(String input, boolean mandatory) {
        try { // Deprecated: return Date.parse("yyyyMMdd", input)
            return new SimpleDateFormat("yyyyMMdd").parse(input).toInstant()
		} catch(Exception ex) {
			if (mandatory)
                return new SimpleDateFormat("yyyyMMdd").parse(DEFAULT_DATE).toInstant()
			else
				return null
		}
	}
	
	static doubleValidityCheck(String input, boolean mandatory) {
		if (input == "Infinity" || input == "NaN" || input == "" || input == null)
		{
			if (mandatory)
				return 0.0
			else
				return null
		}
		else
			return input
	}
}

static dateParse(String format, String input, TimeZone zone){
    def sdf = new SimpleDateFormat(format)
    sdf.setTimeZone(zone)
    def datetime = sdf.parse(input)
    return datetime
}


def getDateTime(String dateinput, String timeinput, boolean mandatory) {
    def datepart = getDate(dateinput, mandatory)
    if (datepart == null)
        return null
    try {
        // Process timeinput into timestr
        zoneId = ZoneId.of(TIMEZONE_CITY)
        def timestr = (timeinput != null && timeinput != "") ? String.format("%.0f", Double.parseDouble(timeinput)) : "000000"
        if (timeinput == "" || timeinput == null || timeinput == "0")
            timestr = "000000"
        timestr = timestr.padLeft(6, '0')
        if (timestr.length() > 6)
            timestr = timestr.substring(0, 6)

        // Combine date and time strings and parse to LocalDateTime
        def dateString = datepart.format(DateTimeFormatter.ofPattern("yyyyMMdd"))
        def dateTimeStr = dateString + timestr

        // Parse into LocalDateTime and convert to Instant
        def formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss")
        def dateTime = LocalDateTime.parse(dateTimeStr, formatter)
        return dateTime.atZone(zoneId).toInstant()
    } catch (Exception ex) {
        // Handle exception and return default date as Instant
        def defaultDate = LocalDate.parse(DEFAULT_DATE, DateTimeFormatter.ofPattern("yyyyMMdd"))
        return defaultDate.atStartOfDay(zoneId).toInstant()
    }
}

def getExPlatformId(String source) {
    def sourcesplit = source.tokenize('.')
    if (sourcesplit.size() < 3)
        return ""
    else
        return sourcesplit[2]
}

def getOrderStatus(String status) {
    switch (status) {
        case "Ac":
            return "Accepted"
            break
        case "Zf":
            return "Zero fill"
            break
        case "De":
            return "Deleted"
            break
        case "Cf":
            return "Filled"
            break
        case "Pf":
            return "PartiallyFilled"
            break
        case "Sus":
            return "Suspended"
            break
        case "Rej":
            return "Rejected"
            break
        default:
            return UNKNOWN
    }
    return UNKNOWN
}

def getOrderType(String type) {
    switch (type) {
        case ["Market", "Limit", "Stop", "StopLimit", "MarketOnClose", "WithOrWithout", "LimitOrBetter", "LimitWithOrWithout", "OnBasis", "LimitOnClose", "PreviouslyIndicated", "PreviouslyQuoted", "ForexLimit", "ForexSwap", "Funari", "Pegged"]:
            return type
            break
        case "OnClose":
            return "On Close"
            break
        case "ForexMarket":
            return "Forex Market"
            break
        case "ForexPreviouslyQuoted":
            return "Forex Previously Quoted"
            break
        case "MarketLimit":
            return "Market Limit"
            break
        case "OCO":
            return "One Cancels the Other Order"
            break
        case "BestLimit":
            return "Best Limit"
            break
        case "StopMarketLimit":
            return "Stop Market Limit"
            break
        default:
            return UNKNOWN
    }
    return UNKNOWN
}

def getOrderValidityPeriod(String timeinforce) {
    switch (timeinforce) {
        case "DAY":
            return "Day"
            break
        case "GFD":
            return "Good for day"
            break
        case "GTC":
            return "GoodTillCancel"
            break
        case "OPG":
            return "AtTheOpening"
            break
        case "IOC":
            return "ImmediateOrCancel"
            break
        case "FOK":
            return "FillOrKill"
            break
        case "GTX":
            return "GoodThroughCrossing"
            break
        case "GTD":
            return "GoodTillDate"
            break
        case ["GFA", "GTA"]:
            return "GoodForAuction"
            break
        case "OC":
            return "AtTheClose"
            break
        case "GFT":
            return "GoodForTime"
            break
        case "OPG_GTC":
            return "At the opening good till cancel"
            break
        case "OPG_GTD":
            return "At the opening good till date"
            break
        case "OPG_FAK":
            return "At the opening fill and kill"
            break
        case "FAK":
            return "Fill and Kill"
            break
        case "FAS":
            return "Fill and store"
            break
        case "STO":
            return "Storable"
            break
        case "GIS":
            return "Good in Session"
            break
        case "GTT":
            return "Good Till Time"
            break
        case "Passive":
            return "Passive"
            break
        case "Aggressive":
            return "Aggressive"
            break
        case "AON_Passive":
            return "All or nothing Passive"
            break
        case "AON_Aggressive":
            return "All or nothing Aggressive"
            break
        default:
            return UNKNOWN
    }
    return UNKNOWN
}

[
        header  : [
                messageId                    : row.header.messageId,
                businessId                   : row.header.businessId,
                //batchId: null,
                sourceSystem                 : row.header.sourceSystem,
                //secondarySourceSystem: row.header.secondarySourceSystem,
                sourceSystemCreationTimestamp: row.header.sourceSystemCreationTimestamp,
                sentBy                       : row.header.sourceSystem,
                sentTo                       : row.header.sentTo,
                messageType                  : MESSAGE_TYPE_ORDER,
                schemaVersion                : SCHEMA_VERSION,
                processing                   : row.header.processing,
                //recordOffset: null,
        ],
        mktOrder: [
                orderHeader      : [
                        orderIdentifiers       : [
                                orderId     : [
                                        id: stringNullCheck(row.nativeData.tags.get('Id')),
                                ],
                                venueOrderId: [
                                        id: row.nativeData.tags.get('OrderNo'),
                                ],
                        ],
                        venueInfo              : [
                                executionPlatformId: getExPlatformId(row.header.sentBy),
                        ],
                        persons                : [
                                traderId: stringNullCheck(row.nativeData.tags.get('Trader')),
                        ],
                        regulatory             : [
                                cfiCode: null,
                        ],
                        assetClass             : UNKNOWN,
                        //orderEventType: null,
                        //orderEventDateTime: null,
                        orderSubmissionDateTime: getDateTime(row.nativeData.tags.get('Date'), row.nativeData.tags.get('Time'), false),
                        //orderReceivedDateTime: null,
                        //orderAcceptedDateTime: null,
                        orderExecutedDateTime  : getDateTime(row.nativeData.tags.get('TrdDateLast'), row.nativeData.tags.get('TrdTimeLast'), false),
                        orderValidUntilTime    : getDateTime(row.nativeData.tags.get('ExpireDate'), row.nativeData.tags.get('ExpireTime'), false),
                        //executionVenueDateTime: null,
                        orderStatus            : stringNullCheck(getOrderStatus(row.nativeData.tags.get('StatusStr'))),
                        //orderStatusReason: null,
                        orderStatusDescription : row.nativeData.tags.get('TradingStatusStr'),
                ],
                productIdentifier: [
                        sourceExternalId    : row.nativeData.tags.get('InstrumentId'),
                        sourceExternalIdType: SOURCE_EXTERNAL_ID_TYPE,
                ],
                order            : [
                        currency           : row.nativeData.tags.get('CurrencyStr'),
                        //orderCountry: null,
                        orderType          : getOrderType(row.nativeData.tags.get('TypeStr')),
                        orderValidityPeriod: getOrderValidityPeriod(row.nativeData.tags.get('TimeInForceStr')),
                        //settlementType: null,
                        //strategyId: null,
                        buySell            : row.nativeData.tags.get('VerbStr'),
                        //isSolicited: null,
                        //isAnonymous: null,
                        limitPrice         : row.nativeData.tags.get('Value'),
                        limitPriceType     : row.nativeData.tags.get('ValueTypeStr'),
                        stopPrice          : row.nativeData.tags.get('StopPrice'),
                        //maxFloor: null,
                        //lastTradedPrice: null,
                ],
                fillInfo         : [
                        //execId
                        fillPrice     : doubleValidityCheck(row.nativeData.tags.get('TrdValueLast'), true),
                        fillQuantity  : doubleValidityCheck(row.nativeData.tags.get('TrdQtyLast'), true),
                        //maxVol
                        //minQty
                        quantityType  : QUANTITY_TYPE,
                        cumulativeSize: doubleValidityCheck(row.nativeData.tags.get('QtyFill'), false),
                        size          : doubleValidityCheck(row.nativeData.tags.get('QtyGoal'), false),
                        fillStatus    : row.nativeData.tags.get('QtyStatusStr'),
                ],
                book             : [
                        //bookingPoint: null,
                        bookId: null,
                        //bookDescription: null,
                        //scotiaLegalEntityId: null,
                        //transitNumber: null,
                        //transitDescription: null,
                        //legalEntityName: null,
                ],
        ],
]