import static Constants.*

class Constants {
	static final UNKNOWN = "Unknown"
	static final SCHEMA_VERSION = "1.97"
	static final DEFAULT_DATE = "19000101"
	static final SOURCE_INTERNAL_ID_TYPE = "MMI_ID"
	static final MESSAGE_TYPE_TRADE = "Trade"
	static final PRICE_TYPE = "PerUnit"
	static final UNIT_OF_MEASURE = "Unit"
	static final TIMEZONE_CITY = "Asia/Singapore"
	static final TRADE_STATUS_LIVE = "LIVE"
	static final TRADE_STATUS_DEAD = "DEAD"
	static final SOURCE_EXTERNAL_ID_TYPE = "Ticker"
	static final ASSET_CLASS = "Cash-FixedIncome"
	static final COMMISSION_AMOUNT = "CommissionAmount"
	static final COMMISSION_TOTAL = "CommissionTotal"
	static final EXCH_CODE_TYPE = "MIC"

	static stringNullCheck(String input) {
		if (input == "" || input == null)
			return UNKNOWN
		else
			return input
	}

	static getDate(String input, boolean mandatory) {
		try {
			return Date.parse("yyyyMMdd", input)
		} catch(Exception ex) {
			if (mandatory)
				return Date.parse("yyyyMMdd", DEFAULT_DATE)
			else
				return null
		}
	}
		
	static doubleValidityCheck(String input, boolean mandatory) {
		if (input == "Infinity" || input == "NaN" || input == "" || input == null)
		{
			if (mandatory)
				return 0.0
			else
				return null
		}
		else
			return input
	}
	
	static OneTwoToTrueFalse(String input) {
		if (input == "Yes" || input == "1")
			return "true"
		else if (input == "No" || input == "2")
			return "false"
		else
			return null
	}
	
	static OneZeroToTrueFalse(String input) {
		if (input == "Yes" || input == "1")
			return "true"
		else if (input == "No" || input == "0")
			return "false"
		else
			return null
	}
}

def getDateTime(String dateinput, String timeinput, boolean mandatory) {
	def datepart = getDate(dateinput, mandatory)
	if (datepart == null)
		return null
	try {
		def timestr = String.format("%.0f", Double.parseDouble(timeinput))
		if (timeinput == "" || timeinput == null || timeinput == "0")
			timestr = "000000"
		def timelength = timestr.length()
		for (i = 0; i < (6 - timelength); i++)
			timestr = "0" + timestr
		if (timestr.length() > 6)
			timestr = timestr.substring(0,6)
		return Date.parse("yyyyMMdd HHmmss", datepart.format("yyyyMMdd") + " " + timestr, TimeZone.getTimeZone(TIMEZONE_CITY))
	} catch(Exception ex) {
		return Date.parse("yyyyMMdd HHmmss", datepart.format("yyyyMMdd") + " 000000", TimeZone.getTimeZone(TIMEZONE_CITY))
	}
}

def getDateTimeMilliUTC(String dateinput, String timeinput) {
	def datepart = getDate(dateinput, true)
	try {
		def timestr = String.format("%.0f", Double.parseDouble(timeinput))
		if (timeinput == "" || timeinput == null || timeinput == "0")
			timestr = "000000000"
		def timelength = timestr.length()
		for (i = 0; i < (9 - timelength); i++)
			timestr = "0" + timestr
		if (timestr.length() > 9)
			timestr = timestr.substring(0,9)
		return Date.parse("yyyyMMdd HHmmssSSS", datepart.format("yyyyMMdd") + " " + timestr, TimeZone.getTimeZone("UTC"))
	} catch(Exception ex) {
		return Date.parse("yyyyMMdd HHmmssSSS", datepart.format("yyyyMMdd") + " 000000000", TimeZone.getTimeZone("UTC"))
	}
}

def getAssetClass(String typest) {
	if (typest == "Cash" || typest == "Future")
		return ASSET_CLASS
	else
		return UNKNOWN
}

def getIsInternalDeal(String input) {
	if (input == "1")
		return true
	else
		return false
}

def getTradeEvent(String input) {
	switch(input) {
		case "Open":
			return "New"
			break
		case "Updated":
			return "Update"
			break
		case "Cancel":
			return "Cancellation"
			break
		default:
			return UNKNOWN 
	}
	return UNKNOWN
}

def getTradeStatus(String input) {
	if (input == "0")
		return TRADE_STATUS_LIVE
	else
		return TRADE_STATUS_DEAD
}

def getTradeID(String input) {
	def (value1, value2) = stringNullCheck(input).tokenize('.')
	return value1
}

def getUpdateDateTime(String datemod, String timemod, String datecr, String timecr) {
	if (getDate(datemod, false) == null)
		return getDateTime(datecr, timecr, true)
	else
		return getDateTime(datemod, timemod, true)
}

def getLotSize(String qns, String qs) {
	def q = Double.parseDouble(qs)
	if (q == 0)
		return 0
	else
	{
		def qn = Double.parseDouble(qns)
		return qn / q
	}
}

def getSourceIsin(String inst, String isin, String fisin) {
	if (inst == "F")
		return fisin
	else
		return isin
}

def getSourceCusip(String inst, String code) {
	if (inst == "O" || inst == "F")
		return ""
	else
		return code
}

def getMaturityLabel(String dm) {
	def datematurity = getDate(dm, false)
	if (datematurity == "")
		return ""
	else
		return datematurity.format("MMM-yy")
}

def getTradeExecutorType(String input) {
	switch(input) {
		case "0":
			return "Undef"
			break
		case "1":
			return "Person"
			break
		case "2":
			return "Algorithm"
			break
		case "3":
			return "LegalEntity"
			break
		default:
			return null 
	}
	return null
}

def GetWaiverIndicator(String input) {
	switch(input) {
		case ["LRGS", "RFPT", "NLIQ", "OILQ", "PRIC", "SIZE", "ILQD"]:
			return input
			break
	default:
			return null
	}
	return null
}

def GetStrikeUnit(String input) {
	switch(input) {
		case "Price":
			return "Percentage"
			break
		case "Yield":
			return "Yield"
			break
		default:
			return null 
	}
	return null
}

def getExDateTime(String dateinput, String timeinput) {
	result = [
			millisTimestamp: getDateTimeMilliUTC(dateinput, timeinput)
		]
	return result
}

def getMMIFields() {
	def mts = null
	try {
		mts = getDateTime(row.nativeData.tags.get('DateStatusChange'), row.nativeData.tags.get('TimeStatusChange'), true)
	} catch(Exception ex) {
		mts = getDateTime(DEFAULT_DATE, "000000", true)
	} finally {
		def result = [
			tradeStatusChangeTime: [
				millisTimestamp: mts,
			],
			isSolicited: OneZeroToTrueFalse(row.nativeData.tags.get('SolicitedFlag')),
			marketRefId: row.nativeData.tags.get('SynthRef'),
			sourceTradeStatus: row.nativeData.tags.get('TradeStatusStr'),
			sourceTradeSubStatus: row.nativeData.tags.get('TradeSubStatusStr'),
			retailAdpCode: row.nativeData.tags.get('RetailADPCode'),
			isRetail: OneZeroToTrueFalse(row.nativeData.tags.get('RetailFlag')),
			checkedbySalesUserId: row.nativeData.tags.get('SalesCheckedUserId'),
			blotterCode: row.nativeData.tags.get('ADPBlotterCode'),
			stpToAdpStatus: row.nativeData.tags.get('ADPSTPStatus'),
			adpTrailerCode: row.nativeData.tags.get('ADPTrailerCode'),
			impactStatusMessage: row.nativeData.tags.get('IMPACTStatusMessage'),
			//tradeStatusChangeBusinessDate: getDate(row.nativeData.tags.get('DateStatusChange'), false),
			tradeStatusChangeCalendarDate: getDate(row.nativeData.tags.get('DateStatusChangeStamp'), false),
			legIdentifier: row.nativeData.tags.get('LegNo'),
			isCancelled: OneZeroToTrueFalse(row.nativeData.tags.get('CancelledFlag')),
			additionaInformationText: row.nativeData.tags.get('FreeText'),
			tradeExecutorType: getTradeExecutorType(row.nativeData.tags.get('ExecutionMktIdType')),
			settlementDayDifference: doubleValidityCheck(row.nativeData.tags.get('SettlDays'), false),
			tradePackageDescription: row.nativeData.tags.get('DealType'),
			//isMarketMaker
			refAssetName: stringNullCheck(row.nativeData.tags.get('RefAssetName')),
			refAssetAccountingMethod: stringNullCheck(row.nativeData.tags.get('RefAssetAcctMethod')),
		]
		return result
	}
}

def getProduct(String inst) {
	def result
	switch(inst) {
		case "F":
			result = [
				assetInfo: [
					assetType: row.nativeData.tags.get('InsTypeStr'),
					assetSubType: row.nativeData.tags.get('InsSubType'),
				],
				tradeSplitInfo: [
					isTradeSplitAllocation: OneTwoToTrueFalse(row.nativeData.tags.get('TradeSplitFlagStr')),
					tradeSplitId: row.nativeData.tags.get('TradeSplitId'),
					tradeSplitPercentage: doubleValidityCheck(row.nativeData.tags.get('TradeSplitPct'), false),
					tradeSplitStatus: row.nativeData.tags.get('TradeSplitStatusStr'),
				],
				settlementInfo: [
					settlementExcludingAccrualCommissionAmount: doubleValidityCheck(row.nativeData.tags.get('PrincipalMoney'), false),
					settlementIncludingAccrualCommissionAmount: doubleValidityCheck(row.nativeData.tags.get('TotalMoney'), false),
					fxRateSettlement: doubleValidityCheck(row.nativeData.tags.get('FXRate'), false),
					isFXRateInverted: OneTwoToTrueFalse(row.nativeData.tags.get('FXRateInverted')),
					isCrossCurrencySettled: OneTwoToTrueFalse(row.nativeData.tags.get('IsCrossCurrencySettl')),
				],
				quote: [
					price: doubleValidityCheck(row.nativeData.tags.get('Price'), true),
					priceCurrency: stringNullCheck(row.nativeData.tags.get('S0_CurrencyStr')),
					priceNotation: UNKNOWN,
					priceType: PRICE_TYPE,
					unitOfMeasure: UNIT_OF_MEASURE,
				],
				events: [
					maturityLabel: getMaturityLabel(row.nativeData.tags.get('DateMaturity')),
					tradeExpiryDate: getDate(row.nativeData.tags.get('DateMaturity'), false),
				],
				buySell: row.nativeData.tags.get('VerbStr'),
				notionalCurrency: stringNullCheck(row.nativeData.tags.get('S0_CurrencyStr')),
				notional: doubleValidityCheck(row.nativeData.tags.get('QtyNominal'), true),
				lotSize: getLotSize(doubleValidityCheck(row.nativeData.tags.get('QtyNominal'), true), doubleValidityCheck(row.nativeData.tags.get('Qty'), true)),
				quantity: doubleValidityCheck(row.nativeData.tags.get('Qty'), true),
				quantityUnit: stringNullCheck(row.nativeData.tags.get('QtyNominal')),
				venueContractCode: row.nativeData.tags.get('Code'),
				venueContractName: row.nativeData.tags.get('Desc'),
				counterpartyAccountId: row.nativeData.tags.get('CPAccountId'),
				isOrderAggressed: OneTwoToTrueFalse(row.nativeData.tags.get('AggressedStr')),
				isSourceExternal: OneTwoToTrueFalse(row.nativeData.tags.get('ExternalStr')),
			]
			break
		case "O":
			result = [
				assetInfo: [
					assetType: row.nativeData.tags.get('InsTypeStr'),
					assetSubType: row.nativeData.tags.get('InsSubType'),
				],
				tradeSplitInfo: [
					isTradeSplitAllocation: OneTwoToTrueFalse(row.nativeData.tags.get('TradeSplitFlagStr')),
					tradeSplitId: row.nativeData.tags.get('TradeSplitId'),
					tradeSplitPercentage: doubleValidityCheck(row.nativeData.tags.get('TradeSplitPct'), false),
					tradeSplitStatus: row.nativeData.tags.get('TradeSplitStatusStr'),
				],
				settlementInfo: [
					settlementExcludingAccrualCommissionAmount: doubleValidityCheck(row.nativeData.tags.get('PrincipalMoney'), false),
					settlementIncludingAccrualCommissionAmount: doubleValidityCheck(row.nativeData.tags.get('TotalMoney'), false),
					fxRateSettlement: doubleValidityCheck(row.nativeData.tags.get('FXRate'), false),
					isFXRateInverted: OneTwoToTrueFalse(row.nativeData.tags.get('FXRateInverted')),
					isCrossCurrencySettled: OneTwoToTrueFalse(row.nativeData.tags.get('IsCrossCurrencySettl')),
				],
				underlyer: [
					productIdentifier: [
						sourceInternalId: row.nativeData.tags.get('UnderlyingId'),
						sourceInternalIdType: SOURCE_INTERNAL_ID_TYPE,
						sourceExternalId: row.nativeData.tags.get('UnderlyingCode'),
						sourceExternalIdType: SOURCE_EXTERNAL_ID_TYPE,
					],
				],
				optionExercise: [
					optionExerciseStyle: row.nativeData.tags.get('OptionStyle'),
				],
				buySell: stringNullCheck(row.nativeData.tags.get('VerbStr')),
				quantity: doubleValidityCheck(row.nativeData.tags.get('Qty'), true),
				notional: doubleValidityCheck(row.nativeData.tags.get('QtyNominal'), true),
				expiryDate: getDate(row.nativeData.tags.get('DateOptionExpire'), true),
				optionType: row.nativeData.tags.get('OptionTypeStr'),
				price: doubleValidityCheck(row.nativeData.tags.get('Price'), false),
				priceCurrency: row.nativeData.tags.get('S0_CurrencyStr'),
				strikePrice: doubleValidityCheck(row.nativeData.tags.get('PriceStrike'), false),
				venueContractCode: row.nativeData.tags.get('Code'),
				venueContractName: row.nativeData.tags.get('Desc'),
				strikeUnit: GetStrikeUnit(row.nativeData.tags.get('StrikeTypeStr')),
				counterpartyAccountId: row.nativeData.tags.get('CPAccountId'),
				isOrderAggressed: OneTwoToTrueFalse(row.nativeData.tags.get('AggressedStr')),
				isSourceExternal: OneTwoToTrueFalse(row.nativeData.tags.get('ExternalStr')),
			]
			break
		default:
			result = [
				assetInfo: [
					assetType: row.nativeData.tags.get('InsTypeStr'),
					assetSubType: row.nativeData.tags.get('InsSubType'),
				],
				tradeSplitInfo: [
					isTradeSplitAllocation: OneTwoToTrueFalse(row.nativeData.tags.get('TradeSplitFlagStr')),
					tradeSplitId: row.nativeData.tags.get('TradeSplitId'),
					tradeSplitPercentage: doubleValidityCheck(row.nativeData.tags.get('TradeSplitPct'), false),
					tradeSplitStatus: row.nativeData.tags.get('TradeSplitStatusStr'),
				],
				settlementInfo: [
					settlementExcludingAccrualCommissionAmount: doubleValidityCheck(row.nativeData.tags.get('PrincipalMoney'), false),
					settlementIncludingAccrualCommissionAmount: doubleValidityCheck(row.nativeData.tags.get('TotalMoney'), false),
					fxRateSettlement: doubleValidityCheck(row.nativeData.tags.get('FXRate'), false),
					isFXRateInverted: OneTwoToTrueFalse(row.nativeData.tags.get('FXRateInverted')),
					isCrossCurrencySettled: OneTwoToTrueFalse(row.nativeData.tags.get('IsCrossCurrencySettl')),
				],
				buySell: stringNullCheck(row.nativeData.tags.get('VerbStr')),
				quantity: doubleValidityCheck(row.nativeData.tags.get('Qty'), false),
				dirtyPrice: doubleValidityCheck(row.nativeData.tags.get('PriceDirty'), false),
				dirtyPriceCurrency: row.nativeData.tags.get('S0_CurrencyStr'),
				notional: doubleValidityCheck(row.nativeData.tags.get('QtyNominal'), true),
				price: doubleValidityCheck(row.nativeData.tags.get('Price'), false),
				priceCurrency: row.nativeData.tags.get('S0_CurrencyStr'),
				yield: row.nativeData.tags.get('Yield'),
				accruedInterest: doubleValidityCheck(row.nativeData.tags.get('AccruedAmount'), false),
				accrualType: row.nativeData.tags.get('AccruedGivenStr'),
				cleanPrice: doubleValidityCheck(row.nativeData.tags.get('PriceClean'), false),
				counterpartyAccountId: row.nativeData.tags.get('CPAccountId'),
				couponRate: doubleValidityCheck(row.nativeData.tags.get('CouponInterest'), false),
				discountRate: doubleValidityCheck(row.nativeData.tags.get('Discount'), false),
				inflationIndexFactor: doubleValidityCheck(row.nativeData.tags.get('IndexFactor'), false),
				isOrderAggressed: OneTwoToTrueFalse(row.nativeData.tags.get('AggressedStr')),
				isSourceExternal: OneTwoToTrueFalse(row.nativeData.tags.get('ExternalStr')),
				isWhenIssued: OneTwoToTrueFalse(row.nativeData.tags.get('WIStr')),
				overridenAccruedInterest: doubleValidityCheck(row.nativeData.tags.get('AccruedValue'), false),
				quantityfactor: doubleValidityCheck(row.nativeData.tags.get('Factor'), false),
				// GFI-5540 - Asia Data Publication on GBM DP: ANY.CM_TRADE.TRADESERVER_ASIA.TRADECAPTURE
				// Needs to be populated for Bonds ONLY:
				maturityDate: getDate(row.nativeData.tags.get('DateMaturity'), false),
			]
			break
	}
	return result
}

[
	header: [
		messageId: row.header.messageId,
		businessId: row.header.businessId,
		//batchId: null,
		sourceSystem: row.header.sourceSystem,
		//secondarySourceSystem: row.header.secondarySourceSystem,
		sourceSystemCreationTimestamp: row.header.sourceSystemCreationTimestamp,
		sentBy: row.header.sourceSystem,
		sentTo: row.header.sentTo,
		messageType: MESSAGE_TYPE_TRADE,
		schemaVersion: SCHEMA_VERSION,
		processing: row.header.processing,
		//recordOffset: null,
	],
	trade: [
		tradeHeader: [
			tradeIdentifiers: [
				tradeId: [
					id: getTradeID(row.nativeData.tags.get('Id')),
					version: row.nativeData.tags.get('RevId'),
				],
				//previousTradeId: null,
				originatingTradeId: [
					id: stringNullCheck(row.nativeData.tags.get('TradeId')),
				],
				originatingOrderId: row.nativeData.tags.get('OrderId'),
				//originatingParentOrderId: null,
				originatingQuoteId: row.nativeData.tags.get('QuoteId'),
				//originatingParentQuoteId: null,
				venueTransactionId: row.nativeData.tags.get('TVTransactionIdCode'),
				//uniqueSwapId: null,
				//uniqueTransactionId: null,
				tradePackageId: row.nativeData.tags.get('DealId'),
				//tradePackageSize: null,
				//internalReverseTradeId: null,
				//tradeName: null,
				//execId: null,
			],
			sourceSystemProductId: [
				sourceInternalId: row.nativeData.tags.get('InstrumentId'),
				sourceInternalIdType: SOURCE_INTERNAL_ID_TYPE,
				sourceExternalId: row.nativeData.tags.get('ExternalId1'),
				sourceExternalIdType: row.nativeData.tags.get('ExternalIdSrc1'),
				sourceInstrumentName: row.nativeData.tags.get('Desc'),
				sourceInstrumentCategory: row.nativeData.tags.get('SecurityTypeStr'),
				sourceIsin: getSourceIsin(row.nativeData.tags.get('InsType'), row.nativeData.tags.get('Isin'), row.nativeData.tags.get('FutureIsin')),
				sourceCusip: getSourceCusip(row.nativeData.tags.get('InsType'), row.nativeData.tags.get('Code')),
				//sourceSedol1: null,
				//sourceSedol2: null,
				//primaryMarketId: row.nativeData.tags.get('Ticker'),
				//primaryMarketIdType: "Ticker",
			],
			venueInfo: [
				//executionVenueType: EXECUTION_VENUE_TYPE,
				executionPlatformId: row.nativeData.tags.get('OriginalSource'),
				exchangeCode: row.nativeData.tags.get('MIC'),
				exchangeCodeType: EXCH_CODE_TYPE,
			],
			persons: [
				//tradeExecutorId: row.nativeData.tags.get('CreateUserId'),
				//tradeExecutorName: null,
				traderId: row.nativeData.tags.get('Trader'),
				//traderName: row.nativeData.tags.get('OriginalCreateUserId'),
				//traderLocation: null,
				tradeCreatorId: row.nativeData.tags.get('CreateUserId'),
				originatingTradeCreatorId: row.nativeData.tags.get('OriginalCreateUserId'),
				salesPersonId: row.nativeData.tags.get('SalesRepId'),
				salesPersonName: row.nativeData.tags.get('SalesPerson'),
				//salesPersonLocation: null,
				//algorithmId: null,
				//algorithmName: null,
				//algorithmLocation: null,
			],
			settlement: [
				//settlementType: row.nativeData.tags.get('TypeStr'),
				//isClearingEligible: null,
				//isNetted: null,
				settlementDate: getDate(row.nativeData.tags.get('DateSettl'), false),
				//settlementAccountName: null,
				settlementAmount: doubleValidityCheck(row.nativeData.tags.get('SettlTradeNetMoney'), false),
				settlementAmountCurrency: row.nativeData.tags.get('CurrencyStr'),
			],
			regulatory: [
				mifidWaiverIndicators: [
					[
						GetWaiverIndicator(row.nativeData.tags.get('WaiverFlag')),
					],
				],
				mifidExecutionWithinFirm: row.nativeData.tags.get('ExecutionMktId'),
				mifidInvestmentDecisionWithinFirm: row.nativeData.tags.get('InvestmentMktId'),
				mifidOtherDetails: "MiFID2Exemption: " + row.nativeData.tags.get('MiFID2Exemption'),
			],
			executionDateTime: getExDateTime(row.nativeData.tags.get('DateMktCreationUTC'), row.nativeData.tags.get('TimeMktCreationUTC')),
			//scotiaUPI: null,
			assetClass: getAssetClass(row.nativeData.tags.get('TypeStr')),
			//algoProductCategory: null,
			isInternalDeal: getIsInternalDeal(row.nativeData.tags.get('ScotiaInternal')),
			isTradingBook: true,
			tradeDate: getDate(row.nativeData.tags.get('DateTrade'), true),
			entryDateTime: getDateTime(row.nativeData.tags.get('DateOriginalCreateStamp'), row.nativeData.tags.get('TimeOriginalCreate'), true),
			//originalExecutionDateTime:
			tradeUpdateDateTime: getUpdateDateTime(row.nativeData.tags.get('DateModified'), row.nativeData.tags.get('TimeModified'), row.nativeData.tags.get('DateCreateStamp'), row.nativeData.tags.get('TimeCreate')),
			tradeStatus: getTradeStatus(row.nativeData.tags.get('Status')),
			tradeEvent: getTradeEvent(row.nativeData.tags.get('TradeSubStatusStr')),
			//tradeSubEvent: null,
			csaEligible: true,
			//isPartOfPortfolioCompression: null,
		],
		book: [
			//bookingPoint: UNKNOWN,
			bookId: row.nativeData.tags.get('BookId'),
			//bookDescription: UNKNOWN,
			scotiaLegalEntityId: row.nativeData.tags.get('LegalEntity'),
			//transitNumber: UNKNOWN,
			//transitDescription: UNKNOWN,
			//legalEntityName: UNKNOWN,
		],
		parties: [
			counterparty: [
				partyId: row.nativeData.tags.get('CPKey'),
				//partyIdType: UNKNOWN,
				partyName: row.nativeData.tags.get('CPName'),
				//partyLei: UNKNOWN,
				//cardsId: UNKNOWN,
				//ccdId: UNKNOWN,
			],
			originalCounterparty: [
				//partyId: UNKNOWN,
				//partyIdType: UNKNOWN,
				//partyName: UNKNOWN,
				partyShortName: row.nativeData.tags.get('CPShortName'),
				//partyLei: null,
				//cardsId: UNKNOWN,
				//ccdId: UNKNOWN,
			],
			executingBroker: [
				partyId: row.nativeData.tags.get('OriginalSource'),
			],
			clearingParty: [
				partyId: row.nativeData.tags.get('ClearingHouse'),
			],
			//client: null,
			//cardsId: null,
			//ccdId: null,
			//executingParty: null,
			//orderOriginatingParty: null,
			//triPartyAgent: null,
		],
		costsAndCharges: [
			commissionRates: [
				[
					type: COMMISSION_TOTAL,
					amount: doubleValidityCheck(row.nativeData.tags.get('CommissionTotal'), true),
				],
				[
					type: COMMISSION_AMOUNT,
					amount: doubleValidityCheck(row.nativeData.tags.get('CommissionAmount'), true),
				],
			],
		],
		sourceSystemSpecific: [
			mmi: getMMIFields(),
		],
		//clearingInstructions: null,
		//endOfDayPosition: null,
		product: getProduct(row.nativeData.tags.get('InsType')),
	],
]