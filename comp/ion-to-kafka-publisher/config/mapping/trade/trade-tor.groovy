import java.time.*
import java.time.format.DateTimeFormatter

/**
 * Constants class containing all static values and utility methods.
 * This class provides centralized access to common values and data conversion functions.
 */
class Constants {
	// Basic string constants
	static final String UNKNOWN = "Unknown"
	static final String SCHEMA_VERSION = "1.144"
	static final String DEFAULT_DATE = "19000101"
	static final String SOURCE_INTERNAL_ID_TYPE = "MMI_ID"
	static final String MESSAGE_TYPE_TRADE = "Trade"

	// Trade-specific constants
	static final String PRICE_TYPE = "PerUnit"
	static final String UNIT_OF_MEASURE = "Unit"
	static final String TIMEZONE_CITY = "America/Toronto"
	static final String TRADE_STATUS_LIVE = "LIVE"
	static final String TRADE_STATUS_DEAD = "DEAD"
	static final String SOURCE_EXTERNAL_ID_TYPE = "Ticker"
	static final String ASSET_CLASS = "Cash-FixedIncome"

	// Commission types
	static final String COMMISSION_AMOUNT = "CommissionAmount"
	static final String COMMISSION_TOTAL = "CommissionTotal"
	static final String EXCH_CODE_TYPE = "MIC"

	/**
	 * Validates and normalizes string input
	 * @param input String to check
	 * @return UNKNOWN if input is null or empty, input string otherwise
	 */
	static String stringNullCheck(String input) {
		return (input == null || input.isEmpty()) ? UNKNOWN : input
	}

	/**
	 * Converts string date to LocalDate
	 * @param input Date string in yyyyMMdd format
	 * @param mandatory If true, returns default date on failure; if false, returns null
	 * @return LocalDate object or null
	 */
	static LocalDate getDate(String input, boolean mandatory) {
		try {
			return LocalDate.parse(input, DateTimeFormatter.ofPattern("yyyyMMdd"))
		} catch(Exception ex) {
			return mandatory ? LocalDate.parse(DEFAULT_DATE, DateTimeFormatter.ofPattern("yyyyMMdd")) : null
		}
	}

	/**
	 * Validates and converts string to numeric value
	 * @param input String to convert
	 * @param mandatory If true, returns "0.0" on failure; if false, returns null
	 * @return String representation of number or null
	 */
	static String doubleValidityCheck(String input, boolean mandatory) {
		if (input == null || input.isEmpty() || input == "Infinity" || input == "NaN") {
			return mandatory ? "0.0" : null
		}
		try {
			double val = Double.parseDouble(input)
			return String.valueOf(val)
		} catch (Exception ex) {
			return mandatory ? "0.0" : null
		}
	}

	/**
	 * Converts Yes/1/No/2 to string boolean
	 */
	static String OneTwoToTrueFalse(String input) {
		if (input == null) return null
		switch(input) {
			case ["Yes", "1"]: return "true"
			case ["No", "2"]: return "false"
			default: return null
		}
	}

	/**
	 * Converts Yes/1/No/0 to string boolean
	 */
	static String OneZeroToTrueFalse(String input) {
		if (input == null) return null
		switch(input) {
			case ["Yes", "1"]: return "true"
			case ["No", "0"]: return "false"
			default: return null
		}
	}

	/**
	 * Formats time string to standard 6-digit format
	 */
	static String formatTimeString(String timeinput) {
		if (timeinput == null || timeinput.isEmpty() || timeinput == "0") {
			return "000000"
		}
		String timestr = String.format("%.0f", Double.parseDouble(timeinput))
		while (timestr.length() < 6) {
			timestr = "0" + timestr
		}
		return timestr.length() > 6 ? timestr.substring(0, 6) : timestr
	}

	/**
	 * Converts date and time strings to millisecond timestamp
	 */
	static Long getDateTime(String dateinput, String timeinput, boolean mandatory) {
		LocalDate datepart = getDate(dateinput, mandatory)
		if (datepart == null) return null

		try {
			String timestr = formatTimeString(timeinput)
			LocalTime timePart = LocalTime.parse(timestr, DateTimeFormatter.ofPattern("HHmmss"))
			return ZonedDateTime.of(datepart, timePart, ZoneId.of(TIMEZONE_CITY))
					.toInstant()
					.toEpochMilli()
		} catch(Exception ex) {
			if (datepart != null) {
				return ZonedDateTime.of(datepart, LocalTime.MIDNIGHT, ZoneId.of(TIMEZONE_CITY))
						.toInstant()
						.toEpochMilli()
			}
			return null
		}
	}

	/**
	 * Converts date and time strings to UTC Instant
	 */
	static Instant getDateTimeMilliUTC(String dateinput, String timeinput) {
		LocalDate datepart = getDate(dateinput, true)
		try {
			String timestr = formatTimeString(timeinput)
			LocalTime timePart = LocalTime.parse(timestr.substring(0,6), DateTimeFormatter.ofPattern("HHmmss"))
			return ZonedDateTime.of(datepart, timePart, ZoneId.of("UTC")).toInstant()
		} catch(Exception ex) {
			return ZonedDateTime.of(datepart, LocalTime.MIDNIGHT, ZoneId.of("UTC")).toInstant()
		}
	}

	/**
	 * Determines asset class based on type
	 */
	static String getAssetClass(String typest) {
		if (typest == "Cash" || typest == "Future") return ASSET_CLASS
		return UNKNOWN
	}

	/**
	 * Converts internal deal flag
	 */
	static boolean getIsInternalDeal(String input) {
		return input == "1"
	}

	/**
	 * Maps trade event types
	 */
	static String getTradeEvent(String input) {
		switch(input) {
			case "Open": return "New"
			case "Updated": return "Update"
			case "Cancel": return "Cancellation"
			default: return UNKNOWN
		}
	}

	/**
	 * Converts trade status code
	 */
	static String getTradeStatus(String input) {
		return input == "0" ? TRADE_STATUS_LIVE : TRADE_STATUS_DEAD
	}

	/**
	 * Extracts trade ID from compound identifier
	 */
	static String getTradeID(String input) {
		def (value1, value2) = stringNullCheck(input).tokenize('.')
		return value1
	}

	/**
	 * Gets appropriate update datetime
	 */
	static Long getUpdateDateTime(String datemod, String timemod, String datecr, String timecr) {
		if (getDate(datemod, false) == null) {
			return getDateTime(datecr, timecr, true)
		}
		return getDateTime(datemod, timemod, true)
	}

	/**
	 * Calculates lot size
	 */
	static Double getLotSize(String qns, String qs) {
		double q = Double.parseDouble(qs)
		if (q == 0) return 0.0
		double qn = Double.parseDouble(qns)
		return qn / q
	}

	/**
	 * Gets appropriate ISIN
	 */
	static String getSourceIsin(String inst, String isin, String fisin) {
		return inst == "F" ? fisin : isin
	}

	/**
	 * Gets CUSIP based on instrument type
	 */
	static String getSourceCusip(String inst, String code) {
		return (inst == "O" || inst == "F") ? "" : code
	}

	/**
	 * Formats maturity date label
	 */
	static String getMaturityLabel(String dm) {
		LocalDate datematurity = getDate(dm, false)
		return datematurity == null ? "" : datematurity.format(DateTimeFormatter.ofPattern("MMM-yy"))
	}

	/**
	 * Maps trade executor types
	 */
	static String getTradeExecutorType(String input) {
		switch(input) {
			case "0": return "Undef"
			case "1": return "Person"
			case "2": return "Algorithm"
			case "3": return "LegalEntity"
			default: return null
		}
	}

	/**
	 * Creates waiver indicator list
	 */
	static List<String> GetWaiverIndicator(String input) {
		return ["LRGS", "RFPT", "NLIQ", "OILQ", "PRIC", "SIZE", "ILQD"].contains(input) ?
				[input] : null
	}

	/**
	 * Maps strike type to unit
	 */
	static String GetStrikeUnit(String input) {
		switch(input) {
			case "Price": return "Percentage"
			case "Yield": return "Yield"
			default: return null
		}
	}

	/**
	 * Creates execution datetime structure
	 */
	static Map getExDateTime(String dateinput, String timeinput) {
		return [millisTimestamp: getDateTimeMilliUTC(dateinput, timeinput)]
	}

	/**
	 * Determines execution venue type
	 */
	static String getExecutionVenueType(String input) {
		return input == "1" ? "ETP" : null
	}
}

/**
 * Builds MMI-specific fields structure
 */
// Create proper MMI fields according to the Avro schema
def getMMIFields() {
	// First, let's create our timestamp data structure
	def timestampNanos = null
	try {
		def millis = Constants.getDateTime(
				row.nativeData.tags.get('DateStatusChange'),
				row.nativeData.tags.get('TimeStatusChange'),
				true
		)
		if (millis != null) {
			timestampNanos = [
					millisTimestamp: millis,
					nanoOfSecond: null
			]
		}
	} catch(Exception ex) {
		def defaultMillis = Constants.getDateTime(Constants.DEFAULT_DATE, "000000", true)
		timestampNanos = [
				millisTimestamp: defaultMillis,
				nanoOfSecond: null
		]
	}

	// We'll create our SystemSpecificDataWrapperMmi structure in a way that matches
	// how Avro expects union types to be structured
	[
			 [
					tradeStatusChangeTime: timestampNanos,
					isSolicited: null,
					marketRefId: null,
					sourceTradeStatus: null,
					sourceTradeSubStatus: null,
					retailAdpCode: null,
					isRetail: null,
					checkedbySalesUserId: null,
					blotterCode: null,
					stpToAdpStatus: null,
					adpTrailerCode: null,
					impactStatusMessage: null,
					tradeStatusChangeBusinessDate: null,
					tradeStatusChangeCalendarDate: null,
					legIdentifier: null,
					isCancelled: null,
					additionaInformationText: null,
					tradeExecutorType: null,
					settlementDayDifference: null,
					tradePackageDescription: null,
					isMarketMaker: null,
					isExecutedUnderPretradeWaiver: null,
					refAssetName: null,
					refAssetAccountingMethod: null
			]
	]
}

/**
 * Builds product structure based on instrument type
 */
def getProduct(String inst) {
	// Common fields for all product types
	def baseFields = [
			assetInfo: [
					assetType: row.nativeData.tags.get('InsTypeStr'),
					assetSubType: row.nativeData.tags.get('InsSubType')
			],
			tradeSplitInfo: [
					isTradeSplitAllocation: Constants.OneTwoToTrueFalse(row.nativeData.tags.get('TradeSplitFlagStr')),
					tradeSplitId: row.nativeData.tags.get('TradeSplitId'),
					tradeSplitPercentage: Constants.doubleValidityCheck(row.nativeData.tags.get('TradeSplitPct'), false),
					tradeSplitStatus: row.nativeData.tags.get('TradeSplitStatusStr')
			],
			settlementInfo: [
					settlementExcludingAccrualCommissionAmount: Constants.doubleValidityCheck(row.nativeData.tags.get('PrincipalMoney'), false),
					settlementIncludingAccrualCommissionAmount: Constants.doubleValidityCheck(row.nativeData.tags.get('TotalMoney'), false),
					fxRateSettlement: Constants.doubleValidityCheck(row.nativeData.tags.get('FXRate'), false),
					isFXRateInverted: Constants.OneTwoToTrueFalse(row.nativeData.tags.get('FXRateInverted')),
					isCrossCurrencySettled: Constants.OneTwoToTrueFalse(row.nativeData.tags.get('IsCrossCurrencySettl'))
			]
	]

	switch(inst) {
		case "F":
			return baseFields + [
					quote: [
							price: Constants.doubleValidityCheck(row.nativeData.tags.get('Price'), true),
							priceCurrency: Constants.stringNullCheck(row.nativeData.tags.get('S0_CurrencyStr')),
							priceNotation: Constants.UNKNOWN,
							priceType: Constants.PRICE_TYPE,
							unitOfMeasure: Constants.UNIT_OF_MEASURE
					],
					events: [
							maturityLabel: Constants.getMaturityLabel(row.nativeData.tags.get('DateMaturity')),
							tradeExpiryDate: Constants.getDate(row.nativeData.tags.get('DateMaturity'), false)
					],
					buySell: row.nativeData.tags.get('VerbStr'),
					notionalCurrency: Constants.stringNullCheck(row.nativeData.tags.get('S0_CurrencyStr')),
					notional: Constants.doubleValidityCheck(row.nativeData.tags.get('QtyNominal'), true),
					lotSize: Constants.getLotSize(
							Constants.doubleValidityCheck(row.nativeData.tags.get('QtyNominal'), true),
							Constants.doubleValidityCheck(row.nativeData.tags.get('Qty'), true)
					),
					quantity: Constants.doubleValidityCheck(row.nativeData.tags.get('Qty'), true),
					quantityUnit: Constants.stringNullCheck(row.nativeData.tags.get('QtyNominal')),
					venueContractCode: Constants.doubleValidityCheck(row.nativeData.tags.get('Code'), false),
					venueContractName: Constants.doubleValidityCheck(row.nativeData.tags.get('Desc'), false),
					counterpartyAccountId: Constants.doubleValidityCheck(row.nativeData.tags.get('CPAccountId'), false),
					isOrderAggressed: Constants.OneTwoToTrueFalse(row.nativeData.tags.get('AggressedStr')),
					isSourceExternal: Constants.OneTwoToTrueFalse(row.nativeData.tags.get('ExternalStr'))
			]
			break

		case "O":
			return baseFields + [
					underlyer: [
							productIdentifier: [
									sourceInternalId: row.nativeData.tags.get('UnderlyingId'),
									sourceInternalIdType: Constants.SOURCE_INTERNAL_ID_TYPE,
									sourceExternalId: row.nativeData.tags.get('UnderlyingCode'),
									sourceExternalIdType: Constants.SOURCE_EXTERNAL_ID_TYPE
							]
					],
					optionExercise: [
							optionExerciseStyle: row.nativeData.tags.get('OptionStyle')
					],
					buySell: Constants.stringNullCheck(row.nativeData.tags.get('VerbStr')),
					quantity: Constants.doubleValidityCheck(row.nativeData.tags.get('Qty'), true),
					notional: Constants.doubleValidityCheck(row.nativeData.tags.get('QtyNominal'), true),
					expiryDate: Constants.getDate(row.nativeData.tags.get('DateOptionExpire'), true),
					optionType: row.nativeData.tags.get('OptionTypeStr'),
					price: Constants.doubleValidityCheck(row.nativeData.tags.get('Price'), false),
					priceCurrency: row.nativeData.tags.get('S0_CurrencyStr'),
					strikePrice: Constants.doubleValidityCheck(row.nativeData.tags.get('PriceStrike'), false),
					venueContractCode: Constants.doubleValidityCheck(row.nativeData.tags.get('Code'), false),
					venueContractName: Constants.doubleValidityCheck(row.nativeData.tags.get('Desc'), false),
					strikeUnit: Constants.GetStrikeUnit(row.nativeData.tags.get('StrikeTypeStr')),
					counterpartyAccountId: Constants.doubleValidityCheck(row.nativeData.tags.get('CPAccountId'), false),
					isOrderAggressed: Constants.OneTwoToTrueFalse(row.nativeData.tags.get('AggressedStr')),
					isSourceExternal: Constants.OneTwoToTrueFalse(row.nativeData.tags.get('ExternalStr'))
			]
			break

		default:
			return baseFields + [
					buySell: Constants.stringNullCheck(row.nativeData.tags.get('VerbStr')),
					quantity: Constants.doubleValidityCheck(row.nativeData.tags.get('Qty'), false),
					dirtyPrice: Constants.doubleValidityCheck(row.nativeData.tags.get('PriceDirty'), false),
					dirtyPriceCurrency: row.nativeData.tags.get('S0_CurrencyStr'),
					notional: Constants.doubleValidityCheck(row.nativeData.tags.get('QtyNominal'), true),
					price: Constants.doubleValidityCheck(row.nativeData.tags.get('Price'), false),
					priceCurrency: row.nativeData.tags.get('S0_CurrencyStr'),
					yield: row.nativeData.tags.get('Yield'),
					accruedInterest: Constants.doubleValidityCheck(row.nativeData.tags.get('AccruedAmount'), false),
					accrualType: row.nativeData.tags.get('AccruedGivenStr'),
					cleanPrice: Constants.doubleValidityCheck(row.nativeData.tags.get('PriceClean'), false),
					counterpartyAccountId: Constants.doubleValidityCheck(row.nativeData.tags.get('CPAccountId'), false),
					couponRate: Constants.doubleValidityCheck(row.nativeData.tags.get('CouponInterest'), false),
					discountRate: Constants.doubleValidityCheck(row.nativeData.tags.get('Discount'), false),
					inflationIndexFactor: Constants.doubleValidityCheck(row.nativeData.tags.get('IndexFactor'), false),
					isOrderAggressed: Constants.OneTwoToTrueFalse(row.nativeData.tags.get('AggressedStr')),
					isSourceExternal: Constants.OneTwoToTrueFalse(row.nativeData.tags.get('ExternalStr')),
					isWhenIssued: Constants.OneTwoToTrueFalse(row.nativeData.tags.get('WIStr')),
					overridenAccruedInterest: Constants.doubleValidityCheck(row.nativeData.tags.get('AccruedValue'), false),
					quantityfactor: Constants.doubleValidityCheck(row.nativeData.tags.get('Factor'), false),
					maturityDate: Constants.getDate(row.nativeData.tags.get('DateMaturity'), false)
			]
	}
}

// Main return structure
[
		header: [
				messageId: row.header.messageId,
				businessId: row.header.businessId,
				sourceSystem: row.header.sourceSystem,
				sourceSystemCreationTimestamp: row.header.sourceSystemCreationTimestamp,
				sentBy: row.header.sourceSystem,
				sentTo: row.header.sentTo,
				messageType: Constants.MESSAGE_TYPE_TRADE,
				schemaVersion: Constants.SCHEMA_VERSION,
				processing: row.header.processing
		],
		trade: [
				tradeHeader: [
						tradeIdentifiers: [
								tradeId: [
										id: Constants.getTradeID(row.nativeData.tags.get('Id')),
										version: row.nativeData.tags.get('RevId')
								],
								originatingTradeId: [
										id: Constants.stringNullCheck(row.nativeData.tags.get('TradeId'))
								],
								originatingOrderId: Constants.doubleValidityCheck(row.nativeData.tags.get('OrderId'), false),
								originatingQuoteId: Constants.doubleValidityCheck(row.nativeData.tags.get('QuoteId'), false),
								venueTransactionId: Constants.doubleValidityCheck(row.nativeData.tags.get('TVTransactionIdCode'), false),
								tradePackageId: row.nativeData.tags.get('DealId')
						],
						sourceSystemProductId: [
								sourceInternalId: row.nativeData.tags.get('InstrumentId'),
								sourceInternalIdType: Constants.SOURCE_INTERNAL_ID_TYPE,
								sourceExternalId: row.nativeData.tags.get('ExternalId1'),
								sourceExternalIdType: row.nativeData.tags.get('ExternalIdSrc1'),
								sourceInstrumentName: row.nativeData.tags.get('Desc'),
								sourceInstrumentCategory: row.nativeData.tags.get('SecurityTypeStr'),
								sourceIsin: Constants.getSourceIsin(row.nativeData.tags.get('InsType'),
										row.nativeData.tags.get('Isin'),
										row.nativeData.tags.get('FutureIsin')),
								sourceCusip: Constants.getSourceCusip(row.nativeData.tags.get('InsType'),
										row.nativeData.tags.get('Code'))
						],
						venueInfo: [
								executionVenueType: Constants.getExecutionVenueType(row.nativeData.tags.get('ElectronicExecution')),
								executionPlatformId: row.nativeData.tags.get('OriginalSource'),
								exchangeCode: Constants.doubleValidityCheck(row.nativeData.tags.get('MIC'), false),
								exchangeCodeType: Constants.EXCH_CODE_TYPE,
								executionVenueLEI: Constants.doubleValidityCheck(row.nativeData.tags.get('VenLEI'), false)
						],
						persons: [
								traderId: row.nativeData.tags.get('Trader'),
								tradeCreatorId: row.nativeData.tags.get('CreateUserId'),
								originatingTradeCreatorId: row.nativeData.tags.get('OriginalCreateUserId'),
								salesPersonId: Constants.doubleValidityCheck(row.nativeData.tags.get('SalesRepId'), false),
								salesPersonName: Constants.doubleValidityCheck(row.nativeData.tags.get('SalesPerson'), false)
						],
						settlement: [
								settlementDate: Constants.getDate(row.nativeData.tags.get('DateSettl'), false),
								settlementAmount: Constants.doubleValidityCheck(row.nativeData.tags.get('SettlTradeNetMoney'), false),
								settlementAmountCurrency: row.nativeData.tags.get('CurrencyStr')
						],
						regulatory: [
								mifidWaiverIndicators: Constants.GetWaiverIndicator(row.nativeData.tags.get('WaiverFlag')),
								mifidExecutionWithinFirm: Constants.doubleValidityCheck(row.nativeData.tags.get('ExecutionMktId'), false),
								mifidInvestmentDecisionWithinFirm: Constants.doubleValidityCheck(row.nativeData.tags.get('InvestmentMktId'), false),
								mifidOtherDetails: "MiFID2Exemption: " + row.nativeData.tags.get('MiFID2Exemption')
						],
						executionDateTime: Constants.getExDateTime(row.nativeData.tags.get('DateMktCreationUTC'),
								row.nativeData.tags.get('TimeMktCreationUTC')),
						assetClass: Constants.getAssetClass(row.nativeData.tags.get('TypeStr')),
						isInternalDeal: Constants.getIsInternalDeal(row.nativeData.tags.get('ScotiaInternal')),
						isTradingBook: true,
						tradeDate: Constants.getDate(row.nativeData.tags.get('DateTrade'), true),
						entryDateTime: Constants.getDateTime(row.nativeData.tags.get('DateOriginalCreateStamp'),
								row.nativeData.tags.get('TimeOriginalCreate'), true),
						tradeUpdateDateTime: Constants.getUpdateDateTime(row.nativeData.tags.get('DateModified'),
								row.nativeData.tags.get('TimeModified'),
								row.nativeData.tags.get('DateCreateStamp'),
								row.nativeData.tags.get('TimeCreate')),
						tradeStatus: Constants.getTradeStatus(row.nativeData.tags.get('Status')),
						tradeEvent: Constants.getTradeEvent(row.nativeData.tags.get('TradeSubStatusStr')),
						csaEligible: true
				],
				book: [
						bookId: Constants.doubleValidityCheck(row.nativeData.tags.get('BookId'), false),
						scotiaLegalEntityId: Constants.doubleValidityCheck(row.nativeData.tags.get('LegalEntity'), false)
				],
				parties: [
						counterparty: [
								partyId: Constants.doubleValidityCheck(row.nativeData.tags.get('CPKey'), false),
								partyName: Constants.doubleValidityCheck(row.nativeData.tags.get('CPName'), false),
								partyLei: Constants.doubleValidityCheck(row.nativeData.tags.get('CounterpartyLEI'), false)
						],
						originalCounterparty: [
								partyShortName: Constants.doubleValidityCheck(row.nativeData.tags.get('CPShortName'), false)
						],
						executingBroker: [
								partyId: Constants.doubleValidityCheck(row.nativeData.tags.get('OriginalSource'), false)
						],
						clearingParty: [
								partyId: Constants.doubleValidityCheck(row.nativeData.tags.get('ClearingHouse'), false)
						]
				],
				costsAndCharges: [
						commissionRates: [
								[
										type: Constants.COMMISSION_TOTAL,
										amount: Constants.doubleValidityCheck(row.nativeData.tags.get('CommissionTotal'), true)
								],
								[
										type: Constants.COMMISSION_AMOUNT,
										amount: Constants.doubleValidityCheck(row.nativeData.tags.get('CommissionAmount'), true)
								]
						]
				],
				//sourceSystemSpecific: [mmi: getMMIFields()],
				// Here's the key change - we reference the MMI wrapper directly
				sourceSystemSpecific: [
						mmi: getMMIFields(),
				],
				product: getProduct(row.nativeData.tags.get('InsType'))
		]
]