//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//
package com.scotia.gcm.sa.v1.trade;

import com.scotia.gcm.sa.v1.MessageHeader;
import java.io.IOException;
import java.io.ObjectInput;
import java.io.ObjectOutput;
import java.nio.ByteBuffer;
import org.apache.avro.AvroMissingFieldException;
import org.apache.avro.AvroRuntimeException;
import org.apache.avro.Schema;
import org.apache.avro.data.RecordBuilder;
import org.apache.avro.data.TimeConversions;
import org.apache.avro.io.DatumReader;
import org.apache.avro.io.DatumWriter;
import org.apache.avro.message.BinaryMessageDecoder;
import org.apache.avro.message.BinaryMessageEncoder;
import org.apache.avro.message.SchemaStore;
import org.apache.avro.specific.AvroGenerated;
import org.apache.avro.specific.SpecificData;
import org.apache.avro.specific.SpecificRecord;
import org.apache.avro.specific.SpecificRecordBase;
import org.apache.avro.specific.SpecificRecordBuilderBase;

/**
 *  Avro schema definition for a TradeMessage type, used in a financial trading system. The schema is extensive and complex,
 *  defining a hierarchical data structure for trade-related information.
 * Schema Structure
 * Main Components:
 * - header: MessageHeader record containing metadata
 * - trade: Trade record containing the trade details. Various product types and supporting data structures
 *
 * Key Fields
 * - Message Header
 * Trade Information
 * - tradeHeader containing identifiers, dates, and status
 * - book information
 * - parties involved in the trade
 * - settlement details
 * - regulatory information
 * - Product-specific details
 *
 * Product Types
 * The schema supports various financial product types including:
 * - Equity products (EquityOption)
 * - Interest rate products (IRSwap, SingleCurrencyCallExtend, Swaption)
 * - Foreign exchange products (FXSingleLeg, FXStrip, FXOption)
 * - Credit products (CreditDefaultSwap)
 * - Commodities (ComOption, ComSwap, ComSpotForward)
 * - Fixed Income products (Forward, Bond, FixedIncomeOption)
 * - Money market products (RepoOrSecFinance, LoanOrDeposit)
 *
 * Supporting Structures
 * - Product identifiers, Trade identifiers, Party information,  Settlement details, Regulatory reporting information,
 *   Source system-specific fields
 *
 * Schema Purpose
 * This appears to be an enterprise-grade schema for financial trade messages at Scotia Bank, used for:
 * - Trade data processing, Regulatory reporting, Cross-system integration, Data warehousing and analytics
 *
 * Non-nullable (required) fields and objects in the Avro schema. These are fields that don't include "null" in their
 * type union and don't have default values specified:
 * - Top-Level Required Fields: MessageHeader record, Trade record
 *
 * Within MessageHeader: messageId (string), businessId (string), sourceSystem (string), sourceSystemCreationTimestamp (timestamp-millis),
 * sentBy (string), sentTo (string)
 *
 * Within Trade: TradeHeader record
 * Within TradeHeader: isTradingBook (boolean), tradeDate (date), entryDateTime (timestamp-millis)
 * tradeUpdateDateTime (timestamp-millis), tradeStatus (string), tradeEvent (string)
 * sourceSystemProductId (ProductIdentifier record), tradeIdentifiers (TradeIdentifiers record)
 *
 * Within TradeIdentifiers: tradeId (VersionedIdentifier record), Within VersionedIdentifier, id (string)
 *
 * Within Common Records: parties.counterparty (Party record), parties.originalCounterparty (Party record)
 *
 * Product-Specific Required Fields:
 * EquityOption: optionCategory (string), optionType (string), buySell (string), expiryDate (date)
 * FXSingleLeg: payCurrency (string), payNotional (double), receiveCurrency (string), receiveNotional (double), fxRate (FxRateQuote record)
 * IRSwap: theoreticalModel (string), payLeg (InterestLeg record), receiveLeg (InterestLeg record)
 * InterestLeg: currency (string), legType (string), paymentFrequency (Period record), isPaymentInAdvance (boolean)
 * dayType (string), CreditDefaultSwap:  (string), notional (double)
 * Swaption: optionType (string), swaptionExerciseStyle (string), buySell (string), isCacheUsed (string), theoreticalModel (string)
 * FXOption: buySell (string), underlyingInstrumentName (string), baseCurrency (string), premiumPaymentDate (date)
 * premiumPaymentAmount (double), premiumPaymentCurrency (string), strikeRate (double), strikeQuoteBasis (string)
 * exerciseStyle (ExerciseStyle record)
 * LoanOrDeposit: termType (string), isLoan (boolean), effectiveDate (date), maturityDate (date), principalCurrency (string),
 * originalPrincipalAmount (double), outstandingPrincipalAmount (double), interestCurrency (string), interestType (string),
 * dayCountFractionType (string), businessDayConventionType (string)
 *
 * Common Structure Fields: Period.periodMultiplier (int), Period.period (string)
 * FxRateQuote.quoteBasis (string), FxRateQuote.quoteValue (double)
 */
@AvroGenerated
public class TradeMessage extends SpecificRecordBase implements SpecificRecord {
    private static final long serialVersionUID = -8675390481311125089L;
    public static final Schema SCHEMA$ = (new Schema.Parser()).parse("{\"type\":\"record\",\"name\":\"TradeMessage\",\"namespace\":\"com.scotia.gcm.sa.v1.trade\",\"fields\":[{\"name\":\"header\",\"type\":{\"type\":\"record\",\"name\":\"MessageHeader\",\"namespace\":\"com.scotia.gcm.sa.v1\",\"fields\":[{\"name\":\"messageId\",\"type\":\"string\"},{\"name\":\"businessId\",\"type\":\"string\"},{\"name\":\"batchId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"sourceSystem\",\"type\":\"string\"},{\"name\":\"secondarySourceSystem\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"sourceSystemCreationTimestamp\",\"type\":{\"type\":\"long\",\"logicalType\":\"timestamp-millis\"}},{\"name\":\"sentBy\",\"type\":\"string\"},{\"name\":\"sentTo\",\"type\":\"string\"},{\"name\":\"messageType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"schemaVersion\",\"type\":\"string\",\"default\":\"1.144\"},{\"name\":\"processing\",\"type\":\"string\",\"default\":\"RealTime\"},{\"name\":\"recordOffset\",\"type\":[\"null\",\"int\"],\"default\":null}]}},{\"name\":\"trade\",\"type\":{\"type\":\"record\",\"name\":\"Trade\",\"fields\":[{\"name\":\"tradeHeader\",\"type\":{\"type\":\"record\",\"name\":\"TradeHeader\",\"fields\":[{\"name\":\"scotiaUPI\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"assetClass\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"algoProductCategory\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isInternalDeal\",\"type\":\"boolean\",\"default\":false},{\"name\":\"isTradingBook\",\"type\":\"boolean\"},{\"name\":\"isXVA\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"tradeDate\",\"type\":{\"type\":\"int\",\"logicalType\":\"date\"}},{\"name\":\"entryDateTime\",\"type\":{\"type\":\"long\",\"logicalType\":\"timestamp-millis\"}},{\"name\":\"executionDateTime\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"TimestampNanos\",\"fields\":[{\"name\":\"millisTimestamp\",\"type\":{\"type\":\"long\",\"logicalType\":\"timestamp-millis\"}},{\"name\":\"nanoOfSecond\",\"type\":[\"null\",\"int\"],\"default\":null}]}],\"default\":null},{\"name\":\"originalExecutionDateTime\",\"type\":[\"null\",{\"type\":\"long\",\"logicalType\":\"timestamp-millis\"}],\"default\":null},{\"name\":\"tradeUpdateDateTime\",\"type\":{\"type\":\"long\",\"logicalType\":\"timestamp-millis\"}},{\"name\":\"latestEventDateTime\",\"type\":[\"null\",{\"type\":\"long\",\"logicalType\":\"timestamp-millis\"}],\"default\":null},{\"name\":\"tradeAssignmentDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"tradeStatus\",\"type\":\"string\"},{\"name\":\"tradeEvent\",\"type\":\"string\"},{\"name\":\"tradeSubEvent\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"tradeEventReasonCode\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"sourceSystemEventDescription\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"tradeTenorCount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"region\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isIntentToDeliver\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"facilityId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"orderEntryType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"sourceSystemProductId\",\"type\":{\"type\":\"record\",\"name\":\"ProductIdentifier\",\"namespace\":\"com.scotia.gcm.sa.v1.common\",\"fields\":[{\"name\":\"sourceInternalId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"sourceInternalIdType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"sourceExternalId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"sourceExternalIdType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"sourceInstrumentName\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"sourceInstrumentCategory\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"sourceIsin\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"sourceCusip\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"sourceSedol1\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"Sedol\",\"fields\":[{\"name\":\"sedol\",\"type\":\"string\"},{\"name\":\"sedolCountry\",\"type\":\"string\"}]}],\"default\":null},{\"name\":\"sourceSedol2\",\"type\":[\"null\",\"Sedol\"],\"default\":null},{\"name\":\"primaryMarketId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"primaryMarketIdType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"adpIdentifier\",\"type\":[\"null\",\"string\"],\"default\":null}]}},{\"name\":\"masteredProductId\",\"type\":[\"null\",{\"type\":\"array\",\"items\":{\"type\":\"record\",\"name\":\"MasteredProductId\",\"namespace\":\"com.scotia.gcm.sa.v1.common\",\"fields\":[{\"name\":\"masteredSourceSystem\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"productId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"productDescription\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"productClassificationLevel1Name\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"productClassificationLevel1Description\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"productClassificationLevel2Name\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"productClassificationLevel2Description\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"productClassificationLevel3Name\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"productClassificationLevel3Description\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"productClassificationLevel4Name\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"productClassificationLevel4Description\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"productClassificationLevel5Name\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"productClassificationLevel5Description\",\"type\":[\"null\",\"string\"],\"default\":null}]}}],\"default\":null},{\"name\":\"csaEligible\",\"type\":\"boolean\",\"default\":true},{\"name\":\"isPartOfPortfolioCompression\",\"type\":[\"null\",\"boolean\"],\"default\":null},{\"name\":\"tradeIdentifiers\",\"type\":{\"type\":\"record\",\"name\":\"TradeIdentifiers\",\"namespace\":\"com.scotia.gcm.sa.v1.common\",\"fields\":[{\"name\":\"tradeId\",\"type\":{\"type\":\"record\",\"name\":\"VersionedIdentifier\",\"fields\":[{\"name\":\"id\",\"type\":\"string\"},{\"name\":\"version\",\"type\":[\"null\",\"string\"],\"default\":null}]}},{\"name\":\"secondarySourceTradeId\",\"type\":[\"null\",\"VersionedIdentifier\"],\"default\":null},{\"name\":\"previousTradeId\",\"type\":[\"null\",\"VersionedIdentifier\"],\"default\":null},{\"name\":\"originatingTradeId\",\"type\":[\"null\",\"VersionedIdentifier\"],\"default\":null},{\"name\":\"parentTradeId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"originatingOrderId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"originatingParentOrderId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"compositeParentOrderID\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"originatingQuoteId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"originatingParentQuoteId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"venueTransactionId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"uniqueSwapId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"uniqueSwapIdPrefix\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"originalUniqueSwapId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"originalUniqueSwapIdPrefix\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"uniqueTransactionId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"uniqueTransactionIdPrefix\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"originalTransactionId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"originalTransactionIdPrefix\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"originalUniqueTransactionId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"tradePackageId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"tradePackageSize\",\"type\":[\"null\",\"int\"],\"default\":null},{\"name\":\"tradeAlternativePackageId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"tradePackageDescription\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"internalReverseTradeId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"tradeName\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"execId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"originatingExecId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"previousExecId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"clearingIdentifier\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"rootOriginatorOrderID\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"RootOrderID\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"tradePackageAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"tradePackageUnwoundAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"substiutionEventPreviousTradeId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"substiutionEventNextTradeId\",\"type\":[\"null\",\"string\"],\"default\":null}]}},{\"name\":\"messageSenderIdentifier\",\"type\":[\"null\",\"", new String[]{"string\"],\"default\":null},{\"name\":\"orderExpiryDateTime\",\"type\":[\"null\",{\"type\":\"long\",\"logicalType\":\"timestamp-millis\"}],\"default\":null},{\"name\":\"venueInfo\",\"type\":{\"type\":\"record\",\"name\":\"VenueInfo\",\"namespace\":\"com.scotia.gcm.sa.v1.common\",\"fields\":[{\"name\":\"executionVenueType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"executionPlatformId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isExchangeTraded\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"exchangeCode\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"exchangeCodeType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"executionVenueLEI\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"accountNumber\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"applicationSessionIdentifier\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"reportTrackingNumber\",\"type\":[\"null\",\"string\"],\"default\":null}]}},{\"name\":\"persons\",\"type\":{\"type\":\"record\",\"name\":\"Persons\",\"namespace\":\"com.scotia.gcm.sa.v1.common\",\"fields\":[{\"name\":\"tradeExecutorId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"tradeExecutorName\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"traderId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"traderName\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"traderLocation\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"tradeCreatorId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"originatingTradeCreatorId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"salesPersonId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"salesPersonName\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"salesPersonLocation\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"algorithmId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"algorithmName\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"algorithmLocation\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"eventUserName\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"originalTradeMarketingUnitName\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"usernote\",\"type\":[\"null\",\"string\"],\"default\":null}]}},{\"name\":\"settlement\",\"type\":{\"type\":\"record\",\"name\":\"Settlement\",\"fields\":[{\"name\":\"settlementType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isClearingEligible\",\"type\":[\"null\",\"boolean\"],\"default\":null},{\"name\":\"isNetted\",\"type\":[\"null\",\"boolean\"],\"default\":null},{\"name\":\"settlementDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"settlementAccountName\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"settlementAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"settlementAmountCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"confirmationIdentifier\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"confirmationMethodCode\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isSingleSidedFlag\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"clearingReceiptDateTime\",\"type\":[\"null\",{\"type\":\"long\",\"logicalType\":\"timestamp-millis\"}],\"default\":null},{\"name\":\"deliveryMethod\",\"type\":[\"null\",\"string\"],\"default\":null}]}},{\"name\":\"regulatory\",\"type\":{\"type\":\"record\",\"name\":\"Regulatory\",\"namespace\":\"com.scotia.gcm.sa.v1.common\",\"fields\":[{\"name\":\"isdaUPIv1\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isdaUPIv2\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isdaAssetClass\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isdaBaseProductId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isdaSubProductId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isdaAdditionalSubProductId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isdaTransactionType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"cfiCode\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isDoddFrankUsPerson\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isVolckerSpot\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isEmirSpot\",\"type\":[\"null\",\"boolean\"],\"default\":null},{\"name\":\"mifidTradingCapacity\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"mifidTradingCapacityEnum\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"mifidInvestmentDecisionWithinFirm\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"mifidExecutionWithinFirm\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isMifidRTO\",\"type\":[\"null\",\"boolean\"],\"default\":null},{\"name\":\"isMifidPriceMaker\",\"type\":[\"null\",\"boolean\"],\"default\":null},{\"name\":\"isHedgeTrade\",\"type\":[\"null\",\"boolean\"],\"default\":null},{\"name\":\"isMifidAgencyTrade\",\"type\":[\"null\",\"boolean\"],\"default\":null},{\"name\":\"isMifidSecuritiesFinancingTrans\",\"type\":[\"null\",\"boolean\"],\"default\":null},{\"name\":\"isMifidCommodityDerivative\",\"type\":[\"null\",\"boolean\"],\"default\":null},{\"name\":\"mifidTransparencyFlag\",\"type\":[\"null\",\"boolean\"],\"default\":null},{\"name\":\"mifidWaiverIndicators\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"string\"}],\"default\":null},{\"name\":\"mifidOtcPostTradeIndicators\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"string\"}],\"default\":null},{\"name\":\"mifidInstrumentIdentificationType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"mifidInstrumentIdentificationCode\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isMifid2FinInstrument\",\"type\":[\"null\",\"boolean\"],\"default\":null},{\"name\":\"mifidLastLqdtyInd\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"mifidBuySell\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"mifidQuantity\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"mifidQuantityCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"mifidPriceCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"mifidNotionalCurrencyAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"mifidOutstandingNotionalAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"mifidNotionalCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"mifidNotionalCurrency1\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"mifidNotionalCurrency2\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"mifidOtherDetails\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"shortSellingIndicator\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isMifidESCBExempt\",\"type\":[\"null\",\"boolean\"],\"default\":null},{\"name\":\"fixRegId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"counterpartyOrigin\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"reportingRegime\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"reportingRole\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"endUserException\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"nonStandardTerms\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"offMarketPrice\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"confirmTimeStamp\",\"type\":[\"null\",{\"type\":\"long\",\"logicalType\":\"timestamp-millis\"}],\"default\":null},{\"name\":\"isRightOfSubstitutionFlag\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isANEFlag\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isBlockTradeFlag\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isMarketMakerFlag\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isLocateRequiredFlag\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isLocateBrokerFlag\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"locateIdentifier\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"complianceIdentifier\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"alphaTradeExecutionDateTime\",\"type\":[\"null\",{\"type\":\"long\",\"logicalType\":\"timestamp-millis\"}],\"default\":null},{\"name\":\"alphaTradeConfirmTimeStamp\",\"type\":[\"null\",{\"type\":\"long\",\"logicalType\":\"timestamp-millis\"}],\"default\":null},{\"name\":\"alphaTradeConfirmationIdentifier\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"compressionProcessingIdentifier\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"lastCapacity\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"tradeIdRepeatingNumber\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"tradePublicationGroupRepeatingNumber\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"tradeRegulationPublicationType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"tradeRegulationPublicationReasonText\",\"type\":[\"null\",\"string\"],\"default\":null}]}},{\"name\":\"memo\",\"type\":[\"null\",\"string\"],\"default\":null}]}},{\"name\":\"book\",\"type\":{\"type\":\"record\",\"name\":\"Book\",\"namespace\":\"com.scotia.gcm.sa.v1.common\",\"fields\"", ":[{\"name\":\"bookingPoint\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"bookId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"bookDescription\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"scotiaLegalEntityId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"transitNumber\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"transitDescription\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"legalEntityName\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"countryId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"departmentCode\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"departmentName\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"riskBook\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"bookOriginName\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"transferBookName\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"counterpartyBookName\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"bookStrategy\",\"type\":[\"null\",\"string\"],\"default\":null}]}},{\"name\":\"masteredBook\",\"type\":[\"null\",{\"type\":\"array\",\"items\":{\"type\":\"record\",\"name\":\"MasteredBook\",\"namespace\":\"com.scotia.gcm.sa.v1.common\",\"fields\":[{\"name\":\"masteredSourceSystem\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"masteredBookId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"bookName\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"transitNumber\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"legalEntityName\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"departmentCode\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"businessUnitLevel1Name\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"businessUnitLevel2Name\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"businessUnitNumber\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"businessUnitDescription\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"businessUnitLEIIdentifier\",\"type\":[\"null\",\"string\"],\"default\":null}]}}],\"default\":null},{\"name\":\"parties\",\"type\":{\"type\":\"record\",\"name\":\"Parties\",\"fields\":[{\"name\":\"counterparty\",\"type\":{\"type\":\"record\",\"name\":\"Party\",\"namespace\":\"com.scotia.gcm.sa.v1.common\",\"fields\":[{\"name\":\"partyId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"partyIdType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"partyName\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"partyShortName\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"partyLei\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"cardsId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"ccdId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"clientMasterId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"partyStatusCode\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"traderId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"traderName\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"additionalPartyAttributes\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"AdditionalPartyAttribute\",\"fields\":[{\"name\":\"participationPercentage\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"participationType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"cityName\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"countryCodeOfDomicile\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"cpShortName\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"collateralizationType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isGiveUpAgreementFlag\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isCsaEligibleFlag\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"cpExternalCreditRating\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"CpExternalCreditRating\",\"namespace\":\"com.scotia.gcm.sa.v1.refdata\",\"fields\":[{\"name\":\"cpRating\",\"type\":\"string\"},{\"name\":\"ratingAgency\",\"type\":\"string\"}]}],\"default\":null},{\"name\":\"accountInfo\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"AccountInfo\",\"fields\":[{\"name\":\"accountNumber\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"accountType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"clientAccountNumber\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"branchCode\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"accountCheckDigitCode\",\"type\":[\"null\",\"string\"],\"default\":null}]}],\"default\":null}]}],\"default\":null}]}},{\"name\":\"originalCounterparty\",\"type\":\"com.scotia.gcm.sa.v1.common.Party\"},{\"name\":\"client\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.Party\"],\"default\":null},{\"name\":\"cardsId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"ccdId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"executingParty\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.Party\"],\"default\":null},{\"name\":\"executingBroker\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.Party\"],\"default\":null},{\"name\":\"clearingParty\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.Party\"],\"default\":null},{\"name\":\"orderOriginatingParty\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.Party\"],\"default\":null},{\"name\":\"triPartyAgent\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.Party\"],\"default\":null},{\"name\":\"lenderParty\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"com.scotia.gcm.sa.v1.common.Party\"}],\"default\":null},{\"name\":\"borrowerParty\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.Party\"],\"default\":null},{\"name\":\"loanTradeCounterParty\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.Party\"],\"default\":null},{\"name\":\"guarantor\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.Party\"],\"default\":null},{\"name\":\"beneficiary\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.Party\"],\"default\":null},{\"name\":\"bankParty\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"com.scotia.gcm.sa.v1.common.Party\"}],\"default\":null},{\"name\":\"arrangingBroker\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.Party\"],\"default\":null},{\"name\":\"reimbursementParty\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.Party\"],\"default\":null}]}},{\"name\":\"masteredLegalEntity\",\"type\":[\"null\",{\"type\":\"array\",\"items\":{\"type\":\"record\",\"name\":\"MasteredLegalEntity\",\"namespace\":\"com.scotia.gcm.sa.v1.common\",\"fields\":[{\"name\":\"masteredSourceSystem\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"sectionReference\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"legalName\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"clientMasterIdentifier\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"ultimateLegalParentIdentifier\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"ultimateLegalParentName\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"legalEntityType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"bnsRelationshipName\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"registeredAddressRegionCode\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"registeredAddressCountryCode\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"countryOfIncorporation\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"internalSubsidiaryIndicator\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"headquarterAddressRegionName\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"headquarterCountryCode\",\"type\":[\"null\",\"string\"],\"default\":null}]}}],\"default\":null},{\"name\":\"costsAndCharges\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"CostsAndCharges\",\"fields\":[{\"name\":\"salesMargin\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"Fee\",\"namespace\":\"com.scotia.gcm.sa.v1.common\",\"fields\":[{\"name\":\"counterpartyId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"counterpartyLegalEntityId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"counterpartyBookId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"feeSubType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"currency\",\"type\":\"string\"},{\"name\":\"amount\",\"type\":\"double\"},{\"name\":\"rate\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"feeSettlementDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"closeoutAccrueToDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"spotMarginPoint\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"marginAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"spotMarginAmount\",\"type\":[\"null\",\"double\"],\"default\":null}]}],\"default\":null},{\"name\":\"traderMargin\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.Fee\"],\"default\":null},{\"name\":\"ndfFee\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.Fee\"],\"default\":null},{\"name\":\"brokerageFee\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.Fe", "e\"],\"default\":null},{\"name\":\"unwindFee\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.Fee\"],\"default\":null},{\"name\":\"assignmentFee\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.Fee\"],\"default\":null},{\"name\":\"commission\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"Commission\",\"fields\":[{\"name\":\"type\",\"type\":\"string\"},{\"name\":\"amount\",\"type\":\"double\"},{\"name\":\"currency\",\"type\":[\"null\",\"string\"],\"default\":null}]}],\"default\":null},{\"name\":\"commissionRates\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"Commission\"}],\"default\":null},{\"name\":\"otherCharges\",\"type\":[\"null\",{\"type\":\"array\",\"items\":{\"type\":\"record\",\"name\":\"OtherCharges\",\"fields\":[{\"name\":\"chargeName\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"chargeAmount\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"rate\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"chargeTypeDescription\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isInclusive\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"chargeCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"chargeCurrencyAmount\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"feeSettlementDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"payIndicator\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isThirdPartyIndicator\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"counterpartyName\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"businessDayConvention\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"paymentHolidayCenters\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"string\"}],\"default\":null}]}}],\"default\":null},{\"name\":\"pointMargin\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"npvMargin\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"fwdMarginPoint\",\"type\":[\"null\",\"double\"],\"default\":null}]}],\"default\":null},{\"name\":\"clearingInstructions\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"ClearingInstructions\",\"fields\":[]}],\"default\":null},{\"name\":\"sourceSystemSpecific\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"SystemSpecificDataWrapperPxv\",\"fields\":[{\"name\":\"pxv\",\"type\":{\"type\":\"record\",\"name\":\"PxvSpecificFields\",\"fields\":[{\"name\":\"placeholder\",\"type\":\"boolean\",\"default\":false},{\"name\":\"isXVA\",\"type\":[\"null\",\"boolean\"],\"default\":null},{\"name\":\"bookingPurpose\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"bookingSubPurpose\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"preDealTradeName\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"userDb\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"userFolders\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"string\"}],\"default\":null},{\"name\":\"dealTypeId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"internalCounterpartyRating\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"groupId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"icarusUserDefinedField\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"nonVanillaUserDefinedField\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"ndfFxResetDates\",\"type\":[\"null\",{\"type\":\"array\",\"items\":{\"type\":\"int\",\"logicalType\":\"date\"}}],\"default\":null},{\"name\":\"decorator\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"Decorator\",\"fields\":[{\"name\":\"paySpecOffsetFlag\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"paySpecOffsetDay\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"paySpecOffsetHolidays\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"string\"}],\"default\":null},{\"name\":\"recSpecOffsetFlag\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"recSpecOffsetDay\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"recSpecOffsetHolidays\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"string\"}],\"default\":null}]}],\"default\":null}]}}]},{\"type\":\"record\",\"name\":\"SystemSpecificDataWrapperMurex\",\"fields\":[{\"name\":\"murex\",\"type\":{\"type\":\"record\",\"name\":\"MurexSpecificFields\",\"fields\":[{\"name\":\"positionHolderId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"positionEffectiveDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"mxTradeId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"mxContractId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isEEOTC\",\"type\":[\"null\",\"boolean\"],\"default\":null},{\"name\":\"EEOTCInstrumentId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"bnsSourceId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"mifidClientHedge\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"mifidCommDerivative\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"mifidComplexTradeComponentId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"mifidExecutionWithinFirm\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"mifidInvestmetnDecisionWithinFirm\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"smart4WayAgreement\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"smartPriceCode\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"smartPriceResolved\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"smartCarryIndicator\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"smartCategory\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"smartCounterpart\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"smartClearer\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"smartCounterpartCo\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"smartClearingMatch\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"smartClearingStatus\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"smartClientId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"smartClOrdId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"smartMatchingRefNo\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"smartTrdMatchId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"smartTrdMatchTime\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"smartOrderId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"smartOrdStatus\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"lmeRingTime\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"mifidEeotc\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"mifidEeotcInstrument\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"salesPersonLocation\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"salesPersonId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"tradeStrategy\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"misType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isGiveUpAgreementFlag\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"giveUpPartyName\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"closeOutReferenceTradeIdentifier\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"tradeFamilyTypeCode\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"tradeGroupTypeCode\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"tradeTypeCode\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"compId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"surchargeRate\",\"type\":[\"null\",\"string\"],\"default\":null}]}}]},{\"type\":\"record\",\"name\":\"SystemSpecificDataWrapperFidessa\",\"fields\":[{\"name\":\"fidessa\",\"type\":{\"type\":\"record\",\"name\":\"FidessaSpecificFields\",\"fields\":[{\"name\":\"investmentDecisionShortCode\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"investmentDecisionValue\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"executionWithinFirmShortCode\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"executionWithinFirmValue\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"dealingCapacity\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"risklessFlag\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"tradeFlags\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"recordType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"businessTransaction\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"orderCounterParty\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"counterparty\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"counterpartyType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"marketId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"routedOrderCode\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"currentServiceIdentifier\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"defaultCommissionRule\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"exchangeTradingCapacity\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"representativeIdentifier\",\"type\":[\"null\",\"string\"],\"default\":n", "ull},{\"name\":\"isDuplicateMessageFlag\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"dealingInstruction\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"tradeDealingInstruction\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isSolicited\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"positionEffect\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isMarketFacingFlag\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"typeOfTransaction\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"settlementCycleCode\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"contractingBookIdentifier\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"executionDestination\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isCompositeOrderIndicator\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"OrderFlagCode\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"compositeOrderTypeQualifierCode\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"tradeCompletionText\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"tradeSourceText\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"executionIsStoppedFill\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"globalOrderId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isCrossIndicator\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"contractMultiplier\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"priceBenchmarkReferenceId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"eventDateTime\",\"type\":[\"null\",{\"type\":\"long\",\"logicalType\":\"timestamp-millis\"}],\"default\":null},{\"name\":\"houseBookName\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"orderVersion\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"executionAccountCode\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"executionDateTime\",\"type\":[\"null\",{\"type\":\"long\",\"logicalType\":\"timestamp-millis\"}],\"default\":null},{\"name\":\"stoppedPriceAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"compId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"orderRisklessCapacityType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isRoutingArrangementIndicator\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"regulatoryTradeIdentifier\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"regulatoryTradeIdentifierType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"strategyParameters\",\"type\":[\"null\",{\"type\":\"array\",\"items\":{\"type\":\"record\",\"name\":\"StrategyParameter\",\"namespace\":\"com.scotia.gcm.sa.v1.common\",\"fields\":[{\"name\":\"strategyName\",\"type\":\"string\"},{\"name\":\"strategyType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"strategyValue\",\"type\":[\"null\",\"string\"],\"default\":null}]}}],\"default\":null}]}}]},{\"type\":\"record\",\"name\":\"SystemSpecificDataWrapperMmi\",\"fields\":[{\"name\":\"mmi\",\"type\":{\"type\":\"record\",\"name\":\"MmiSpecificFields\",\"fields\":[{\"name\":\"isSolicited\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"marketRefId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"sourceTradeStatus\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"sourceTradeSubStatus\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"retailAdpCode\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isRetail\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"checkedbySalesUserId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"blotterCode\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"stpToAdpStatus\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"adpTrailerCode\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"impactStatusMessage\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"tradeStatusChangeBusinessDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"tradeStatusChangeTime\",\"type\":[\"null\",\"TimestampNanos\"],\"default\":null},{\"name\":\"tradeStatusChangeCalendarDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"legIdentifier\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isCancelled\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"additionaInformationText\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"tradeExecutorType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"settlementDayDifference\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"tradePackageDescription\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isMarketMaker\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isExecutedUnderPretradeWaiver\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"refAssetName\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"refAssetAccountingMethod\",\"type\":[\"null\",\"string\"],\"default\":null}]}}]},{\"type\":\"record\",\"name\":\"SystemSpecificDataWrapperAnvil\",\"fields\":[{\"name\":\"anvil\",\"type\":{\"type\":\"record\",\"name\":\"AnvilSpecificFields\",\"fields\":[{\"name\":\"compactId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"compactPreviousId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"compactNextId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"compactOriginalId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"compactTicketGroup\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"subsitutionEventCompactPreviousId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"subsitutionEventCompactNextId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"compactTradeVersion\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"compactStartDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"agreementType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"workflowStateType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"groupId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"groupTradeId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"breakdownId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"groupType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"shellTradeId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"purposeDescription\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"strategyDescription\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"costOfCollateralProfileName\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"tradeClass\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"compactTradeEventReasonCode\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"compactTradeValueCashCurrencyAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"compactTradeValueSecurityCurrencyAmount\",\"type\":[\"null\",\"double\"],\"default\":null}]}}]}],\"default\":null},{\"name\":\"endOfDayPosition\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"EndOfDayPosition\",\"fields\":[{\"name\":\"currency\",\"type\":\"string\"},{\"name\":\"positionValue\",\"type\":\"double\"},{\"name\":\"bookValueCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"bookValue\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"bookValueGlAccountNo\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"generalLedgerDetail\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"GeneralLedgerDetail\",\"namespace\":\"com.scotia.gcm.sa.v1.common\",\"fields\":[{\"name\":\"generalLedgerAttributes\",\"type\":[\"null\",{\"type\":\"array\",\"items\":{\"type\":\"record\",\"name\":\"GeneralLedgerAttribute\",\"fields\":[{\"name\":\"accountType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"accountNumber\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"product\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"currency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"departmentCode\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"customerSegment\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"amountType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"strategyCode\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"bookId\",\"type\":[\"null\",\"string\"],\"default\":null}]}}],\"default\":null}]}],\"default\":null},{\"name\":\"metrics\",\"type\":[\"null\",{\"type\":\"array\",\"items\":{\"type\":\"record\",\"name\":\"Metric\",\"namespace\":\"com.scotia.gcm.sa.v1.common\",\"fields\":[{\"name\":\"name\",\"type\":\"string\"},{\"name\":\"value\",\"type\":\"double\"},{\"name\":\"currency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"glAccountNo\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"subName\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"timePeriod\",\"type\":[\"null\",\"string\"],\"default\":null}]}}],\"default\":null}]}],\"default\":null},{\"name\":\"generalLedgerDetail\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.GeneralLedgerDetail\"],\"default\"", ":null},{\"name\":\"accountingMasteredMetrics\",\"type\":[\"null\",{\"type\":\"array\",\"items\":{\"type\":\"record\",\"name\":\"AccountingMasteredMetrics\",\"namespace\":\"com.scotia.gcm.sa.v1.common\",\"fields\":[{\"name\":\"name\",\"type\":\"string\"},{\"name\":\"value\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"currency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"payIndicator\",\"type\":[\"null\",\"string\"],\"default\":null}]}}],\"default\":null},{\"name\":\"intraDayTradeAttributes\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"IntradayTrade\",\"fields\":[{\"name\":\"metrics\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"com.scotia.gcm.sa.v1.common.Metric\"}],\"default\":null}]}],\"default\":null},{\"name\":\"additionalComments\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"string\"}],\"default\":null},{\"name\":\"product\",\"type\":[{\"type\":\"record\",\"name\":\"EquityOption\",\"namespace\":\"com.scotia.gcm.sa.v1.trade.eo\",\"fields\":[{\"name\":\"optionCategory\",\"type\":\"string\"},{\"name\":\"optionType\",\"type\":\"string\"},{\"name\":\"buySell\",\"type\":\"string\"},{\"name\":\"quantity\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"expiryDate\",\"type\":{\"type\":\"int\",\"logicalType\":\"date\"}},{\"name\":\"isCollateralFlag\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"paymentDaysOffset\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"Period\",\"namespace\":\"com.scotia.gcm.sa.v1.common\",\"fields\":[{\"name\":\"periodMultiplier\",\"type\":\"int\"},{\"name\":\"period\",\"type\":\"string\"}]}],\"default\":null},{\"name\":\"initialNotionalAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"initialNotionalCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"theoreticalModel\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"assetSimulationCurve\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"assetSimulationCurvePoint\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"discountRateCurve\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"discountRateCurvePoint\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"dividendRateCurve\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"volatilityCurve\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"tradeStrategy\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isIncludedInCCR\",\"type\":[\"null\",\"boolean\"],\"default\":null},{\"name\":\"lotSize\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"specialCreditIndicatorCode\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"venueContractCode\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"venueContractName\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"venueContractType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"paymentHolidayCenters\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"string\"}],\"default\":null},{\"name\":\"assetHolidayCenters\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"string\"}],\"default\":null},{\"name\":\"fxRate\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"FxRateQuote\",\"namespace\":\"com.scotia.gcm.sa.v1.common\",\"fields\":[{\"name\":\"currencyPair\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"quoteTimestamp\",\"type\":[\"null\",{\"type\":\"long\",\"logicalType\":\"timestamp-millis\"}],\"default\":null},{\"name\":\"quoteBasis\",\"type\":\"string\"},{\"name\":\"quoteValue\",\"type\":\"double\"},{\"name\":\"baseEquivalentRate\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"baseEquivalentAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"baseEquivalentCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"marketRate\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"spotMarketRate\",\"type\":[\"null\",\"double\"],\"default\":null}]}],\"default\":null},{\"name\":\"dividendCondition\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"DividendCondition\",\"fields\":[{\"name\":\"dividendAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"dividendCurrency\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"string\"}],\"default\":null}]}],\"default\":null},{\"name\":\"contractDetails\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"ContractDetails\",\"fields\":[{\"name\":\"strikeLevel\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"assetNotional\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"assetNotionalCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"spotResetDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"spotPrice\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"spotCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"strikePrice\",\"type\":\"double\"},{\"name\":\"strikeCurrency\",\"type\":[\"null\",\"string\"],\"default\":null}]}],\"default\":null},{\"name\":\"equityExercise\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"EquityExercise\",\"fields\":[{\"name\":\"exerciseStyle\",\"type\":\"string\"},{\"name\":\"isSpecialOpeningQuotation\",\"type\":[\"null\",\"boolean\"],\"default\":null},{\"name\":\"equityAmericanExercise\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"EquityAmericanExercise\",\"fields\":[{\"name\":\"isExercisedEarly\",\"type\":\"boolean\"}]}],\"default\":null}]}],\"default\":null},{\"name\":\"feature\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"Feature\",\"fields\":[{\"name\":\"expiryResetSchedule\",\"type\":[\"null\",{\"type\":\"array\",\"items\":{\"type\":\"record\",\"name\":\"ExpiryResetScheduleEntry\",\"fields\":[{\"name\":\"resetDate\",\"type\":{\"type\":\"int\",\"logicalType\":\"date\"}},{\"name\":\"resetAmount\",\"type\":\"double\"}]}}],\"default\":null},{\"name\":\"dividendProtection\",\"type\":[\"null\",\"string\"],\"default\":null}]}],\"default\":null},{\"name\":\"underlyer\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"Underlyer\",\"fields\":[{\"name\":\"productIdentifier\",\"type\":\"com.scotia.gcm.sa.v1.common.ProductIdentifier\"},{\"name\":\"events\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"UnderlyerEvents\",\"namespace\":\"com.scotia.gcm.sa.v1.common\",\"fields\":[{\"name\":\"maturityLabel\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"tradeExpiryDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null}]}],\"default\":null}]}],\"default\":null},{\"name\":\"equityPremium\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"EquityPremium\",\"fields\":[{\"name\":\"premiumAmount\",\"type\":\"double\"},{\"name\":\"premiumCurrency\",\"type\":\"string\"},{\"name\":\"premiumPaymentDate\",\"type\":{\"type\":\"int\",\"logicalType\":\"date\"}}]}],\"default\":null},{\"name\":\"isHedge\",\"type\":[\"null\",\"boolean\"],\"default\":null},{\"name\":\"isUseDeferredPremiumAmountIndicator\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"hedgeTrade\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"EquityHedgeTrade\",\"fields\":[{\"name\":\"name\",\"type\":\"string\"},{\"name\":\"buySell\",\"type\":\"string\"},{\"name\":\"strikeLevel\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"assetNotional\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"assetNotionalCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"initialNotionalAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"initialNotionalCurrency\",\"type\":[\"null\",\"string\"],\"default\":null}]}],\"default\":null},{\"name\":\"event\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"EOEvent\",\"fields\":[{\"name\":\"effectiveDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"terminationDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"adjustedEffectiveDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"adjustedTerminationDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"maturityLabel\",\"type\":[\"null\",\"string\"],\"default\":null}]}],\"default\":null},{\"name\":\"fixAttributes\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"FixAttributes\",\"namespace\":\"com.scotia.gcm.sa.v1.common\",\"fields\":[{\"name\":\"execTransType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"fixExecType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"idSource\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"orderStatus\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"orderStatusReason\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"additionaInformationText\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"pricePerShare\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"stopPrice\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"fillPrice\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"size\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"remainingSize\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"cumulativeSize\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"fixExecInst\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"orderValidityPeriod\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isAnonymo", "us\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"fixExecRefId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"orderType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"relationship\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"sourceEntity\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"destinationEntity\",\"type\":[\"null\",\"string\"],\"default\":null}]}],\"default\":null}]},{\"type\":\"record\",\"name\":\"SingleCurrencyCallExtend\",\"fields\":[{\"name\":\"terminationDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"notionalCurrency\",\"type\":\"string\"},{\"name\":\"notional\",\"type\":\"double\"},{\"name\":\"isCollateralFlag\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"swaptionComponentId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"swap\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"IRSwap\",\"fields\":[{\"name\":\"npvCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"collateralcurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isCollateralFlag\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isTradeSplit\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"fxResetType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"fxConvertDir\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"fxResetInfo\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"FxResetInfo\",\"fields\":[{\"name\":\"fxResetOffset\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.Period\"],\"default\":null},{\"name\":\"fxResetHolidayCenters\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"string\"}],\"default\":null},{\"name\":\"fxSourceRef\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"dasFxReferenceSource\",\"type\":[\"null\",\"string\"],\"default\":null}]}],\"default\":null},{\"name\":\"fxResetRates\",\"type\":[\"null\",{\"type\":\"array\",\"items\":{\"type\":\"record\",\"name\":\"Reset\",\"namespace\":\"com.scotia.gcm.sa.v1.common\",\"fields\":[{\"name\":\"resetDate\",\"type\":{\"type\":\"int\",\"logicalType\":\"date\"}},{\"name\":\"resetRate\",\"type\":\"double\"}]}}],\"default\":null},{\"name\":\"principalExchange\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"principalPaymentDateType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"theoreticalModel\",\"type\":\"string\"},{\"name\":\"isAccrualAccounting\",\"type\":[\"null\",\"boolean\"],\"default\":null},{\"name\":\"payLeg\",\"type\":{\"type\":\"record\",\"name\":\"InterestLeg\",\"namespace\":\"com.scotia.gcm.sa.v1.common\",\"fields\":[{\"name\":\"currency\",\"type\":\"string\"},{\"name\":\"legType\",\"type\":\"string\"},{\"name\":\"fixedRate\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"spread\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"notional\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"notionalType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isMultiplePaymentDaysFlag\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"interestPaymentMonths\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"string\"}],\"default\":null},{\"name\":\"nextResetDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"interpolationIndicator\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"firstPeriodIndexFrequencyType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"lastPeriodIndexFrequencyType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"firstPeriodResetDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"lastPeriodResetDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"interestResetMonths\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"string\"}],\"default\":null},{\"name\":\"dasReferenceSource\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"notionalSchedule\",\"type\":[\"null\",{\"type\":\"array\",\"items\":{\"type\":\"record\",\"name\":\"NotionalSchedule\",\"fields\":[{\"name\":\"notionalDate\",\"type\":{\"type\":\"int\",\"logicalType\":\"date\"}},{\"name\":\"notionalAmt\",\"type\":\"double\"},{\"name\":\"notionalCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"notionalPaymentDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null}]}}],\"default\":null},{\"name\":\"notionalAmortizationDetails\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"NotionalAmortizationDetails\",\"fields\":[{\"name\":\"notionalAmortFreq\",\"type\":[\"null\",\"Period\"],\"default\":null},{\"name\":\"notionalDateAdjType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"notionalPaydownAmt\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"notionalTargetBalance\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"notionalMortPayAmt\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"notionalImpliedMortRate\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"notionalMortAmortTerm\",\"type\":[\"null\",\"int\"],\"default\":null},{\"name\":\"notionalOutstandPrin\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"notionalMortRate\",\"type\":[\"null\",\"double\"],\"default\":null}]}],\"default\":null},{\"name\":\"referenceIndex\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"referenceIndexTenor\",\"type\":[\"null\",\"Period\"],\"default\":null},{\"name\":\"referenceIndexDayCount\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"sourceForwardCurve\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"sourceForwardCurveName\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"sourceDiscountCurve\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"sourceDiscountCurveName\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"fallbackCurveName\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"indexFraction\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"indexType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"resetRates\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"Reset\"}],\"default\":null},{\"name\":\"resetFrequency\",\"type\":[\"null\",\"Period\"],\"default\":null},{\"name\":\"dayCount\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"resetHolidayCenters\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"string\"}],\"default\":null},{\"name\":\"resetBusinessDayConvention\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"resetOffset\",\"type\":[\"null\",\"Period\"],\"default\":null},{\"name\":\"isResetInAdvance\",\"type\":[\"null\",\"boolean\"],\"default\":null},{\"name\":\"paymentFrequency\",\"type\":\"Period\"},{\"name\":\"paymentHolidayCenters\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"string\"}],\"default\":null},{\"name\":\"paymentBusinessDayConvention\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"paymentDatesOffset\",\"type\":[\"null\",\"Period\"],\"default\":null},{\"name\":\"isPaymentInAdvance\",\"type\":\"boolean\"},{\"name\":\"stubType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"stubDayCount\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"stubTypeAdjustedDates\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"StubTypeAdjustedDates\",\"fields\":[{\"name\":\"initialPeriodStartDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"initialPeriodEndDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"lastPeriodStartDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"lastPeriodEndDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null}]}],\"default\":null},{\"name\":\"dateAdjustType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"firstPaymentDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"firstRollDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"accrualStartDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"fees\",\"type\":[\"null\",{\"type\":\"array\",\"items\":{\"type\":\"record\",\"name\":\"FeeSchedule\",\"fields\":[{\"name\":\"feeType\",\"type\":\"string\"},{\"name\":\"feeDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"feeAmt\",\"type\":\"double\"},{\"name\":\"feeCurrency\",\"type\":\"string\"}]}}],\"default\":null},{\"name\":\"dayType\",\"type\":\"string\"},{\"name\":\"compoundingMethod\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isConstantMaturity\",\"type\":[\"null\",\"boolean\"],\"default\":null},{\"name\":\"isOisCompoundRounding\",\"type\":[\"null\",\"boolean\"],\"default\":null},{\"name\":\"isAverage\",\"type\":[\"null\",\"boolean\"],\"default\":null},{\"name\":\"averageFrequency\",\"type\":[\"null\",\"Period\"],\"default\":null},{\"name\":\"isWeightedAvg\",\"type\":[\"null\",\"boolean\"],\"default\":null},{\"name\":\"swapLongFormSchedule\",\"type\":[\"null\",{\"type\":\"array\",\"items\":{\"type\":\"record\",\"name\":\"SwapLongForm\",\"fields\":[{\"name\":\"resetPeriodDate\",\"type\":{\"type\":\"int\",\"logicalType\":\"date\"}},{\"name\":\"resetDate\",\"type\":[\"null\",{", "\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"fxResetDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"paymentPeriodDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"paymentDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"periodNotional\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"spread\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"fixedRate\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"forwardPeriodStartDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"forwardPeriodEndDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"indexFreq\",\"type\":[\"null\",\"Period\"],\"default\":null},{\"name\":\"interestPayment\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"cpiResetRate\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"cpiResetDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"fixingDay\",\"type\":[\"null\",\"string\"],\"default\":null}]}}],\"default\":null},{\"name\":\"ndfFXResets\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"Reset\"}],\"default\":null},{\"name\":\"ndfFXResetHolidays\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"string\"}],\"default\":null},{\"name\":\"ndfFXResetOffset\",\"type\":[\"null\",\"Period\"],\"default\":null},{\"name\":\"ndfSettlementCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"swapLongFormEditedFields\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"string\"}],\"default\":null},{\"name\":\"ndfSettlementDiscountCurves\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"string\"}],\"default\":null},{\"name\":\"ndfSettlementPayments\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"SwapLongForm\"}],\"default\":null},{\"name\":\"ndfSettlementFees\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"FeeSchedule\"}],\"default\":null},{\"name\":\"isArrearsConvexityAdj\",\"type\":[\"null\",\"boolean\"],\"default\":null},{\"name\":\"volatility\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"isCm\",\"type\":[\"null\",\"boolean\"],\"default\":null},{\"name\":\"isDifferentialSwap\",\"type\":[\"null\",\"boolean\"],\"default\":null},{\"name\":\"diffCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"fxRateCorrelation\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"fxVolatilityCurveName\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"rateRounding\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"inflationRateCalculation\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"InflationRateCalculation\",\"fields\":[{\"name\":\"inflationLag\",\"type\":[\"null\",\"Period\"],\"default\":null},{\"name\":\"indexSource\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"interpolationMethod\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"initialIndexLevel\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"cpiLinkage\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"cpiCurve\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"cpiResets\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"Reset\"}],\"default\":null}]}],\"default\":null},{\"name\":\"additionalTerms\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"AdditionalTerms\",\"fields\":[{\"name\":\"referenceBondIsin\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"optionType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"strikePrice\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"cpiIsin\",\"type\":[\"null\",\"string\"],\"default\":null}]}],\"default\":null},{\"name\":\"periodHolidayCenters\",\"type\":{\"type\":\"array\",\"items\":\"string\"}},{\"name\":\"specialPayPeriod\",\"type\":[\"null\",\"Period\"],\"default\":null},{\"name\":\"specialResetPeriod\",\"type\":[\"null\",\"Period\"],\"default\":null},{\"name\":\"specialIndexPeriod\",\"type\":[\"null\",\"Period\"],\"default\":null},{\"name\":\"firstResetHolidayCenters\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"string\"}],\"default\":null},{\"name\":\"overnightIndex\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"OvernightIndex\",\"fields\":[{\"name\":\"offsetFlag\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"offsetDay\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"offsetHolidayCenters\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"string\"}],\"default\":null},{\"name\":\"isSpreadForPayPeriod\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isNotionalForPayPeriod\",\"type\":[\"null\",\"string\"],\"default\":null}]}],\"default\":null},{\"name\":\"imbeddedOption\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"ImbeddedOption\",\"fields\":[{\"name\":\"optionType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isOptionOnDailyRateFlag\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"volatilitySurface\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"capFloorStrike\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"double\"}],\"default\":null},{\"name\":\"capFloorStrike2\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"double\"}],\"default\":null}]}],\"default\":null}]}},{\"name\":\"receiveLeg\",\"type\":\"com.scotia.gcm.sa.v1.common.InterestLeg\"},{\"name\":\"events\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"IrEvents\",\"fields\":[{\"name\":\"effectiveDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"terminationDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"adjustedEffectiveDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"adjustedTerminationDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"contractBreaks\",\"type\":[\"null\",{\"type\":\"array\",\"items\":{\"type\":\"record\",\"name\":\"ContractBreak\",\"namespace\":\"com.scotia.gcm.sa.v1.common\",\"fields\":[{\"name\":\"contractBreakType\",\"type\":\"string\"},{\"name\":\"contractBreakDate\",\"type\":{\"type\":\"int\",\"logicalType\":\"date\"}},{\"name\":\"contractBreakSettlementDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null}]}}],\"default\":null},{\"name\":\"contractBreakAttributes\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"ContractBreakAttributes\",\"namespace\":\"com.scotia.gcm.sa.v1.common\",\"fields\":[{\"name\":\"exerciseCity\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"exerciseCityTimeZone\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"exerciseDailyEndTime\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"exerciseDailyStartTime\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"exerciseDateOffset\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"exerciseFrequency\",\"type\":[\"null\",\"Period\"],\"default\":null},{\"name\":\"exerciseHolidayCenters\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"string\"}],\"default\":null},{\"name\":\"exerciseOffsetBusinessDayConvention\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"exerciseReferenceDateSide\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"exerciseReferenceDateGenerationType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"firstSettlementDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"optionalTerminationType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"settlementDateAdjustmentMethod\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"settlementDayType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"settlementHolidaysCenters\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"string\"}],\"default\":null},{\"name\":\"settlementOffsetBusinessDayConvention\",\"type\":[\"null\",\"string\"],\"default\":null}]}],\"default\":null}]}],\"default\":null},{\"name\":\"componentName\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"components\",\"type\":[\"null\",{\"type\":\"array\",\"items\":{\"type\":\"record\",\"name\":\"IRSubSwap\",\"fields\":[{\"name\":\"npvCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"fxResetType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"fxConvertDir\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"fxResetInfo\",\"type\":[\"null\",\"FxResetInfo\"],\"default\":null},{\"name\":\"fxResetRates\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"com.scotia.gcm.sa.v1.common.Reset\"}],\"default\":null},{\"name\":\"principalExchange\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"theoreticalModel\",\"type\":\"string\"},{\"name\":\"payLeg\",\"type\":\"com.scotia.gcm.sa.v1.common.InterestLeg\"},{\"name\":\"receiveLeg\",\"type\":\"com.scotia.gcm.sa.v1.common.InterestLeg\"},{\"name\":\"events\",\"type\":[\"null\",\"IrEvents\"],\"default\":null},{\"name\":\"componentName\",\"type\":[\"null\",\"string\"],\"default\":null}]}}],\"default\":null},{\"name\":\"novation\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"Novation\",\"namespace\":\"com.scotia.gcm.sa.v1.common\",\"fields\":[{\"name\":\"isBNSTransferorFlag\",\"type\":[\"null\",\"str", "ing\"],\"default\":null},{\"name\":\"isBNSTransfereeFlag\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"closeoutAccrueToDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null}]}],\"default\":null}]}],\"default\":null},{\"name\":\"swaption\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"Swaption\",\"fields\":[{\"name\":\"swaptionPremium\",\"type\":[\"null\",{\"type\":\"array\",\"items\":{\"type\":\"record\",\"name\":\"Premium\",\"fields\":[{\"name\":\"paymentAmount\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"PaymentAmount\",\"fields\":[{\"name\":\"currency\",\"type\":\"string\"},{\"name\":\"amount\",\"type\":\"double\"},{\"name\":\"PaymentDate\",\"type\":{\"type\":\"int\",\"logicalType\":\"date\"}}]}],\"default\":null}]}}],\"default\":null},{\"name\":\"optionType\",\"type\":\"string\"},{\"name\":\"isCollateralFlag\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"swaptionExerciseStyle\",\"type\":\"string\"},{\"name\":\"swaptionExerciseStyleDetails\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"SwaptionExerciseStyleDetails\",\"fields\":[{\"name\":\"bermudaExercise\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"Bermuda\",\"fields\":[{\"name\":\"bermudaExerciseDate\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"BermudaExerciseDate\",\"fields\":[{\"name\":\"exerciseSchedule\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"exerciseDate\",\"type\":[\"null\",{\"type\":\"array\",\"items\":{\"type\":\"int\",\"logicalType\":\"date\"}}],\"default\":null}]}],\"default\":null},{\"name\":\"expirationDate\",\"type\":{\"type\":\"int\",\"logicalType\":\"date\"}},{\"name\":\"firstExpirationDate\",\"type\":{\"type\":\"int\",\"logicalType\":\"date\"}},{\"name\":\"isConvergentMaturity\",\"type\":[\"null\",\"boolean\"],\"default\":null},{\"name\":\"isConstantStrikeSwap\",\"type\":[\"null\",\"boolean\"],\"default\":null},{\"name\":\"paymentDaysOffset\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"PaymentDaysOffset\",\"fields\":[{\"name\":\"paymentDaysOffset\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.Period\"],\"default\":null},{\"name\":\"paymentDaysOffsetConvention\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"dateAdjustmentMethod\",\"type\":[\"null\",\"string\"],\"default\":null}]}],\"default\":null},{\"name\":\"instrumentRate\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"double\"}],\"default\":null},{\"name\":\"redemptionPremium\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"double\"}],\"default\":null},{\"name\":\"redemptionPremiumCurrency\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"string\"}],\"default\":null},{\"name\":\"isAdjustSigma\",\"type\":[\"null\",\"boolean\"],\"default\":null},{\"name\":\"isRefitSigma\",\"type\":[\"null\",\"boolean\"],\"default\":null},{\"name\":\"isFullExercise\",\"type\":[\"null\",\"boolean\"],\"default\":null},{\"name\":\"multipleExercise\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"MultipleExercise\",\"fields\":[{\"name\":\"isMultipleExercise\",\"type\":[\"null\",\"boolean\"],\"default\":null},{\"name\":\"numberOfExercise\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"maximumNumberOfOptions\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"isNotionalConstant\",\"type\":[\"null\",\"boolean\"],\"default\":null}]}],\"default\":null},{\"name\":\"swapStartDate\",\"type\":[\"null\",{\"type\":\"array\",\"items\":{\"type\":\"int\",\"logicalType\":\"date\"}}],\"default\":null},{\"name\":\"swapEndDate\",\"type\":[\"null\",{\"type\":\"array\",\"items\":{\"type\":\"int\",\"logicalType\":\"date\"}}],\"default\":null}]}],\"default\":null},{\"name\":\"europeanExercise\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"EuropeanExercise\",\"fields\":[{\"name\":\"expirationDate\",\"type\":{\"type\":\"int\",\"logicalType\":\"date\"}}]}],\"default\":null}]}],\"default\":null},{\"name\":\"buySell\",\"type\":\"string\"},{\"name\":\"isCacheUsed\",\"type\":\"string\"},{\"name\":\"theoreticalModel\",\"type\":\"string\"},{\"name\":\"isIncludedInCCR\",\"type\":[\"null\",\"boolean\"],\"default\":null},{\"name\":\"initialNotionalAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"initialNotionalCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"paymentHolidayCenters\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"string\"}],\"default\":null},{\"name\":\"businessDateAdjustmentMethod\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"mutualPutDates\",\"type\":[\"null\",{\"type\":\"array\",\"items\":{\"type\":\"int\",\"logicalType\":\"date\"}}],\"default\":null},{\"name\":\"settlementDates\",\"type\":[\"null\",{\"type\":\"array\",\"items\":{\"type\":\"int\",\"logicalType\":\"date\"}}],\"default\":null},{\"name\":\"isAmericanPut\",\"type\":[\"null\",\"boolean\"],\"default\":null},{\"name\":\"isMandatoryPut\",\"type\":[\"null\",\"boolean\"],\"default\":null},{\"name\":\"isAccrualAccounting\",\"type\":[\"null\",\"boolean\"],\"default\":null},{\"name\":\"novation\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.Novation\"],\"default\":null},{\"name\":\"swap\",\"type\":[\"null\",\"IRSwap\"],\"default\":null},{\"name\":\"swaptionMeasures\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"Measures\",\"fields\":[{\"name\":\"volatilityCurve\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"volatilitySurface\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"volatilitySpread\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"discountCurve\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"discountRate\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"dividendCurve\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"dividendRate\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"forwardCurve\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"fallbackCurveName\",\"type\":[\"null\",\"string\"],\"default\":null}]}],\"default\":null},{\"name\":\"swapTerm\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"events\",\"type\":[\"null\",\"IrEvents\"],\"default\":null}]}],\"default\":null}]},{\"type\":\"record\",\"name\":\"TotalReturnSwap\",\"fields\":[{\"name\":\"terminationDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"notionalCurrency\",\"type\":\"string\"},{\"name\":\"notional\",\"type\":\"double\"},{\"name\":\"isCollateralFlag\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"accountingMethod\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"priceQuantityResetList\",\"type\":[\"null\",{\"type\":\"array\",\"items\":{\"type\":\"record\",\"name\":\"EquityPriceQuantityReset\",\"fields\":[{\"name\":\"resetPrice\",\"type\":\"double\"},{\"name\":\"resetDate\",\"type\":{\"type\":\"int\",\"logicalType\":\"date\"}},{\"name\":\"quantity\",\"type\":\"double\"},{\"name\":\"paymentDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null}]}}],\"default\":null},{\"name\":\"buySell\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"theoreticalModel\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isIncludedInCCR\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isPayTodayFlag\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"interestLeg\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.InterestLeg\"],\"default\":null},{\"name\":\"assetLeg\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"TRSAssetLeg\",\"fields\":[{\"name\":\"legType\",\"type\":\"string\"},{\"name\":\"currency\",\"type\":\"string\"},{\"name\":\"assetType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"assetSubType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"notional\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"units\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"assetQtyType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"nextResetDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"resetFrequency\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.Period\"],\"default\":null},{\"name\":\"interpolationRateMehod\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"breakFeeType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"breakRate\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"isNotionalAdjustment\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"notionalAdjustHistories\",\"type\":[\"null\",{\"type\":\"array\",\"items\":{\"type\":\"record\",\"name\":\"NotionalAdjustmentHistory\",\"fields\":[{\"name\":\"adjustDate\",\"type\":{\"type\":\"int\",\"logicalType\":\"date\"}},{\"name\":\"interestRate\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"adjustNotional\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"priceOnReset\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"isUnitChange\",\"type\":[\"null\",\"string\"],\"default\":null}]}}],\"default\":null},{\"name\":\"referenceAssetName\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"referenceAssetId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"basketConstituents\",\"type\":[\"null\",{\"type\":\"array\",\"items\":{\"type\":\"record\",\"name\":\"BasketConstituent\",\"fields\":[{\"name\":\"assetId\",\"type\":\"string\"},{\"name\":\"versionDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"noOfShares\",\"type\":[\"null\",\"d", "ouble\"],\"default\":null},{\"name\":\"currency\",\"type\":[\"null\",\"string\"],\"default\":null}]}}],\"default\":null},{\"name\":\"referenceAssetPathId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"spotSource\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"spotPrice\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"spotPriceType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"percentagePassThrough\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"withholdingTaxRate\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"discountCurve\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"discountRate\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"assetGrowthCurve\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"assetGrowthRate\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isGrowAsset\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"dividendPaymentDateType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"dividendDeterminationType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isDividendNativeCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"dividendLag\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.Period\"],\"default\":null},{\"name\":\"dividendCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"paymentHolidayCenters\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"string\"}],\"default\":null},{\"name\":\"resetHolidayCenters\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"string\"}],\"default\":null},{\"name\":\"stubType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"dayType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"paymentBusinessDayConvention\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"sourceForwardCurve\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"underlyer\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"TRSUnderlyer\",\"fields\":[{\"name\":\"totalReturnSwaps\",\"type\":[\"null\",{\"type\":\"array\",\"items\":{\"type\":\"record\",\"name\":\"TRSUnderlyerTotalReturnSwap\",\"fields\":[{\"name\":\"assetClass\",\"type\":\"string\"},{\"name\":\"productIdentifier\",\"type\":\"com.scotia.gcm.sa.v1.common.ProductIdentifier\"}]}}],\"default\":null},{\"name\":\"forwards\",\"type\":[\"null\",{\"type\":\"array\",\"items\":{\"type\":\"record\",\"name\":\"TRSUnderlyerForward\",\"fields\":[{\"name\":\"assetClass\",\"type\":\"string\"},{\"name\":\"productIdentifier\",\"type\":\"com.scotia.gcm.sa.v1.common.ProductIdentifier\"}]}}],\"default\":null}]}],\"default\":null}]}],\"default\":null},{\"name\":\"events\",\"type\":[\"null\",\"IrEvents\"],\"default\":null},{\"name\":\"expiryDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"fixedFloatPayment\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"dateGenerateType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"assetOffsetPeriod\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.Period\"],\"default\":null},{\"name\":\"isNonRecourse\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isWrongWay\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"accrueSpreadModel\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"creditMatrixId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"initialNotionalAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"initialNotionalCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"novation\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.Novation\"],\"default\":null},{\"name\":\"minimumSpread\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"MinimumSpread\",\"fields\":[{\"name\":\"isMinimumSpreadFlag\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"minimumSpreadSchedule\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"MinimumSpreadSchedule\",\"fields\":[{\"name\":\"periodStartDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"minimumNotional\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"couponSpread\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"calculationType\",\"type\":[\"null\",\"string\"],\"default\":null}]}],\"default\":null}]}],\"default\":null},{\"name\":\"minSpread\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"MinimumSpreadTRS\",\"fields\":[{\"name\":\"isMinimumSpreadFlag\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"minimumSpreadSchedule\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"MinimumSpreadSchedule\"}],\"default\":null}]}],\"default\":null}]},{\"type\":\"record\",\"name\":\"CreditDefaultSwap\",\"fields\":[{\"name\":\"terminationDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"notionalCurrency\",\"type\":\"string\"},{\"name\":\"notional\",\"type\":\"double\"},{\"name\":\"isCollateralFlag\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"buySell\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"theoreticalModel\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"cdsType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isIncludedInCCR\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"interestLeg\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.InterestLeg\"],\"default\":null},{\"name\":\"assetLeg\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"CDSAssetLeg\",\"fields\":[{\"name\":\"legType\",\"type\":\"string\"},{\"name\":\"referenceAssetName\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"tradedPrice\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"tradedPriceType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isAllGuaranteed\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"creditTransactionType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"creditTransactionTypeVersionDate\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"referenceObligation\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"CreditReferenceObligation\",\"fields\":[{\"name\":\"redsId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"issuer\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"markitObligor\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"guarantor\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"maturityDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"coupon\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"cusipIsin\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"appliedFactor\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"currentFactor\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"issueAmount\",\"type\":[\"null\",\"double\"],\"default\":null}]}],\"default\":null},{\"name\":\"paymentConditions\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"CreditPaymentConditions\",\"fields\":[{\"name\":\"notifyingParty\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isPublicAvailableInfo\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"nonStandardPublicSource\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"specificNumberOfSource\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"isdaInfo\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"string\"}],\"default\":null}]}],\"default\":null},{\"name\":\"creditSettlementTerms\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"CreditSettlementTerms\",\"fields\":[{\"name\":\"settlementMethod\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"settlementTerms\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"CreditPhysicalSettlementTerms\",\"fields\":[{\"name\":\"cashPhysicalSettlementLag\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.Period\"],\"default\":null},{\"name\":\"portfolioAccrualMethod\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isNotifyPhysicalSettle\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isPhysicalSettleDefinition\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"calculationAgentCity\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isISDAFallback\",\"type\":[\"null\",\"string\"],\"default\":null}]},{\"type\":\"record\",\"name\":\"CreditCashSettlementTerms\",\"fields\":[{\"name\":\"valuationDateType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"valuationLag\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.Period\"],\"default\":null},{\"name\":\"calculationAgentCity\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"valuationTimeOfDay\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"valuationMethod\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"quotationMethod\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"quotationAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"minQuotationAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"cashSettlementAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"cashSettlmentCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"cashSettlementLag\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.Period\"],\"default\":null},{\"name", "\":\"cashQuotationAccrualMethod\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"dealers\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"string\"}],\"default\":null}]}],\"default\":null}]}],\"default\":null},{\"name\":\"spIndustrySector\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"creditRating\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"CreditRating\",\"namespace\":\"com.scotia.gcm.sa.v1.marketdata\",\"fields\":[{\"name\":\"ratingSP\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"ratingMoody\",\"type\":[\"null\",\"string\"],\"default\":null}]}],\"default\":null}]}],\"default\":null},{\"name\":\"protectionTerm\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"ProtectionTerms\",\"fields\":[{\"name\":\"creditEvent\",\"type\":{\"type\":\"record\",\"name\":\"CreditEvent\",\"fields\":[{\"name\":\"isBankruptcy\",\"type\":\"string\"},{\"name\":\"isFailuretoPay\",\"type\":\"string\"},{\"name\":\"isRestructuring\",\"type\":\"string\"},{\"name\":\"isObligationToDefault\",\"type\":\"string\"},{\"name\":\"isObligationToAcceleration\",\"type\":\"string\"},{\"name\":\"isRepudiation\",\"type\":\"string\"},{\"name\":\"isGovernmentalIntervention\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isMay112001Restructuring\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isNov092001Convertible\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isNov282001Successor\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isGracePeriodExtened\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"gracePeriod\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.Period\"],\"default\":null},{\"name\":\"failToPayRequirementAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"failToPayRequirementCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"defaultRequirementAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"defaultRequirementCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isFullyRestructingFlag\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isModifiedRestructingFlag\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isMultipleHolderRestructuringFlag\",\"type\":[\"null\",\"string\"],\"default\":null}]}},{\"name\":\"obligation\",\"type\":{\"type\":\"record\",\"name\":\"Obligation\",\"fields\":[{\"name\":\"deliverableObligation\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"deliverableObligationCharacterstics\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"string\"}],\"default\":null},{\"name\":\"maxMaturityTerm\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"obligationCategory\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"obligationCharacterstics\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"string\"}],\"default\":null}]}}]}],\"default\":null},{\"name\":\"creditDefaultSwapMeasure\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"CreditDefaultSwapMeasure\",\"fields\":[{\"name\":\"recoveryCurve\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"recoveryRate\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"survivalProbabilityCurve\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"restructingFactorCurve\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"restructingFactor\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"marketSwapCurve\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"marketSwapLevel\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"discountCurve\",\"type\":[\"null\",\"string\"],\"default\":null}]}],\"default\":null},{\"name\":\"event\",\"type\":[\"null\",\"IrEvents\"],\"default\":null},{\"name\":\"overridePayPeriodDates\",\"type\":[\"null\",{\"type\":\"array\",\"items\":{\"type\":\"int\",\"logicalType\":\"date\"}}],\"default\":null},{\"name\":\"principalAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"principalPayDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"initialNotionalAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"initialNotionalCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"novation\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.Novation\"],\"default\":null}]},\"Swaption\",{\"type\":\"record\",\"name\":\"FXSingleLeg\",\"fields\":[{\"name\":\"isTradeSplitAllocation\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"npvCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"payCurrency\",\"type\":\"string\"},{\"name\":\"payNotional\",\"type\":\"double\"},{\"name\":\"isCollateralFlag\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"payDiscountCurve\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"receiveCurrency\",\"type\":\"string\"},{\"name\":\"receiveNotional\",\"type\":\"double\"},{\"name\":\"receiveDiscountCurve\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"paymentHolidayCenters\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"string\"}],\"default\":null},{\"name\":\"price\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"priceType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"theoreticalModel\",\"type\":\"string\"},{\"name\":\"startDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"endDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"underlyingAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"underlyingCurrencyCode\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"baseAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"baseCurrencyCode\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"trdaeQuoteDisplayPriceText\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"quoteCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"fxRate\",\"type\":\"com.scotia.gcm.sa.v1.common.FxRateQuote\"},{\"name\":\"isSpotTrade\",\"type\":[\"null\",\"boolean\"],\"default\":null},{\"name\":\"swapPoint\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"fixingArea\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isForwardStarting\",\"type\":[\"null\",\"boolean\"],\"default\":null},{\"name\":\"calculatedTrueSpotDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"isPaySideNonDeliverable\",\"type\":[\"null\",\"boolean\"],\"default\":null},{\"name\":\"payDeliveryCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"payNdfReferenceIndex\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"paymentBusinessDayConvention\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isFXRateInverted\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"payNdfResetOffset\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.Period\"],\"default\":null},{\"name\":\"payNdfFxReset\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"FxReset\",\"namespace\":\"com.scotia.gcm.sa.v1.common\",\"fields\":[{\"name\":\"resetDate\",\"type\":{\"type\":\"int\",\"logicalType\":\"date\"}},{\"name\":\"resetRate\",\"type\":\"FxRateQuote\"}]}],\"default\":null},{\"name\":\"paySettlementDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"paySettlementCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isReceiveSideNonDeliverable\",\"type\":[\"null\",\"boolean\"],\"default\":null},{\"name\":\"receiveDeliveryCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"receiveNdfReferenceIndex\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"receiveNdfResetOffset\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.Period\"],\"default\":null},{\"name\":\"receiveNdfFxReset\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.FxReset\"],\"default\":null},{\"name\":\"receiveSettlementDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"receiveSettlementCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"ndfResetHolidayCenters\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"string\"}],\"default\":null},{\"name\":\"isTimeOptionForward\",\"type\":\"boolean\",\"default\":false},{\"name\":\"timeOptionSet\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"TimeOptionFieldSet\",\"fields\":[{\"name\":\"issueDate\",\"type\":{\"type\":\"int\",\"logicalType\":\"date\"}},{\"name\":\"startDate\",\"type\":{\"type\":\"int\",\"logicalType\":\"date\"}},{\"name\":\"endDate\",\"type\":{\"type\":\"int\",\"logicalType\":\"date\"}},{\"name\":\"remainingPayNotional\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"remainingReceiveNotional\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"isDrawdown\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"timeOptionDealNumber\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"optionHolder\",\"type\":[\"null\",\"string\"],\"default\":null}]}],\"default\":null},{\"name\":\"isPartOfFxSwap\",\"type\":\"boolean\",\"default\":false},{\"name\":\"facilityId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"fxSwapSet\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"FxSwapFieldSet\",\"fields\":[{\"name\":\"fxSwapLegType\",\"type\":\"string\"},{\"name\":", "\"swapOtherLegTradeId\",\"type\":\"string\"}]}],\"default\":null},{\"name\":\"events\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"FxEvents\",\"fields\":[{\"name\":\"effectiveDate\",\"type\":{\"type\":\"int\",\"logicalType\":\"date\"}},{\"name\":\"terminationDate\",\"type\":{\"type\":\"int\",\"logicalType\":\"date\"}},{\"name\":\"adjustedEffectiveDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"adjustedTerminationDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"contractExpiryPeriod\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"tenorBusinessPeriod\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.Period\"],\"default\":null},{\"name\":\"contractBreaks\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"com.scotia.gcm.sa.v1.common.ContractBreak\"}],\"default\":null},{\"name\":\"contractBreakAttributes\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.ContractBreakAttributes\"],\"default\":null}]}],\"default\":null},{\"name\":\"floatingRateCalculation\",\"type\":[\"null\",{\"type\":\"array\",\"items\":{\"type\":\"record\",\"name\":\"FloatingRateCalculation\",\"namespace\":\"com.scotia.gcm.sa.v1.common\",\"fields\":[{\"name\":\"floatingRateIndex\",\"type\":\"string\"},{\"name\":\"floatingRateIndexCode\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"rateName\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"floatingRateIndexTenor\",\"type\":[\"null\",\"Period\"],\"default\":null},{\"name\":\"spreadInitialValue\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"spreadScheduleSteps\",\"type\":[\"null\",{\"type\":\"array\",\"items\":{\"type\":\"record\",\"name\":\"StepScheduleElement\",\"fields\":[{\"name\":\"stepDate\",\"type\":{\"type\":\"int\",\"logicalType\":\"date\"}},{\"name\":\"stepValue\",\"type\":\"double\"}]}}],\"default\":null},{\"name\":\"resetFrequency\",\"type\":[\"null\",\"Period\"],\"default\":null},{\"name\":\"calculatedRates\",\"type\":[\"null\",{\"type\":\"array\",\"items\":{\"type\":\"record\",\"name\":\"CalculatedRateElement\",\"fields\":[{\"name\":\"rateEffectiveDate\",\"type\":{\"type\":\"int\",\"logicalType\":\"date\"}},{\"name\":\"calculatedRate\",\"type\":[\"null\",\"double\"],\"default\":null}]}}],\"default\":null}]}}],\"default\":null},{\"name\":\"novation\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.Novation\"],\"default\":null}]},{\"type\":\"record\",\"name\":\"FXStrip\",\"fields\":[{\"name\":\"npvCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"payCurrency\",\"type\":\"string\"},{\"name\":\"payNotional\",\"type\":\"double\"},{\"name\":\"isCollateralFlag\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"payNotionalSchedule\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"com.scotia.gcm.sa.v1.common.NotionalSchedule\"}],\"default\":null},{\"name\":\"payDiscountCurve\",\"type\":\"string\"},{\"name\":\"receiveCurrency\",\"type\":\"string\"},{\"name\":\"receiveNotional\",\"type\":\"double\"},{\"name\":\"receiveNotionalSchedule\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"com.scotia.gcm.sa.v1.common.NotionalSchedule\"}],\"default\":null},{\"name\":\"receiveDiscountCurve\",\"type\":\"string\"},{\"name\":\"fxLongFormSchedule\",\"type\":[\"null\",{\"type\":\"array\",\"items\":{\"type\":\"record\",\"name\":\"FxLongForm\",\"fields\":[{\"name\":\"paymentDate\",\"type\":{\"type\":\"int\",\"logicalType\":\"date\"}},{\"name\":\"payNotional\",\"type\":\"double\"},{\"name\":\"receiveNotional\",\"type\":\"double\"},{\"name\":\"exchangeRate\",\"type\":\"double\"},{\"name\":\"ndfFxResetDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null}]}}],\"default\":null},{\"name\":\"theoreticalModel\",\"type\":\"string\"},{\"name\":\"fxRate\",\"type\":\"com.scotia.gcm.sa.v1.common.FxRateQuote\"},{\"name\":\"isSpotTrade\",\"type\":[\"null\",\"boolean\"],\"default\":null},{\"name\":\"isForwardStarting\",\"type\":[\"null\",\"boolean\"],\"default\":null},{\"name\":\"isPaySideNonDeliverable\",\"type\":[\"null\",\"boolean\"],\"default\":null},{\"name\":\"payDeliveryCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"payNdfReferenceIndex\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"payNdfResetOffset\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.Period\"],\"default\":null},{\"name\":\"payNdfFxResets\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"com.scotia.gcm.sa.v1.common.FxReset\"}],\"default\":null},{\"name\":\"isReceiveSideNonDeliverable\",\"type\":[\"null\",\"boolean\"],\"default\":null},{\"name\":\"receiveDeliveryCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"receiveNdfReferenceIndex\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"receiveNdfResetOffset\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.Period\"],\"default\":null},{\"name\":\"receiveNdfFxResets\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"com.scotia.gcm.sa.v1.common.FxReset\"}],\"default\":null},{\"name\":\"firstPaymentDate\",\"type\":{\"type\":\"int\",\"logicalType\":\"date\"}},{\"name\":\"paymentFrequency\",\"type\":\"com.scotia.gcm.sa.v1.common.Period\"},{\"name\":\"paymentHolidayCenters\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"string\"}],\"default\":null},{\"name\":\"paymentBusinessDayConvention\",\"type\":\"string\"},{\"name\":\"paymentOffset\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.Period\"],\"default\":null},{\"name\":\"dayType\",\"type\":\"string\"},{\"name\":\"ndfResetHolidayCenters\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"string\"}],\"default\":null},{\"name\":\"events\",\"type\":[\"null\",\"FxEvents\"],\"default\":null}]},{\"type\":\"record\",\"name\":\"FXOption\",\"fields\":[{\"name\":\"buySell\",\"type\":\"string\"},{\"name\":\"underlyingInstrumentName\",\"type\":\"string\"},{\"name\":\"baseCurrency\",\"type\":\"string\"},{\"name\":\"quoteCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"payOutCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"putCurrencyAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"putCurrencyUnwoundAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"putCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"callCurrencyAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"callCurrencyUnwoundAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"callCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"premiumPaymentDate\",\"type\":{\"type\":\"int\",\"logicalType\":\"date\"}},{\"name\":\"premiumPaymentAmount\",\"type\":\"double\"},{\"name\":\"premiumPaymentCurrency\",\"type\":\"string\"},{\"name\":\"strikeRate\",\"type\":\"double\"},{\"name\":\"strikeQuoteBasis\",\"type\":\"string\"},{\"name\":\"payOffAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"payOffUnwoundAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"payOffCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"cutoff\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"startDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"endDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"facilityId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isDigital\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"optionType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"underlyingAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"underlyingCurrencyCode\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"baseAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"baseCurrencyCode\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"tradeQuoteDisplayPriceText\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"initialPremiumPaymentAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"initialPremiumPaymentCurrencyCode\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"tradedCurrencyCode\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"displayFormatPrice\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"hedgeCurrencyCode\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"initialPremiumPaymentDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"premiumPaymentDirectionType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"proxyCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"proxyNominalAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"exerciseStyle\",\"type\":{\"type\":\"record\",\"name\":\"ExerciseStyle\",\"fields\":[{\"name\":\"optionExerciseStyle\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"effectiveDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"expiryDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"valueDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"contractExpiryPeriod\",\"type\":[\"null\",\"string\"],\"default\":null}]}},{\"name\":\"barrierFeature\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"BarrierFeature\",\"fields\":[{\"name\":\"knockTyp", "e\",\"type\":\"string\"},{\"name\":\"barrierExerciseStyle\",\"type\":\"string\"},{\"name\":\"startDate\",\"type\":{\"type\":\"int\",\"logicalType\":\"date\"}},{\"name\":\"endDate\",\"type\":{\"type\":\"int\",\"logicalType\":\"date\"}},{\"name\":\"triggerRate\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"barrierUpRate\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"barrierDownRate\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"tarfBarrierType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"rebateType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"rebateAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"rebateCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"knockInUpRatioPercent\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"knockInMidRatioPercent\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"knockInLowRatioPercent\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"barrierType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"discreteFeature\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"DiscreteFeature\",\"fields\":[{\"name\":\"rateInformationSourceName\",\"type\":\"string\"},{\"name\":\"startDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"endDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"calculationFrequencyType\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.Period\"],\"default\":null}]}],\"default\":null},{\"name\":\"digitalFeature\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"DigitalFeature\",\"fields\":[{\"name\":\"digitalRatePercentage\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"digitalAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"digitalAmountCurrency\",\"type\":\"string\"}]}],\"default\":null}]}],\"default\":null},{\"name\":\"touchOptionFeature\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"TouchOptionFeature\",\"fields\":[{\"name\":\"paymentAt\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"barrierQuoteCurrencyPair\",\"type\":[\"null\",\"string\"],\"default\":null}]}],\"default\":null},{\"name\":\"asianFeature\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"AsianFeature\",\"fields\":[{\"name\":\"startDate\",\"type\":{\"type\":\"int\",\"logicalType\":\"date\"}},{\"name\":\"endDate\",\"type\":{\"type\":\"int\",\"logicalType\":\"date\"}},{\"name\":\"rateInformationSource\",\"type\":\"string\"},{\"name\":\"calculationFrequencyType\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.Period\"],\"default\":null},{\"name\":\"strikeType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"modelType\",\"type\":[\"null\",\"string\"],\"default\":null}]}],\"default\":null},{\"name\":\"fixingObservation\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"FixingObservation\",\"namespace\":\"com.scotia.gcm.sa.v1.common\",\"fields\":[{\"name\":\"observationDate\",\"type\":{\"type\":\"int\",\"logicalType\":\"date\"}},{\"name\":\"observationRate\",\"type\":\"double\"},{\"name\":\"averageRateWeightingFactor\",\"type\":[\"null\",\"string\"]}]}],\"default\":null},{\"name\":\"fxOptionMeasures\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"FxOptionMeasures\",\"fields\":[{\"name\":\"volatilitySurface\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"discountCurve\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"foreignIRCurve\",\"type\":[\"null\",\"string\"],\"default\":null}]}],\"default\":null},{\"name\":\"windowDelivery\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"WindowDelivery\",\"fields\":[{\"name\":\"startDate\",\"type\":{\"type\":\"int\",\"logicalType\":\"date\"}},{\"name\":\"endDate\",\"type\":{\"type\":\"int\",\"logicalType\":\"date\"}},{\"name\":\"underlyingOwner\",\"type\":\"string\"}]}],\"default\":null},{\"name\":\"flexAccumulatorFeature\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"FlexAccumulatorFeature\",\"fields\":[{\"name\":\"fadeType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"payOutType\",\"type\":\"string\"},{\"name\":\"scheduleModeType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"startDate\",\"type\":{\"type\":\"int\",\"logicalType\":\"date\"}},{\"name\":\"endDate\",\"type\":{\"type\":\"int\",\"logicalType\":\"date\"}},{\"name\":\"expiryDate\",\"type\":{\"type\":\"int\",\"logicalType\":\"date\"}},{\"name\":\"archivingGroupLabelName\",\"type\":\"string\"},{\"name\":\"numberOfFixingsCount\",\"type\":\"double\"},{\"name\":\"upRatioPercent\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"midRatioPercent\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"lowRatioPercent\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"isKeepAccrued\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"lowRangeValue\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"highRangeValue\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"weight\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"calculationFrequencyType\",\"type\":\"com.scotia.gcm.sa.v1.common.Period\"},{\"name\":\"autocallable\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"AutoCallable\",\"fields\":[{\"name\":\"autoBarrierStyle\",\"type\":\"string\"},{\"name\":\"autoBarrierLevel\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"observationDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"deliveryDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null}]}],\"default\":null}]}],\"default\":null},{\"name\":\"flexTARFFeature\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"FlexTARFFeature\",\"fields\":[{\"name\":\"targetAmount\",\"type\":\"double\"},{\"name\":\"targetQuotationCurrencyPair\",\"type\":\"string\"},{\"name\":\"targetStyle\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"scheduleModeType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"maturityRebateType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"crossTargetType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"totalNominalAmount\",\"type\":\"double\"},{\"name\":\"startDate\",\"type\":{\"type\":\"int\",\"logicalType\":\"date\"}},{\"name\":\"endDate\",\"type\":{\"type\":\"int\",\"logicalType\":\"date\"}},{\"name\":\"expiryDate\",\"type\":{\"type\":\"int\",\"logicalType\":\"date\"}},{\"name\":\"archivingGroupLabelName\",\"type\":\"string\"},{\"name\":\"numberOfFixingsCount\",\"type\":\"double\"},{\"name\":\"upRatioPercent\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"midRatioPercent\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"lowRatioPercent\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"deliveryAdjustmentMethod\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"remainingTargetAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"lowRangeCategory\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"highRangeCategory\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isStrikeByRangeIndicator\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isNominalConstantIndicator\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"upStrikeModeType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"midStrikeModeType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"lowStrikeModeType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"upStrikeModeDigitalQuotationCurrencyPair\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"midStrikeModeDigitalQuotationCurrencyPair\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"lowStrikeModeDigitalQuotationCurrencyPair\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"totalRemainingNominalAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"itmCriteria\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"nominalByIndicator\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"capLossType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"capAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"capQuoteType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"maxLossCurrencyCode\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"pivotRebate\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"calculationFrequencyType\",\"type\":\"com.scotia.gcm.sa.v1.common.Period\"}]}],\"default\":null},{\"name\":\"pivotTARFFeature\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"PivotTARFFeature\",\"fields\":[{\"name\":\"pivotStyle\",\"type\":\"string\"},{\"name\":\"scheduleValueType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"pivotPointRate\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"pivotBoundaryType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"buyOutOfTheMoneyPercentage\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"sellOutOfTheMoneyPercentage\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"buyStrikeRate\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"sellStrikeRate\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"buySellStrikeBoundType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"digitalQuoteType\",\"type\":[\"null\",\"s", "tring\"],\"default\":null},{\"name\":\"knockInBarrier\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"KnockInBarrier\",\"fields\":[{\"name\":\"knockInBarrierType\",\"type\":\"string\"},{\"name\":\"downKnockInBarrierAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"upKnockInBarrierAmount\",\"type\":[\"null\",\"double\"],\"default\":null}]}],\"default\":null}]}],\"default\":null},{\"name\":\"historicalFixingObservations\",\"type\":[\"null\",{\"type\":\"array\",\"items\":{\"type\":\"record\",\"name\":\"HistoricalFixingObservations\",\"namespace\":\"com.scotia.gcm.sa.v1.common\",\"fields\":[{\"name\":\"observationDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"observationRate\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"averageRateWeightingFactor\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"nominalPerFixingAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"lowRangeValue\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"highRangeValue\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"observationPaymentDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"underlyingOptionEndDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"optionEffectiveDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"lowTARFStrikePriceValue\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"highTARFStrikePriceValue\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"midTARFStrikePriceValue\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"upRatioPercent\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"midRatioPercent\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"lowRatioPercent\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"upCalculatedNotional\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"midCalculatedNotional\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"lowCalculatedNotional\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"upTARFStrikeSlopeValue\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"midTARFStrikeSlopeValue\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"lowTARFStrikeSlopeValue\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"buyOutOfTheMoneyWeightAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"sellOutOfTheMoneyWeightAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"pivotLevel\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"buyStrikePrice\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"sellStrikePrice\",\"type\":[\"null\",\"double\"],\"default\":null}]}}],\"default\":null},{\"name\":\"events\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"events\",\"namespace\":\"com.scotia.gcm.sa.v1.common\",\"fields\":[{\"name\":\"eventFeeAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"eventFeeCurrencyCode\",\"type\":[\"null\",\"string\"],\"default\":null}]}],\"default\":null},{\"name\":\"kikoFeature\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"KikoFeature\",\"fields\":[{\"name\":\"kikoStyle\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"transatlantic\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"knockInStyle\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"knockInTriggerRate\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"knockInBarrierStyle\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"knockInStartDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"knockInEndDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"knockInRebateAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"knockOutStyle\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"knockOutTriggerRate\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"knockOutBarrierStyle\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"knockOutStartDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"knockOutEndDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"knockOutRebateAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"knockInRebateCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"knockInRebateFormFactor\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"knockOutRebateCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"knockOutRebateFormFactor\",\"type\":[\"null\",\"double\"],\"default\":null}]}],\"default\":null}]},\"IRSwap\",{\"type\":\"record\",\"name\":\"FRA\",\"fields\":[{\"name\":\"buySell\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"npvCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"notionalCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"notional\",\"type\":\"double\"},{\"name\":\"strike\",\"type\":\"double\"},{\"name\":\"referenceIndex\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"sourceForwardCurve\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"sourceDiscountCurve\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"theoreticalModel\",\"type\":\"string\"},{\"name\":\"indexFraction\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"nextResetDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"resetRates\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"com.scotia.gcm.sa.v1.common.Reset\"}],\"default\":null},{\"name\":\"referenceIndexTenor\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.Period\"],\"default\":null},{\"name\":\"resetHolidayCenters\",\"type\":{\"type\":\"array\",\"items\":\"string\"}},{\"name\":\"resetOffset\",\"type\":\"com.scotia.gcm.sa.v1.common.Period\"},{\"name\":\"isResetInAdvance\",\"type\":\"boolean\"},{\"name\":\"paymentDayCount\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"paymentHolidayCenters\",\"type\":{\"type\":\"array\",\"items\":\"string\"}},{\"name\":\"paymentBusinessDayConvention\",\"type\":\"string\"},{\"name\":\"paymentOffset\",\"type\":\"com.scotia.gcm.sa.v1.common.Period\"},{\"name\":\"isPaymentInAdvance\",\"type\":\"boolean\"},{\"name\":\"periodHolidayCenters\",\"type\":{\"type\":\"array\",\"items\":\"string\"}},{\"name\":\"events\",\"type\":[\"null\",\"IrEvents\"],\"default\":null},{\"name\":\"novation\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.Novation\"],\"default\":null}]},{\"type\":\"record\",\"name\":\"CapFloor\",\"fields\":[{\"name\":\"buySell\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"optionType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isCollateralFlag\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"capRateBuyerPartyId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"capRateSellerPartyId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"floorRateBuyerPartyId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"floorRateSellerPartyId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isMultiplePaymentDaysFlag\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"interestPaymentMonths\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"string\"}],\"default\":null},{\"name\":\"nextResetDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"interestResetMonths\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"string\"}],\"default\":null},{\"name\":\"paymentDates\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"com.scotia.gcm.sa.v1.common.NotionalSchedule\"}],\"default\":null},{\"name\":\"resetDates\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"com.scotia.gcm.sa.v1.common.Reset\"}],\"default\":null},{\"name\":\"calculationPeriodAmountParameters\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"CalculationPeriodAmountParameters\",\"fields\":[{\"name\":\"dayCount\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"notionalCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"interpolationIndicator\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"firstPeriodIndexFrequencyType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"lastPeriodIndexFrequencyType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"firstPeriodResetDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"lastPeriodResetDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"notionalSchedule\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"com.scotia.gcm.sa.v1.common.NotionalSchedule\"}],\"default\":null},{\"name\":\"notionalAmortizationDetails\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.NotionalAmortizationDetails\"],\"default\":null},{\"name\":\"floatingRateIndex\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"indexTenor\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.Period\"],\"default\":null},{\"name\":\"capRateSchedule\",\"type\":[\"null\",{\"t", "ype\":\"array\",\"items\":{\"type\":\"record\",\"name\":\"RateSchedule\",\"fields\":[{\"name\":\"initialValue\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"steps\",\"type\":[\"null\",{\"type\":\"array\",\"items\":{\"type\":\"record\",\"name\":\"Step\",\"fields\":[{\"name\":\"stepDate\",\"type\":{\"type\":\"int\",\"logicalType\":\"date\"}},{\"name\":\"stepValue\",\"type\":\"double\"}]}}],\"default\":null}]}}],\"default\":null},{\"name\":\"floorRateSchedule\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"RateSchedule\"}],\"default\":null},{\"name\":\"notionalType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"dayType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"stubType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"stubDayCount\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"stubTypeAdjustedDates\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.StubTypeAdjustedDates\"],\"default\":null},{\"name\":\"isAverage\",\"type\":[\"null\",\"boolean\"],\"default\":null},{\"name\":\"indexFraction\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"referenceIndex\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"referenceIndexDayCount\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"indexType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"compoundingMethod\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"overnightIndex\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.OvernightIndex\"],\"default\":null},{\"name\":\"isArrearsConvexityAdj\",\"type\":[\"null\",\"boolean\"],\"default\":null},{\"name\":\"swapLongForm\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"com.scotia.gcm.sa.v1.common.SwapLongForm\"}],\"default\":null},{\"name\":\"strikeRateSchedule\",\"type\":[\"null\",\"RateSchedule\"],\"default\":null},{\"name\":\"swapLongFormEditedFields\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"string\"}],\"default\":null}]}],\"default\":null},{\"name\":\"premiumAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"premiumCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"capfloorPremium\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"Premium\"}],\"default\":null},{\"name\":\"events\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"CapFloorEvents\",\"fields\":[{\"name\":\"effectiveDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"terminationDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"adjustedEffectiveDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"adjustedTerminationDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"calculationPeriodDatesAdjustments\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"calculationPeriodFrequency\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.Period\"],\"default\":null},{\"name\":\"paymentFrequency\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.Period\"],\"default\":null},{\"name\":\"resetFrequency\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.Period\"],\"default\":null}]}],\"default\":null},{\"name\":\"upfrontFee\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.Fee\"],\"default\":null},{\"name\":\"fees\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"com.scotia.gcm.sa.v1.common.FeeSchedule\"}],\"default\":null},{\"name\":\"additionalTerms\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"CapFloorAdditionalTerms\",\"fields\":[{\"name\":\"cpiIndexName\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"cpiIsin\",\"type\":[\"null\",\"string\"],\"default\":null}]}],\"default\":null},{\"name\":\"additionalPayment\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"CapFloorAdditionalPayment\",\"fields\":[{\"name\":\"binaryPay\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"binaryPayType\",\"type\":[\"null\",\"string\"],\"default\":null}]}],\"default\":null},{\"name\":\"isMultipleExercise\",\"type\":[\"null\",\"boolean\"],\"default\":null},{\"name\":\"numberOfExercise\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"capfloorMeasures\",\"type\":[\"null\",\"Measures\"],\"default\":null},{\"name\":\"capfloorStream\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"CapfloorStream\",\"fields\":[{\"name\":\"calculationPeriodDates\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"CalculationPeriodDates\",\"fields\":[{\"name\":\"calculationPeriodDatesAdjustments\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"CalculationPeriodDatesAdjustments\",\"fields\":[{\"name\":\"paymentDaysOffsetConvention\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"dateAdjustType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"paymentHolidayCenters\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"string\"}],\"default\":null},{\"name\":\"isPaymentInAdvance\",\"type\":\"boolean\"},{\"name\":\"isPaymentDiscounted\",\"type\":\"boolean\"},{\"name\":\"paymentDaysOffset\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.Period\"],\"default\":null},{\"name\":\"resetOffset\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.Period\"],\"default\":null},{\"name\":\"resetHolidayCenters\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"string\"}],\"default\":null},{\"name\":\"businessDateAdjustmentMethod\",\"type\":[\"null\",\"string\"],\"default\":null}]}],\"default\":null},{\"name\":\"firstPaymentDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"firstRollDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"calculationPeriodFrequency\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"CalculationPeriodFrequency\",\"fields\":[{\"name\":\"averageFrequency\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.Period\"],\"default\":null},{\"name\":\"isWeightedAvg\",\"type\":[\"null\",\"boolean\"],\"default\":null},{\"name\":\"specialPayPeriod\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.Period\"],\"default\":null},{\"name\":\"specialResetPeriod\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.Period\"],\"default\":null},{\"name\":\"specialIndexPeriod\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.Period\"],\"default\":null}]}],\"default\":null}]}],\"default\":null}]}],\"default\":null},{\"name\":\"isAccrualAccounting\",\"type\":[\"null\",\"boolean\"],\"default\":null},{\"name\":\"isCashflowOnValuationDay\",\"type\":[\"null\",\"boolean\"],\"default\":null},{\"name\":\"theoreticalModel\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"initialNotionalAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"initialNotionalCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isIncludedInCCR\",\"type\":[\"null\",\"boolean\"],\"default\":null},{\"name\":\"cmsterm\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"npvCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"periodicSpread\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"periodicStrikeReset\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"isFixReset\",\"type\":[\"null\",\"boolean\"],\"default\":null},{\"name\":\"novation\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.Novation\"],\"default\":null}]},{\"type\":\"record\",\"name\":\"Equity\",\"fields\":[{\"name\":\"buySell\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"buySellQualifier\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"stockType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"currency\",\"type\":\"string\"},{\"name\":\"settlementCurrency\",\"type\":\"string\"},{\"name\":\"quantity\",\"type\":\"double\"},{\"name\":\"price\",\"type\":\"double\"},{\"name\":\"cleanPrice\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"netPrice\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"principalAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"tradeToSettlementRate\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"tradeToSettlementOperation\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"ask\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"bid\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"specialCreditIndicatorCode\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"marketPrice\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"marketValue\",\"type\":[\"null\",\"int\"],\"default\":null},{\"name\":\"crossTargetType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isCrossComplementIndicator\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"fxRate\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.FxRateQuote\"],\"default\":null},{\"name\":\"events\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"EquityEvents\",\"fields\":[]}],\"default\":null},{\"name\":\"fixAttributes\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.FixAttributes\"],\"default\":null}]},{\"type\":\"record\",\"name\":\"ConvertibleBond\",\"fields\":[{\"name\":\"buySell\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"currency\",\"type\":\"string\"},{\"name\":\"settlementCurrency\",\"type\":\"string\"},{\"name\":\"quantity\",\"type\":\"double\"},{\"name\":\"price\",\"type\":\"double\"},{\"name\":\"cleanPrice\",\"type", "\":[\"null\",\"double\"],\"default\":null},{\"name\":\"netPrice\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"accruedInterest\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"principalAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"tradeToSettlementRate\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"tradeToSettlementOperation\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"netAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"events\",\"type\":[\"null\",\"EquityEvents\"],\"default\":null}]},{\"type\":\"record\",\"name\":\"EquityReturnSwap\",\"fields\":[{\"name\":\"productType\",\"type\":\"string\"},{\"name\":\"isBnsPayingAsset\",\"type\":\"boolean\"},{\"name\":\"isCrossCurrencySwapIndicator\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"returnLeg\",\"type\":{\"type\":\"record\",\"name\":\"ReturnLeg\",\"fields\":[{\"name\":\"assetResetLag\",\"type\":\"com.scotia.gcm.sa.v1.common.Period\"},{\"name\":\"underlyer\",\"type\":[{\"type\":\"record\",\"name\":\"SingleUnderlyer\",\"fields\":[{\"name\":\"equityExchangeSymbol\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"equityExchange\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"equityIsin\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"openUnits\",\"type\":\"long\"},{\"name\":\"dividendPayoutRatio\",\"type\":\"double\"},{\"name\":\"initialPrice\",\"type\":{\"type\":\"record\",\"name\":\"Quote\",\"fields\":[{\"name\":\"price\",\"type\":\"double\"},{\"name\":\"priceCurrency\",\"type\":\"string\"},{\"name\":\"priceExpression\",\"type\":[\"null\",\"string\"],\"default\":null}]}},{\"name\":\"equityPriceResetSchedule\",\"type\":{\"type\":\"record\",\"name\":\"EquityPriceResetSchedule\",\"fields\":[{\"name\":\"priceResetList\",\"type\":[\"null\",{\"type\":\"array\",\"items\":{\"type\":\"record\",\"name\":\"EquityPriceReset\",\"fields\":[{\"name\":\"resetPrice\",\"type\":\"double\"},{\"name\":\"resetDate\",\"type\":{\"type\":\"int\",\"logicalType\":\"date\"}}]}}],\"default\":null},{\"name\":\"priceCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"priceExpression\",\"type\":[\"null\",\"string\"],\"default\":null}]}},{\"name\":\"instrumentId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"longShort\",\"type\":[\"null\",\"string\"],\"default\":null}]},{\"type\":\"record\",\"name\":\"Basket\",\"fields\":[{\"name\":\"basketConstituentList\",\"type\":{\"type\":\"array\",\"items\":\"SingleUnderlyer\"}}]}]},{\"name\":\"rateOfReturn\",\"type\":{\"type\":\"record\",\"name\":\"RateOfReturn\",\"fields\":[{\"name\":\"isNotionalReset\",\"type\":\"boolean\"},{\"name\":\"calculationPeriodFrequency\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.Period\"],\"default\":null},{\"name\":\"calcualtionPeriodRollConvention\",\"type\":\"string\"},{\"name\":\"valuationDates\",\"type\":{\"type\":\"array\",\"items\":{\"type\":\"int\",\"logicalType\":\"date\"}}},{\"name\":\"paymentDatesInterimOffset\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.Period\"],\"default\":null}]}},{\"name\":\"notionalAmount\",\"type\":\"double\"},{\"name\":\"notionalCurrency\",\"type\":\"string\"},{\"name\":\"returnType\",\"type\":\"string\"},{\"name\":\"dividendConditions\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"DividendConditions\",\"fields\":[{\"name\":\"isDividendReinvested\",\"type\":\"boolean\"},{\"name\":\"dividendEntitlement\",\"type\":\"string\"},{\"name\":\"dividendPaymentSchedule\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.NotionalSchedule\"],\"default\":null}]}],\"default\":null},{\"name\":\"notionalAdjustments\",\"type\":[\"null\",\"string\"],\"default\":null}]}},{\"name\":\"interestLeg\",\"type\":\"com.scotia.gcm.sa.v1.common.InterestLeg\"},{\"name\":\"earlyTerminationStartingDates\",\"type\":[\"null\",{\"type\":\"array\",\"items\":{\"type\":\"record\",\"name\":\"EarlyTerminationStartingDate\",\"fields\":[{\"name\":\"party\",\"type\":\"com.scotia.gcm.sa.v1.common.Party\"},{\"name\":\"startingDate\",\"type\":{\"type\":\"int\",\"logicalType\":\"date\"}}]}}],\"default\":null},{\"name\":\"events\",\"type\":{\"type\":\"record\",\"name\":\"EquitySwapEvents\",\"fields\":[{\"name\":\"effectiveDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"terminationDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"swapStartDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"swapEndDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"contractBreaks\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"com.scotia.gcm.sa.v1.common.ContractBreak\"}],\"default\":null},{\"name\":\"extraordinaryEvents\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"ExtraordinaryEvents\",\"fields\":[{\"name\":\"mergerEventShareForShare\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"mergerEventShareForOther\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"mergerEventShareForCombined\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"tenderOfferEventShareForShare\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"tenderOfferEventShareForOther\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"tenderOfferEventShareForCombined\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"determiningPartyForDisruption\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.Party\"],\"default\":null},{\"name\":\"isChangeInLawApplicable\",\"type\":[\"null\",\"boolean\"],\"default\":null},{\"name\":\"isFailureToDeliverApplicable\",\"type\":[\"null\",\"boolean\"],\"default\":null},{\"name\":\"isHedgingDisruptionApplicable\",\"type\":[\"null\",\"boolean\"],\"default\":null},{\"name\":\"isNonRelianceApplicable\",\"type\":[\"null\",\"boolean\"],\"default\":null},{\"name\":\"isHedgingAgreementsApplicable\",\"type\":[\"null\",\"boolean\"],\"default\":null}]}],\"default\":null}]}}]},{\"type\":\"record\",\"name\":\"Future\",\"fields\":[{\"name\":\"buySell\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"price\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"priceCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isCloseout\",\"type\":[\"null\",\"boolean\"],\"default\":null},{\"name\":\"notionalCurrency\",\"type\":\"string\"},{\"name\":\"notional\",\"type\":\"double\"},{\"name\":\"initialNotionalAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"initialNotionalCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"lotSize\",\"type\":\"double\"},{\"name\":\"quantity\",\"type\":\"double\"},{\"name\":\"quantityUnit\",\"type\":\"string\"},{\"name\":\"totalQuantity\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"venueContractCode\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"venueContractName\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"venueContractType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"startDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"endDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"isSpotPosition\",\"type\":[\"null\",\"boolean\"],\"default\":null},{\"name\":\"facilityId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"metalType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"metalVault\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"specialCreditIndicatorCode\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"fxRate\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.FxRateQuote\"],\"default\":null},{\"name\":\"quote\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"FutQuote\",\"fields\":[{\"name\":\"price\",\"type\":\"double\"},{\"name\":\"priceCurrency\",\"type\":\"string\"},{\"name\":\"priceNotation\",\"type\":\"string\"},{\"name\":\"priceType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"unitOfMeasure\",\"type\":[\"null\",\"string\"],\"default\":null}]}],\"default\":null},{\"name\":\"events\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"FutEvents\",\"fields\":[{\"name\":\"maturityLabel\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"tradeExpiryDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"deliveryFirstNoticeDay\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"deliveryLastTradingDay\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null}]}],\"default\":null},{\"name\":\"index\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"ComIndex\",\"fields\":[{\"name\":\"sourceIndexLabel\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"sourceIndexDescription\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"indexTerm\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isdaInstrumentName\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"deliveryVenue\",\"type\":[\"null\",\"string\"],\"default\":null}]}],\"default\":null},{\"name\":\"counterpartyAccountId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"settlementInfo\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"Settlement\",\"namespace\":\"com.scotia.gcm.sa.v1.common\",\"fields\":[{\"name\":\"settlementExcludingAccrual", "CommissionAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"settlementIncludingAccrualCommissionAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"isCrossCurrencySettled\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"fxRateSettlement\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"isFXRateInverted\",\"type\":[\"null\",\"string\"],\"default\":null}]}],\"default\":null},{\"name\":\"assetInfo\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"AssetInfo\",\"namespace\":\"com.scotia.gcm.sa.v1.common\",\"fields\":[{\"name\":\"assetType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"assetSubType\",\"type\":[\"null\",\"string\"],\"default\":null}]}],\"default\":null},{\"name\":\"tradeSplitInfo\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"TradeSplitInfo\",\"namespace\":\"com.scotia.gcm.sa.v1.common\",\"fields\":[{\"name\":\"isTradeSplitAllocation\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"tradeSplitId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"tradeSplitPercentage\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"tradeSplitStatus\",\"type\":[\"null\",\"string\"],\"default\":null}]}],\"default\":null},{\"name\":\"isSourceExternal\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isOrderAggressed\",\"type\":[\"null\",\"string\"],\"default\":null}]},{\"type\":\"record\",\"name\":\"ComOption\",\"fields\":[{\"name\":\"buySell\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"notionalCurrency\",\"type\":\"string\"},{\"name\":\"notional\",\"type\":\"double\"},{\"name\":\"initialNotionalAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"initialNotionalCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"optionExerciseStyle\",\"type\":\"string\"},{\"name\":\"optionExpiryDate\",\"type\":{\"type\":\"int\",\"logicalType\":\"date\"}},{\"name\":\"startDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"endDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"optionType\",\"type\":\"string\"},{\"name\":\"strikePrice\",\"type\":\"double\"},{\"name\":\"strikePriceCurrency\",\"type\":\"string\"},{\"name\":\"optionPremium\",\"type\":\"double\"},{\"name\":\"optionPremiumCurrency\",\"type\":[\"null\",\"string\"]},{\"name\":\"optionPremiumSettlementDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}]},{\"name\":\"upfrontPayment\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"upfrontPaymentCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"quantity\",\"type\":\"double\"},{\"name\":\"quantityUnit\",\"type\":\"string\"},{\"name\":\"totalQuantity\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"strikePriceNotation\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"lotSize\",\"type\":[\"null\",\"int\"],\"default\":null},{\"name\":\"venueContractCode\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"venueContractName\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"venueContractType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"contractStartDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"contractEndDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"isSpotPosition\",\"type\":[\"null\",\"boolean\"],\"default\":null},{\"name\":\"optionDelta\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"isBumpRelativeIndicator\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isCashSettledIndicator\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"underlyerInstrument\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"UnderlyerInstrument\",\"fields\":[{\"name\":\"sourceInstrumentName\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"comSwap\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"ComSwap\",\"fields\":[{\"name\":\"buySell\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"quantity\",\"type\":\"double\"},{\"name\":\"quantityUnit\",\"type\":\"string\"},{\"name\":\"totalQuantity\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"lotSize\",\"type\":\"double\"},{\"name\":\"facilityId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"startDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"endDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"leg1\",\"type\":{\"type\":\"record\",\"name\":\"ComLeg\",\"fields\":[{\"name\":\"legType\",\"type\":\"string\"},{\"name\":\"legName\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"swapletWeight\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"feeAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"paymentCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"fixedPricecurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isZeroReset\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"foreignExchangeCurveName\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"crossCurrencyType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"fxPeriodDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"sourceForwardCurve\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"sourceForwardCurveName\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"sourceDiscountCurve\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"sourceDiscountCurveName\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"sourceMinorDiscountCurveName\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"historicalResetCurveName\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"fxResetCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"scotiaSide\",\"type\":\"string\"},{\"name\":\"index\",\"type\":[\"null\",\"ComIndex\"],\"default\":null},{\"name\":\"notionalCurrency\",\"type\":\"string\"},{\"name\":\"notional\",\"type\":\"double\"},{\"name\":\"fixedRate\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"fixedRateQuotation\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"fxPricingDateName\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"periodsSchedule\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.Period\"],\"default\":null},{\"name\":\"fixingObservation\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.FixingObservation\"],\"default\":null},{\"name\":\"swapStartDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"swapEndDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"margin\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"marginPercentage\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"swapCashFlow\",\"type\":[\"null\",{\"type\":\"array\",\"items\":{\"type\":\"record\",\"name\":\"SwapCashFlow\",\"fields\":[{\"name\":\"calculationStartDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"calculationEndDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"quantity\",\"type\":\"double\"},{\"name\":\"quantityUnitOfMeasure\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"firstFixingDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"lastFixingDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"numberOfFixingDates\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"price\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"priceIncludingMargin\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"paymentDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"notionalAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"notionalCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"spread\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"strikePrice\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"strikePriceCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"strikePriceUnitOfMeasure\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"quantityUnitDetail\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"QuantityUnitDetail\",\"fields\":[{\"name\":\"periodUnit\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"dailyUnit\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"unitSchedule\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"customDailyUnits\",\"type\":[\"null\",{\"type\":\"array\",\"items\":{\"type\":\"record\",\"name\":\"CustomDailyUnit\",\"fields\":[{\"name\":\"unitDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"customUnit\",\"type\":[\"null\",\"double\"],\"default\":null}]}}],\"default\":null}]}],\"default\":null}]}}],\"default\":null},{\"name\":\"underlyer\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"Underlyer\",\"fields\":[{\"name\":\"underlyerConstiuents\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"string\"}],\"default\":null}]}],\"default\":null},{\"nam", "e\":\"paymentFrequency\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.Period\"],\"default\":null},{\"name\":\"paymentHolidayCenters\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"string\"}],\"default\":null},{\"name\":\"paymentBusinessDayConvention\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"pricingDateName\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"initialIndexPrice\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"indexSwapFeeAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"fxResetRates\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"com.scotia.gcm.sa.v1.common.Reset\"}],\"default\":null}]}},{\"name\":\"leg2\",\"type\":\"ComLeg\"},{\"name\":\"otherLegs\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"ComLeg\"}],\"default\":null},{\"name\":\"quote\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"ComQuote\",\"fields\":[{\"name\":\"price\",\"type\":\"double\"},{\"name\":\"priceCurrency\",\"type\":\"string\"},{\"name\":\"priceNotation\",\"type\":\"string\"},{\"name\":\"priceDenominationUnit\",\"type\":[\"null\",\"string\"],\"default\":null}]}],\"default\":null},{\"name\":\"events\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"ComSwapEvents\",\"fields\":[{\"name\":\"effectiveDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"adjustedEffectiveDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"terminationDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"adjustedTerminationDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"paymentDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null}]}],\"default\":null},{\"name\":\"metalForm\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"metalVault\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"metalType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"settlementUsage\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isMultiPeriodSwap\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isSpotTrade\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"historicalFixingObservations\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"com.scotia.gcm.sa.v1.common.HistoricalFixingObservations\"}],\"default\":null},{\"name\":\"venueContractCode\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"venueContractName\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"venueContractType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isTodayPayment\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isTotalReturn\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isNotionalAdjust\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"collateralResetCurves\",\"type\":[\"null\",{\"type\":\"array\",\"items\":{\"type\":\"record\",\"name\":\"CollateralResetCurve\",\"fields\":[{\"name\":\"resetDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"resetRate\",\"type\":[\"null\",\"double\"],\"default\":null}]}}],\"default\":null},{\"name\":\"collateralFutureResets\",\"type\":[\"null\",{\"type\":\"array\",\"items\":{\"type\":\"record\",\"name\":\"CollateralFutureReset\",\"fields\":[{\"name\":\"resetDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"resetRate\",\"type\":[\"null\",\"double\"],\"default\":null}]}}],\"default\":null}]}],\"default\":null},{\"name\":\"isdaInstrumentName\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"deliveryVenue\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"sourceIndexLabel\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"sourceIndexDescription\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"indexTerm\",\"type\":[\"null\",\"string\"],\"default\":null}]},\"ComIndex\"],\"default\":null},{\"name\":\"quote\",\"type\":[\"null\",\"ComQuote\"],\"default\":null},{\"name\":\"underlyerEvents\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"ComEvents\",\"fields\":[{\"name\":\"maturityLabel\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"tradeExpiryDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"deliveryFirstNoticeDay\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"deliveryLastTradingDay\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null}]},{\"type\":\"record\",\"name\":\"ComTapoEvents\",\"fields\":[{\"name\":\"tradeEffectiveDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"tradeAdjustedEffectiveDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"tradeExpiryDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"tradeAdjustedExpiryDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"observationStartDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"observationEndDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null}]}],\"default\":null},{\"name\":\"fixingObservation\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.FixingObservation\"],\"default\":null},{\"name\":\"barrierFeature\",\"type\":[\"null\",\"BarrierFeature\"],\"default\":null},{\"name\":\"metalType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"metalForm\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"metalVault\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"settlementUsage\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"facilityId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isDigital\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"digitalAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"exercisedTradeID\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"optionCashflows\",\"type\":[\"null\",{\"type\":\"array\",\"items\":{\"type\":\"record\",\"name\":\"OptionCashflow\",\"fields\":[{\"name\":\"cashflowId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"strikePrice\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"calculationStartDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"calculationEndDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"quantity\",\"type\":\"double\"},{\"name\":\"firstFixingDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"lastFixingDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"numberOfFixingDates\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"price\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"paymentDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"notionalAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"notionalCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"paymentFrequency\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.Period\"],\"default\":null},{\"name\":\"paymentHolidayCenters\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"string\"}],\"default\":null},{\"name\":\"paymentBusinessDayConvention\",\"type\":[\"null\",\"string\"],\"default\":null}]}}],\"default\":null},{\"name\":\"historicalFixingObservations\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"com.scotia.gcm.sa.v1.common.HistoricalFixingObservations\"}],\"default\":null}]},\"ComSwap\",{\"type\":\"record\",\"name\":\"ComSpotForward\",\"fields\":[{\"name\":\"buySell\",\"type\":\"string\"},{\"name\":\"quantity\",\"type\":\"double\"},{\"name\":\"quantityUnit\",\"type\":\"string\"},{\"name\":\"totalQuantity\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"startDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"endDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"settlementPrice\",\"type\":[\"null\",\"ComQuote\"],\"default\":null},{\"name\":\"notionalCurrency\",\"type\":\"string\"},{\"name\":\"notional\",\"type\":\"double\"},{\"name\":\"referenceIndex\",\"type\":[\"null\",\"ComIndex\"],\"default\":null},{\"name\":\"metalForm\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"metalVault\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"metalType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"settlementUsage\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"facilityId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isSpotTrade\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"venueContractCode\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"venueContractName\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"venueContractType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"contractStartDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"contractEndDate\",\"type\":[\"null\",{\"type\":\"int\",\"logi", "calType\":\"date\"}],\"default\":null},{\"name\":\"metalFineness\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"certificateNumber\",\"type\":[\"null\",\"string\"],\"default\":null}]},{\"type\":\"record\",\"name\":\"RepoOrSecFinance\",\"fields\":[{\"name\":\"isRepo\",\"type\":\"boolean\"},{\"name\":\"duration\",\"type\":\"string\"},{\"name\":\"fixedRate\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"indexSpread\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"maturityDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"rateType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"interestCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isMigratedDealIndicator\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"rateTypeCode\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isRehypothecationIndicator\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"sourceAgreementTypeId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"floatingRateCalculation\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.FloatingRateCalculation\"],\"default\":null},{\"name\":\"dayCount\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"callingParty\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"structureMaturityType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isStructureMaturityActiveIndicator\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isCouponReinvestmentIndicator\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"couponReinvestmentRate\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"couponReinvestmentAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"tradeValueCashCurrencyAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"tradeValueExcludingHaircutCashCurrencyAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"tradeValueSecurityCurrencyAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"tradeValueExcludingHaircutSecurityCurrencyAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"inflationCoefficientNumber\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"netDirtyPriceAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"lendingFeeAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"mortgagePoolFactor\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"totalPriceAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"cashAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"securityLendingLoanPriceAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"securityLendingLoanValueAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"variableMarginCode\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isMarginCallIndicator\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isOrderAggressed\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"tradeNovationStatus\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"interestPaymentFrequency\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.Period\"],\"default\":null},{\"name\":\"interestPaymentMethod\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"financeInterestAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"initialMargin\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"InitialMargin\",\"fields\":[{\"name\":\"marginRatio\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"marginRatioThreshold\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"haircut\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"haircutThreshold\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"haircutType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"collateralTypeName\",\"type\":[\"null\",\"string\"],\"default\":null}]}],\"default\":null},{\"name\":\"externalAllocation\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"ExternalAllocation\",\"fields\":[{\"name\":\"yield\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"priceAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"mortgagePoolFactor\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"allocationAmount\",\"type\":[\"null\",\"double\"],\"default\":null}]}],\"default\":null},{\"name\":\"resetEvent\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"ResetEvent\",\"fields\":[{\"name\":\"cleanPriceAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"dirtyPriceAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"netDirtyPriceAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"yieldRate\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"rate\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"fixingDay\",\"type\":[\"null\",\"double\"],\"default\":null}]}],\"default\":null},{\"name\":\"earlyTerminationEvent\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"EarlyTerminationEvent\",\"fields\":[{\"name\":\"callDayNumber\",\"type\":[\"null\",\"double\"],\"default\":null}]}],\"default\":null},{\"name\":\"nearLeg\",\"type\":{\"type\":\"record\",\"name\":\"RepoOrSecFinanceLeg\",\"fields\":[{\"name\":\"buySell\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"settlementAmount\",\"type\":\"double\"},{\"name\":\"settlementCurrency\",\"type\":\"string\"},{\"name\":\"settlementDate\",\"type\":{\"type\":\"int\",\"logicalType\":\"date\"}},{\"name\":\"deliveryDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"callDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"fxRate\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.FxRateQuote\"],\"default\":null},{\"name\":\"interest\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"accrualDayNumber\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"collaterals\",\"type\":[\"null\",{\"type\":\"array\",\"items\":{\"type\":\"record\",\"name\":\"Collateral\",\"fields\":[{\"name\":\"collateralSetId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"bond\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"CollateralBond\",\"fields\":[{\"name\":\"nominalAmount\",\"type\":\"double\"},{\"name\":\"nominalCurrency\",\"type\":\"string\"},{\"name\":\"quantity\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"yield\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"cleanPrice\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"accruals\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"dirtyPrice\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"effectiveDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"adjustedEffectiveDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"adjustedMaturityDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"inDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"outDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"calculationStartDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"calculationEndDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"repaymentAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"payoutType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"calculationTypeMethodName\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"estimatedMaturityDateCleanPrice\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"estimatedMaturityDateAccruedCoupon\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"estimatedMaturityDateYield\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"estimatedMaturityDateDirtyPrice\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"currentNominalAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"currentPrice\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"currentQuantity\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"couponAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"relativePrice\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"RelativePrice\",\"fields\":[{\"name\":\"spread\",\"type\":\"double\"},{\"name\":\"asset\",\"type\":\"com.scotia.gcm.sa.v1.common.ProductIdentifier\"}]}],\"default\":null},{\"name\":\"assetClassification\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"ProductClassification\",\"namespace\":\"com.scotia.gcm.sa.v1.common\",\"fields\":[{\"name\":\"assetClass\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"scotiaUPI\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"cfiCode\",\"type\":[\"null\",\"string\"],\"default\":null}]}],\"default\":null},{\"name\":\"assetReference\",\"type\":\"com.scotia.gcm.sa.v1.common.ProductIdentifier\"},{\"name\":\"settlement\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"collateralSettlement\",\"fields\":[{\"name\":\"agencyName\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"", "counterpartyAgencyName\",\"type\":[\"null\",\"string\"],\"default\":null}]}],\"default\":null}]}],\"default\":null},{\"name\":\"equity\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"CollateralEquity\",\"fields\":[{\"name\":\"numberOfUnits\",\"type\":\"double\"},{\"name\":\"unitPrice\",\"type\":\"double\"},{\"name\":\"unitCurrency\",\"type\":\"string\"},{\"name\":\"currentPrice\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"currentNominalAmount\",\"type\":[\"null\",\"int\"],\"default\":null},{\"name\":\"dividendRequiredPercentage\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"assetClassification\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.ProductClassification\"],\"default\":null},{\"name\":\"assetReference\",\"type\":\"com.scotia.gcm.sa.v1.common.ProductIdentifier\"}]}],\"default\":null},{\"name\":\"option\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"CollateralOption\",\"fields\":[{\"name\":\"currency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"quantity\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"assetClassification\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.ProductClassification\"],\"default\":null},{\"name\":\"assetReference\",\"type\":\"com.scotia.gcm.sa.v1.common.ProductIdentifier\"}]}],\"default\":null},{\"name\":\"cash\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"CollateralCash\",\"fields\":[{\"name\":\"amount\",\"type\":\"double\"},{\"name\":\"currency\",\"type\":\"string\"},{\"name\":\"settlement\",\"type\":[\"null\",\"collateralSettlement\"],\"default\":null}]}],\"default\":null}]}}],\"default\":null},{\"name\":\"deliveryMethod\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"swingTrade\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"SwingTrade\",\"fields\":[{\"name\":\"swingTradeid\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isSwingSameDirectionAsOriginatorIndicator\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isSwingComponentIndicator\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"swingTypeCode\",\"type\":[\"null\",\"string\"],\"default\":null}]}],\"default\":null},{\"name\":\"fees\",\"type\":[\"null\",{\"type\":\"array\",\"items\":{\"type\":\"record\",\"name\":\"Fee\",\"fields\":[{\"name\":\"feeType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"feePaymentMethod\",\"type\":[\"null\",\"string\"],\"default\":null}]}}],\"default\":null}]}},{\"name\":\"farLeg\",\"type\":[\"null\",\"RepoOrSecFinanceLeg\"],\"default\":null}]},{\"type\":\"record\",\"name\":\"LoanOrDeposit\",\"fields\":[{\"name\":\"termType\",\"type\":\"string\"},{\"name\":\"isLoan\",\"type\":\"boolean\"},{\"name\":\"effectiveDate\",\"type\":{\"type\":\"int\",\"logicalType\":\"date\"}},{\"name\":\"originalEffectiveDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"maturityDate\",\"type\":{\"type\":\"int\",\"logicalType\":\"date\"}},{\"name\":\"originalMaturityDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"principalCurrency\",\"type\":\"string\"},{\"name\":\"principalUnitOfMeasure\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"originalPrincipalAmount\",\"type\":\"double\"},{\"name\":\"outstandingPrincipalAmount\",\"type\":\"double\"},{\"name\":\"grossOutstandingAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"netOutstandingAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"outstandingAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"interestCurrency\",\"type\":\"string\"},{\"name\":\"interestType\",\"type\":\"string\"},{\"name\":\"fixedRate\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"facilityFxRate\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"dayCountFractionType\",\"type\":\"string\"},{\"name\":\"businessDayConventionType\",\"type\":\"string\"},{\"name\":\"resetDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"firstRolloverDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"riskType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"loanTypeDescription\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"loanTypeCode\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"riskTypeDescription\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"riskTypeCode\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"loanPricingOptionTypeDescription\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"loanPricingOptionTypeCode\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"repricingDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"lastRepricingDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"repricingFrequencyCode\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.Period\"],\"default\":null},{\"name\":\"interestRateMaturityDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"isPerforming\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isFullyDrawn\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isCancelable\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isInDefault\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isSecured\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isPerpetual\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"facilityId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isZeroCouponFlag\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"secondaryUniversalFacilityId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"loanPurposeCode\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"regulatoryLoanPurposeCode\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isEvergreenIndicator\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"paidOffDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"finalExpiryDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"loanAdjustedExpiryDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"rateCalculationMethod\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"rateLookbackDay\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"rateLockoutDay\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"ratePricingLagDay\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"principalGLAccountNumber\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"accrualGLAccountNumber\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"contingentLiabilityGLAccountNumber\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"callDayNumber\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"event\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"Event\",\"namespace\":\"com.scotia.gcm.sa.v1.common\",\"fields\":[{\"name\":\"eventId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"userId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"approverId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"transactionAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"transactionCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"transactionHostBankAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"transactionRate\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"transactionRateType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"eventTimestamp\",\"type\":[\"null\",{\"type\":\"long\",\"logicalType\":\"timestamp-millis\"}],\"default\":null},{\"name\":\"effectiveDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null}]}],\"default\":null},{\"name\":\"issuerIdentifier\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"LoanPartyIdentifier\",\"fields\":[{\"name\":\"loanPartyCode\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"loanPartyCodeType\",\"type\":[\"null\",\"string\"],\"default\":null}]}],\"default\":null},{\"name\":\"issuerExternalCreditRating\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"com.scotia.gcm.sa.v1.refdata.CpExternalCreditRating\"}],\"default\":null},{\"name\":\"floatingRateCalculation\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"com.scotia.gcm.sa.v1.common.FloatingRateCalculation\"}],\"default\":null},{\"name\":\"generalLedgerDefinition\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"GeneralLedgerDefinition\",\"fields\":[{\"name\":\"generalLedgerAccountNumber\",\"type\":\"string\"},{\"name\":\"accountName\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"accountType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"accountTypeDesc\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"generalLedgerCurrency\",\"type\":\"string\"},{\"name\":\"scotiaIndustryCode\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"generalLedgerUnit\",\"type\":[\"null\",\"string\"],\"default\":null}]}],\"default\":null},{\"name\":\"payment\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"LoanPayment\",\"fields\"", ":[{\"name\":\"paymentHolidayCenter\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"interestPaymentPeriod\",\"type\":\"string\"},{\"name\":\"principalPaymentPeriod\",\"type\":\"string\"},{\"name\":\"firstInterestPaymentDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"firstInterestPaymentAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"nextInterestPaymentDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"nextInterestPaymentAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"lastPrincipalPaymentDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"lastPrincipalPaymentAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"nextPrincipalPaymentDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"nextPrincipalPaymentAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"paymentPrincipalCurrency\",\"type\":\"string\"},{\"name\":\"penaltyAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"daysPastDueForPrincipal\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"daysPastDueForInterest\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"billingScheduleType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"prepaymentPenaltyAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"interestPastDueAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"principalPastDueAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"isEarlyPayOffPermittedFlag\",\"type\":[\"null\",\"double\"],\"default\":null}]}],\"default\":null},{\"name\":\"accrualDefinition\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"AccrualDefinition\",\"namespace\":\"com.scotia.gcm.sa.v1.common\",\"fields\":[{\"name\":\"isAccrual\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"accrualType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"interestRateCode\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"interestRateName\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"interestRateDesc\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"accrualCategory\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"accruingRate\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"accrualCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"baseRate\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"baseFloorRate\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"reserveRate\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"spreadRate\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"penaltyRate\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"spreadAdjustmentRate\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"interestDueDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"accrualFrequency\",\"type\":[\"null\",\"Period\"],\"default\":null}]}],\"default\":null},{\"name\":\"underlyer\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"LoanUnderlyer\",\"fields\":[{\"name\":\"instrumentDescription\",\"type\":\"string\"},{\"name\":\"quantityUnit\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"price\",\"type\":[\"null\",\"double\"],\"default\":null}]}],\"default\":null},{\"name\":\"lenderShares\",\"type\":[\"null\",{\"type\":\"array\",\"items\":{\"type\":\"record\",\"name\":\"LoanLenderShare\",\"namespace\":\"com.scotia.gcm.sa.v1.common\",\"fields\":[{\"name\":\"lenderShareId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"shareType\",\"type\":\"string\"},{\"name\":\"shareSubType\",\"type\":\"string\"},{\"name\":\"shareAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"bookShareAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"bookId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"strategyDescription\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"strategyCode\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isHostBankIndicator\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"MonthlyAverageOutstandingBalance\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"masteredBook\",\"type\":[\"null\",\"MasteredBook\"]},{\"name\":\"masteredLegalEntity\",\"type\":[\"null\",\"MasteredLegalEntity\"]},{\"name\":\"accrualSchedule\",\"type\":[\"null\",{\"type\":\"array\",\"items\":{\"type\":\"record\",\"name\":\"AccrualSchedule\",\"fields\":[{\"name\":\"accrualCycleId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"startDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"endDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"currencyCode\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"accrualBalanceAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"accruedAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"paidAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"withheldAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"todayAccruedAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"projectedAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"accrualAdjustmentAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"accrualRate\",\"type\":[\"null\",\"double\"],\"default\":null}]}}],\"default\":null},{\"name\":\"lenderParty\",\"type\":[\"null\",\"Party\"],\"default\":null},{\"name\":\"amortizationPeriods\",\"type\":[\"null\",{\"type\":\"array\",\"items\":{\"type\":\"record\",\"name\":\"AmortizationPeriod\",\"fields\":[{\"name\":\"amortizationPeriodId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"startDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"endDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"currencyCode\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"paidAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"notPostedAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"originalAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"unamortizedAmount\",\"type\":[\"null\",\"double\"],\"default\":null}]}}],\"default\":null},{\"name\":\"accountingMetrics\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"AccountingMasteredMetrics\"}],\"default\":null}]}}],\"default\":null},{\"name\":\"repaymentSchedules\",\"type\":[\"null\",{\"type\":\"array\",\"items\":{\"type\":\"record\",\"name\":\"RepaymentSchedule\",\"namespace\":\"com.scotia.gcm.sa.v1.common\",\"fields\":[{\"name\":\"sourcePaymentId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"arrangementId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"scheduleType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"scheduleTypeCode\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"scheduleState\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"fixedAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"paymentMultipleAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"residualAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"amortizationStartDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"amortizationEndDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"repaymentDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"periodCount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"itemSequenceNumber\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"principalDueDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"hostBankPrincipalAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"schedulePeriod\",\"type\":[\"null\",\"Period\"],\"default\":null}]}}],\"default\":null}]},{\"type\":\"record\",\"name\":\"Facility\",\"fields\":[{\"name\":\"facilityId\",\"type\":\"string\"},{\"name\":\"buySell\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"tradeCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"tradeCurrentAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"closingAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"preclosingAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"discountAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"price\",\"type\":\"double\"},{\"name\":\"tradeTypeDescription\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"tradeTypeCode\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"principalGLAccountNumber\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"strategyCode\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"transitBookIdentifier\",\"type\":[\"null\",\"string\"],\"default\":null}]},{\"type\":\"record\",\"name\":\"MoneyMarketSecurity\",\"fields\":[{\"name\":\"buySell\",\"type\":\"string\"},{\"name\":\"curre", "ncy\",\"type\":\"string\"},{\"name\":\"issueDate\",\"type\":{\"type\":\"int\",\"logicalType\":\"date\"}},{\"name\":\"effectiveDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"maturityDate\",\"type\":{\"type\":\"int\",\"logicalType\":\"date\"}},{\"name\":\"issuerName\",\"type\":\"string\"},{\"name\":\"isPerpetual\",\"type\":\"string\"},{\"name\":\"resetDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"lastRepricingDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"price\",\"type\":\"double\"},{\"name\":\"priceType\",\"type\":\"string\"},{\"name\":\"notionalAmount\",\"type\":\"double\"},{\"name\":\"principalAmount\",\"type\":\"double\"},{\"name\":\"interestType\",\"type\":\"string\"},{\"name\":\"dayCount\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"fixedRate\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"yield\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"payment\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"MoneyMarketPayment\",\"fields\":[{\"name\":\"nextInterestPaymentDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"nextInterestPaymentAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"lastInterestPaymentDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"interestPaymentPeriod\",\"type\":[\"null\",\"string\"],\"default\":null}]}],\"default\":null},{\"name\":\"floatingRateCalculation\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.FloatingRateCalculation\"],\"default\":null}]},{\"type\":\"record\",\"name\":\"Bond\",\"fields\":[{\"name\":\"buySell\",\"type\":\"string\"},{\"name\":\"quantity\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"price\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"priceCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"dirtyPrice\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"dirtyPriceCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"cleanPrice\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"cleanPriceCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"yield\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"notional\",\"type\":\"double\"},{\"name\":\"couponRate\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"discountRate\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"accrualType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isWhenIssued\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"quantityfactor\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"inflationIndexFactor\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"accruedInterest\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"overridenAccruedInterest\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"accruedAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"counterpartyAccountId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"maturityDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"couponFrequency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isZeroCouponFlag\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isFloatingRateFlag\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isCallableFlag\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isConvertibleFlag\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"settlementInfo\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.Settlement\"],\"default\":null},{\"name\":\"assetInfo\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.AssetInfo\"],\"default\":null},{\"name\":\"tradeSplitInfo\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.TradeSplitInfo\"],\"default\":null},{\"name\":\"isSourceExternal\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isOrderAggressed\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"collateralCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"paymentConditions\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"accrualDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"accrualAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"accrualStartDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"salesPrice\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"salesMarginPrice\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"salesMarginPriceYield\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"amortizationPaymentsNumber\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"tradeParAmount\",\"type\":[\"null\",\"double\"],\"default\":null}]},{\"type\":\"record\",\"name\":\"FixedIncomeOption\",\"fields\":[{\"name\":\"buySell\",\"type\":\"string\"},{\"name\":\"optionType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"optionExercise\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"FixedIncomeOptionExercise\",\"fields\":[{\"name\":\"optionExerciseStyle\",\"type\":[\"null\",\"string\"],\"default\":null}]}],\"default\":null},{\"name\":\"price\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"priceCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"dirtyPrice\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"dirtyPriceCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"strikePrice\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"strikePriceCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"strikeUnit\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"quantity\",\"type\":\"double\"},{\"name\":\"lotSize\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"notional\",\"type\":\"double\"},{\"name\":\"notionalCurrency\",\"type\":\"string\"},{\"name\":\"initialNotionalAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"initialNotionalCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"expiryDate\",\"type\":{\"type\":\"int\",\"logicalType\":\"date\"}},{\"name\":\"venueContractCode\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"venueContractName\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"venueContractType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"underlyer\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"FixedIncomeOptionUnderlyer\",\"fields\":[{\"name\":\"productIdentifier\",\"type\":\"com.scotia.gcm.sa.v1.common.ProductIdentifier\"},{\"name\":\"events\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.UnderlyerEvents\"],\"default\":null}]}],\"default\":null},{\"name\":\"counterpartyAccountId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"settlementInfo\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.Settlement\"],\"default\":null},{\"name\":\"assetInfo\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.AssetInfo\"],\"default\":null},{\"name\":\"tradeSplitInfo\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.TradeSplitInfo\"],\"default\":null},{\"name\":\"isSourceExternal\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isOrderAggressed\",\"type\":[\"null\",\"string\"],\"default\":null}]},{\"type\":\"record\",\"name\":\"Forward\",\"fields\":[{\"name\":\"buySell\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"npvCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"event\",\"type\":[\"null\",\"IrEvents\"],\"default\":null},{\"name\":\"notional\",\"type\":\"double\"},{\"name\":\"notionalCurrency\",\"type\":\"string\"},{\"name\":\"isCollateralFlag\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"strikePrice\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"strikeCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"theoreticalModel\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"settlementDaysOffset\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.Period\"],\"default\":null},{\"name\":\"sourceForwardCurve\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isNDF\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"ndfDelivSettlementAmount\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"ndfBondClosingPrice\",\"type\":[\"null\",\"double\"],\"default\":null},{\"name\":\"ndfFXResets\",\"type\":[\"null\",{\"type\":\"array\",\"items\":{\"type\":\"record\",\"name\":\"NdfFxReset\",\"namespace\":\"com.scotia.gcm.sa.v1.common\",\"fields\":[{\"name\":\"resetDate\",\"type\":{\"type\":\"int\",\"logicalType\":\"date\"}},{\"name\":\"resetRate\",\"type\":\"double\"},{\"name\":\"fxSourceRef\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"bondClosingSourceRef\",\"type\":[\"null\",\"string\"],\"default\":null}]}}],\"default\":null},{\"name\":\"ndfFXResetHolidays\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"string\"}],\"default\":null},{\"name\":\"ndfFXResetOffset\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.Period\"],\"default\":null},{\"name\":\"ndfSettlementCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"ndfSettlementDiscountCurves\",\"type\":", "[\"null\",{\"type\":\"array\",\"items\":\"string\"}],\"default\":null},{\"name\":\"effectiveDateBusinessDayConvention\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"terminationDateBusinessDayConvention\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"dateAdjustmentBusinessCenter\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"string\"}],\"default\":null},{\"name\":\"sourceDiscountCurve\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"paymentBusinessDayConvention\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"paymentHolidayCenters\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"string\"}],\"default\":null},{\"name\":\"underlyerProductInformation\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.ProductIdentifier\"],\"default\":null},{\"name\":\"novation\",\"type\":[\"null\",\"com.scotia.gcm.sa.v1.common.Novation\"],\"default\":null}]},{\"type\":\"record\",\"name\":\"ExoticIRSwap\",\"fields\":[{\"name\":\"npvCurrency\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"fxResetType\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"fxConvertDir\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"fxResetInfo\",\"type\":[\"null\",\"FxResetInfo\"],\"default\":null},{\"name\":\"fxResetRates\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"com.scotia.gcm.sa.v1.common.Reset\"}],\"default\":null},{\"name\":\"principalExchange\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"theoreticalModel\",\"type\":\"string\"},{\"name\":\"payLeg\",\"type\":\"com.scotia.gcm.sa.v1.common.InterestLeg\"},{\"name\":\"receiveLeg\",\"type\":\"com.scotia.gcm.sa.v1.common.InterestLeg\"},{\"name\":\"events\",\"type\":[\"null\",\"IrEvents\"],\"default\":null},{\"name\":\"componentName\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"components\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"IRSubSwap\"}],\"default\":null}]},{\"type\":\"record\",\"name\":\"LetterOfCredit\",\"fields\":[{\"name\":\"notionalCurrencyCode\",\"type\":\"string\"},{\"name\":\"notionalAmount\",\"type\":\"double\"},{\"name\":\"netAmount\",\"type\":\"double\"},{\"name\":\"maturityDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"default\":null},{\"name\":\"isParticipatedIndicator\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isSyndicatedIndicator\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"isPerpetualIndicator\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"interbranchTradeId\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"lcConfirmationType\",\"type\":[\"null\",\"string\"],\"default\":null}]}]}]}}]}"});
    private static final SpecificData MODEL$ = new SpecificData();
    private static final BinaryMessageEncoder<TradeMessage> ENCODER;
    private static final BinaryMessageDecoder<TradeMessage> DECODER;
    private MessageHeader header;
    private Trade trade;
    private static final DatumWriter<TradeMessage> WRITER$;
    private static final DatumReader<TradeMessage> READER$;

    public static Schema getClassSchema() {
        return SCHEMA$;
    }

    public static BinaryMessageEncoder<TradeMessage> getEncoder() {
        return ENCODER;
    }

    public static BinaryMessageDecoder<TradeMessage> getDecoder() {
        return DECODER;
    }

    public static BinaryMessageDecoder<TradeMessage> createDecoder(SchemaStore resolver) {
        return new BinaryMessageDecoder(MODEL$, SCHEMA$, resolver);
    }

    public ByteBuffer toByteBuffer() throws IOException {
        return ENCODER.encode(this);
    }

    public static TradeMessage fromByteBuffer(ByteBuffer b) throws IOException {
        return (TradeMessage)DECODER.decode(b);
    }

    public TradeMessage() {
    }

    public TradeMessage(MessageHeader header, Trade trade) {
        this.header = header;
        this.trade = trade;
    }

    public SpecificData getSpecificData() {
        return MODEL$;
    }

    public Schema getSchema() {
        return SCHEMA$;
    }

    public Object get(int field$) {
        switch (field$) {
            case 0:
                return this.header;
            case 1:
                return this.trade;
            default:
                throw new IndexOutOfBoundsException("Invalid index: " + field$);
        }
    }

    public void put(int field$, Object value$) {
        switch (field$) {
            case 0:
                this.header = (MessageHeader)value$;
                break;
            case 1:
                this.trade = (Trade)value$;
                break;
            default:
                throw new IndexOutOfBoundsException("Invalid index: " + field$);
        }

    }

    public MessageHeader getHeader() {
        return this.header;
    }

    public void setHeader(MessageHeader value) {
        this.header = value;
    }

    public Trade getTrade() {
        return this.trade;
    }

    public void setTrade(Trade value) {
        this.trade = value;
    }

    public static Builder newBuilder() {
        return new Builder();
    }

    public static Builder newBuilder(Builder other) {
        return other == null ? new Builder() : new Builder(other);
    }

    public static Builder newBuilder(TradeMessage other) {
        return other == null ? new Builder() : new Builder(other);
    }

    public void writeExternal(ObjectOutput out) throws IOException {
        WRITER$.write(this, SpecificData.getEncoder(out));
    }

    public void readExternal(ObjectInput in) throws IOException {
        READER$.read(this, SpecificData.getDecoder(in));
    }

    static {
        MODEL$.addLogicalTypeConversion(new TimeConversions.DateConversion());
        MODEL$.addLogicalTypeConversion(new TimeConversions.TimestampMillisConversion());
        ENCODER = new BinaryMessageEncoder(MODEL$, SCHEMA$);
        DECODER = new BinaryMessageDecoder(MODEL$, SCHEMA$);
        WRITER$ = MODEL$.createDatumWriter(SCHEMA$);
        READER$ = MODEL$.createDatumReader(SCHEMA$);
    }

    @AvroGenerated
    public static class Builder extends SpecificRecordBuilderBase<TradeMessage> implements RecordBuilder<TradeMessage> {
        private MessageHeader header;
        private MessageHeader.Builder headerBuilder;
        private Trade trade;
        private Trade.Builder tradeBuilder;

        private Builder() {
            super(TradeMessage.SCHEMA$, TradeMessage.MODEL$);
        }

        private Builder(Builder other) {
            super(other);
            if (isValidValue(this.fields()[0], other.header)) {
                this.header = (MessageHeader)this.data().deepCopy(this.fields()[0].schema(), other.header);
                this.fieldSetFlags()[0] = other.fieldSetFlags()[0];
            }

            if (other.hasHeaderBuilder()) {
                this.headerBuilder = MessageHeader.newBuilder(other.getHeaderBuilder());
            }

            if (isValidValue(this.fields()[1], other.trade)) {
                this.trade = (Trade)this.data().deepCopy(this.fields()[1].schema(), other.trade);
                this.fieldSetFlags()[1] = other.fieldSetFlags()[1];
            }

            if (other.hasTradeBuilder()) {
                this.tradeBuilder = Trade.newBuilder(other.getTradeBuilder());
            }

        }

        private Builder(TradeMessage other) {
            super(TradeMessage.SCHEMA$, TradeMessage.MODEL$);
            if (isValidValue(this.fields()[0], other.header)) {
                this.header = (MessageHeader)this.data().deepCopy(this.fields()[0].schema(), other.header);
                this.fieldSetFlags()[0] = true;
            }

            this.headerBuilder = null;
            if (isValidValue(this.fields()[1], other.trade)) {
                this.trade = (Trade)this.data().deepCopy(this.fields()[1].schema(), other.trade);
                this.fieldSetFlags()[1] = true;
            }

            this.tradeBuilder = null;
        }

        public MessageHeader getHeader() {
            return this.header;
        }

        public Builder setHeader(MessageHeader value) {
            this.validate(this.fields()[0], value);
            this.headerBuilder = null;
            this.header = value;
            this.fieldSetFlags()[0] = true;
            return this;
        }

        public boolean hasHeader() {
            return this.fieldSetFlags()[0];
        }

        public MessageHeader.Builder getHeaderBuilder() {
            if (this.headerBuilder == null) {
                if (this.hasHeader()) {
                    this.setHeaderBuilder(MessageHeader.newBuilder(this.header));
                } else {
                    this.setHeaderBuilder(MessageHeader.newBuilder());
                }
            }

            return this.headerBuilder;
        }

        public Builder setHeaderBuilder(MessageHeader.Builder value) {
            this.clearHeader();
            this.headerBuilder = value;
            return this;
        }

        public boolean hasHeaderBuilder() {
            return this.headerBuilder != null;
        }

        public Builder clearHeader() {
            this.header = null;
            this.headerBuilder = null;
            this.fieldSetFlags()[0] = false;
            return this;
        }

        public Trade getTrade() {
            return this.trade;
        }

        public Builder setTrade(Trade value) {
            this.validate(this.fields()[1], value);
            this.tradeBuilder = null;
            this.trade = value;
            this.fieldSetFlags()[1] = true;
            return this;
        }

        public boolean hasTrade() {
            return this.fieldSetFlags()[1];
        }

        public Trade.Builder getTradeBuilder() {
            if (this.tradeBuilder == null) {
                if (this.hasTrade()) {
                    this.setTradeBuilder(Trade.newBuilder(this.trade));
                } else {
                    this.setTradeBuilder(Trade.newBuilder());
                }
            }

            return this.tradeBuilder;
        }

        public Builder setTradeBuilder(Trade.Builder value) {
            this.clearTrade();
            this.tradeBuilder = value;
            return this;
        }

        public boolean hasTradeBuilder() {
            return this.tradeBuilder != null;
        }

        public Builder clearTrade() {
            this.trade = null;
            this.tradeBuilder = null;
            this.fieldSetFlags()[1] = false;
            return this;
        }

        public TradeMessage build() {
            try {
                TradeMessage record = new TradeMessage();
                if (this.headerBuilder != null) {
                    try {
                        record.header = this.headerBuilder.build();
                    } catch (AvroMissingFieldException e) {
                        e.addParentField(record.getSchema().getField("header"));
                        throw e;
                    }
                } else {
                    record.header = this.fieldSetFlags()[0] ? this.header : (MessageHeader)this.defaultValue(this.fields()[0]);
                }

                if (this.tradeBuilder != null) {
                    try {
                        record.trade = this.tradeBuilder.build();
                    } catch (AvroMissingFieldException e) {
                        e.addParentField(record.getSchema().getField("trade"));
                        throw e;
                    }
                } else {
                    record.trade = this.fieldSetFlags()[1] ? this.trade : (Trade)this.defaultValue(this.fields()[1]);
                }

                return record;
            } catch (AvroMissingFieldException e) {
                throw e;
            } catch (Exception e) {
                throw new AvroRuntimeException(e);
            }
        }
    }
}
