import java.text.SimpleDateFormat
import static Constants.*

class Constants {
        static final UNDEF = "NaN"
        static final SCHEMA_VERSION = "1.97"
        static final DEFAULT_DATE = "0"
        static final MESSAGE_TYPE = "NativeMessage"

        static stringNullCheck(String input) {
                if (input == "" || input == null)
                        return UNDEF
                else
                        return input
        }

        static getDate(String input, boolean mandatory) {
                try { // Deprecated: return Date.parse("yyyyMMdd", input)
                        def date = new SimpleDateFormat("yyyyMMdd").parse(input)
                        return input
                } catch(Exception ex) {
                        if (mandatory)
                                return DEFAULT_DATE
                        else
                                return ""
                }
        }
        static doubleValidityCheck(String input, boolean mandatory) {
                if (input == "Infinity" || input == "NaN" || input == "" || input == null)
                {
                        if (mandatory)
                                return 0.0
                        else
                                return null
                }
                else {
                        try {
                                return input.toDouble()
                        } catch(Exception e) {
                                if (mandatory)
                                        return 0.0
                                else
                                        return null
                        }
                }
        } // doubleValidityCheck()

        static intValidityCheck(String input, boolean mandatory) {
                if (input == "Infinity" || input == "NaN" || input == "" || input == null || input == "0")
                {
                        if (mandatory)
                                return 0
                        else
                                return null
                }
                else {
                        try {
                                return input.toInteger()
                        } catch(Exception e) {
                                if (mandatory)
                                        return 0
                                else
                                        return null
                        }
                }
        } // doubleValidityCheck()

}
[
        header  : [
                messageId                    : row.header.messageId,
                businessId                   : row.header.businessId,
                batchId                      : null,
                sourceSystem                 : row.header.sourceSystem,
                secondarySourceSystem        : row.header.secondarySourceSystem,
                sourceSystemCreationTimestamp: row.header.sourceSystemCreationTimestamp,
                sentBy                       : row.header.sentBy,
                sentTo                       : row.header.sentTo,
                messageType                  : MESSAGE_TYPE,
                schemaVersion                : SCHEMA_VERSION,
                processing                   : row.header.processing,
                recordOffset                 : null,
        ],
        nativeData: [
                tags : [
                SettleAccrualDayCntConvention   : stringNullCheck(row.nativeData.tags.get('SettleAccrualDayCntConvention')),
                MortgageLiquidationParameter    : stringNullCheck(row.nativeData.tags.get('MortgageLiquidationParameter')),
                PartialPrepaymentRate           : stringNullCheck(row.nativeData.tags.get('PartialPrepaymentRate')),
                DayCntConvention                : stringNullCheck(row.nativeData.tags.get('DayCntConvention')),
                publisher_name                  : stringNullCheck(row.nativeData.tags.get('PublisherName')),
                MortgageLiquidationModel        : stringNullCheck(row.nativeData.tags.get('MortgageLiquidationModel')),
                AccrualDayCntConventionStr      : stringNullCheck(row.nativeData.tags.get('AccrualDayCntConventionStr')),
                SettleAccrualDayCntConventionStr: stringNullCheck(row.nativeData.tags.get('SettleAccrualDayCntConventionStr')),
                source                          : stringNullCheck(row.header.sentBy),
                MoneyMarketDayCntConventionStr  : stringNullCheck(row.nativeData.tags.get('MoneyMarketDayCntConventionStr')),
                PenaltyInterestPaymentRatio     : stringNullCheck(row.nativeData.tags.get('PenaltyInterestPaymentRatio')),
                BalancePriorMaturity1M          : stringNullCheck(row.nativeData.tags.get('BalancePriorMaturity1M')),
                DayCntConventionStr             : stringNullCheck(row.nativeData.tags.get('DayCntConventionStr')),
                MortgageLiquidationModelStr     : stringNullCheck(row.nativeData.tags.get('MortgageLiquidationModelStr')),
                BalancePriorMaturity2M          : stringNullCheck(row.nativeData.tags.get('BalancePriorMaturity2M')),
                BalancePriorMaturity3M          : stringNullCheck(row.nativeData.tags.get('BalancePriorMaturity3M')),
                BalancePriorMaturity4M          : stringNullCheck(row.nativeData.tags.get('BalancePriorMaturity4M')),
                BalancePriorMaturity5M          : stringNullCheck(row.nativeData.tags.get('BalancePriorMaturity5M')),
                Id                              : stringNullCheck(row.nativeData.tags.get('Id')),
                InterestAdjustmentDate          : stringNullCheck(row.nativeData.tags.get('InterestAdjustmentDate')),
                AccrualDayCntConvention         : stringNullCheck(row.nativeData.tags.get('AccrualDayCntConvention')),
                MoneyMarketDayCntConvention     : stringNullCheck(row.nativeData.tags.get('MoneyMarketDayCntConvention')),
        ],
        data: null
    ]
]