import java.text.SimpleDateFormat
import static Constants.*

class Constants {
        static final UNDEF = "Undef"
        static final SCHEMA_VERSION = "1.97"
        static final DEFAULT_DATE = "0"
        static final MESSAGE_TYPE = "NativeMessage"

        static stringNullCheck(String input) {
                if (input == "" || input == null)
                        return ""
                else
                        return input
        }
        static stringNullCheckUndef(String input) {
                if (input == "" || input == null)
                        return UNDEF
                else
                        return input
        }

        static getDate(String input, boolean mandatory) {
                try { // Deprecated: return Date.parse("yyyyMMdd", input)
                        def date = new SimpleDateFormat("yyyyMMdd").parse(input)
                        return input
                } catch(Exception ex) {
                        if (mandatory)
                                return DEFAULT_DATE
                        else
                                return ""
                }
        }
        static doubleValidityCheck(String input, boolean mandatory) {
                if (input == "Infinity" || input == "NaN" || input == "" || input == null)
                {
                        if (mandatory)
                                return 0.0
                        else
                                return null
                }
                else {
                        try {
                                return input.toDouble()
                        } catch(Exception e) {
                                if (mandatory)
                                        return 0.0
                                else
                                        return null
                        }
                }
        } // doubleValidityCheck()

        static intValidityCheck(String input, boolean mandatory) {
                if (input == "Infinity" || input == "NaN" || input == "" || input == null || input == "0")
                {
                        if (mandatory)
                                return 0
                        else
                                return null
                }
                else {
                        try {
                                return input.toInteger()
                        } catch(Exception e) {
                                if (mandatory)
                                        return 0
                                else
                                        return null
                        }
                }
        } // doubleValidityCheck()

}
[
        header  : [
                messageId                    : row.header.messageId,
                businessId                   : row.header.businessId,
                sourceSystem                 : row.header.sourceSystem,
                secondarySourceSystem        : row.header.secondarySourceSystem,
                sourceSystemCreationTimestamp: row.header.sourceSystemCreationTimestamp,
                sentBy                       : row.header.sentBy,
                sentTo                       : row.header.sentTo,
                messageType                  : MESSAGE_TYPE,
                schemaVersion                : SCHEMA_VERSION,
                processing                   : row.header.processing,
        ],
        nativeData: [
                tags : [
                FloaterResetDate            : getDate(row.nativeData.tags.get('FloaterResetDate'),false),
                QtyNominal                  : intValidityCheck(row.nativeData.tags.get('QtyNominal'),true),
                PriceTypeStr                : stringNullCheck(row.nativeData.tags.get('PriceTypeStr')),
                NextCallDate                : getDate(row.nativeData.tags.get('NextCallDate'),true),
                InsertionSource             : stringNullCheck(row.nativeData.tags.get('InsertionSource')),
                STRIPTypeStr                : stringNullCheck(row.nativeData.tags.get('STRIPTypeStr')),
                CallableFlag                : intValidityCheck(row.nativeData.tags.get('CallableFlag'), true),
                DateAnnounce                : getDate(row.nativeData.tags.get('DateAnnounce'),true),
                RatingDBRS                  : stringNullCheck(row.nativeData.tags.get('RatingDBRS')),
                FloaterIndex                : stringNullCheck(row.nativeData.tags.get('FloaterIndex')),
                ADPCode                     : stringNullCheck(row.nativeData.tags.get('ADPCode')),
                ScotiaId                    : stringNullCheck(row.nativeData.tags.get('ScotiaId')),
                CurrencyStr                 : stringNullCheck(row.nativeData.tags.get('CurrencyStr')),
                CallNotifyDays              : intValidityCheck(row.nativeData.tags.get('CallNotifyDays'),true),
                CollateralType              : stringNullCheck(row.nativeData.tags.get('CollateralType')),
                Code                        : stringNullCheck(row.nativeData.tags.get('Code')),
                InsertionDate               : getDate(row.nativeData.tags.get('InsertionDate'),true),
                UnderlyingDesc              : stringNullCheck(row.nativeData.tags.get('UnderlyingDesc')),
                Exchange                    : stringNullCheck(row.nativeData.tags.get('Exchange')),
                InsTypeStr                  : stringNullCheck(row.nativeData.tags.get('InsTypeStr')),
                Country                     : stringNullCheck(row.nativeData.tags.get('Country')),
                CountryOfIssue              : stringNullCheck(row.nativeData.tags.get('CountryOfIssue')),
                RatingStandardPoors         : stringNullCheck(row.nativeData.tags.get('RatingStandardPoors')),
                Id                          : stringNullCheck(row.nativeData.tags.get('Id')),
                DateFirstCoupon             : getDate(row.nativeData.tags.get('DateFirstCoupon'),true),
                Called                      : stringNullCheck(row.nativeData.tags.get('Called')),
                DateStop                    : getDate(row.nativeData.tags.get('DateStop'),true),
                MBSPoolId                   : stringNullCheck(row.nativeData.tags.get('MBSPoolId')),
                ResetFreq                   : intValidityCheck(row.nativeData.tags.get('ResetFreq'),true),
                CallNotifyDaysType          : intValidityCheck(row.nativeData.tags.get('CallNotifyDaysType'),true),
                CalculationTypeDescription  : stringNullCheck(row.nativeData.tags.get('CalculationTypeDescription')),
                SecurityTypeStr             : stringNullCheck(row.nativeData.tags.get('SecurityTypeStr')),
                IssuerId                    : stringNullCheck(row.nativeData.tags.get('IssuerId')),
                RatingFitch                 : stringNullCheck(row.nativeData.tags.get('RatingFitch')),
                FutureProductCode           : stringNullCheck(row.nativeData.tags.get('FutureProductCode')),
                PriceStrike                 : doubleValidityCheck(row.nativeData.tags.get('PriceStrike'),true),
                IndexLinkedFlag             : intValidityCheck(row.nativeData.tags.get('IndexLinkedFlag'),true),
                BondCallOptionStyleStr      : stringNullCheck(row.nativeData.tags.get('BondCallOptionStyleStr')),
                IndustryGroup               : stringNullCheck(row.nativeData.tags.get('IndustryGroup')),
                DateFirstDelivery           : getDate(row.nativeData.tags.get('DateFirstDelivery'),true),
                DateDated                   : getDate(row.nativeData.tags.get('DateDated'),true),
                BondCalcType                : stringNullCheck(row.nativeData.tags.get('BondCalcType')),
                FloaterFlag                 : intValidityCheck(row.nativeData.tags.get('FloaterFlag'),true),
                publisher_name              : stringNullCheck(row.nativeData.tags.get('PublisherName')),
                CompoundingFreqStr          : stringNullCheck(row.nativeData.tags.get('CompoundingFreqStr')),
                FixToFloatFlag              : intValidityCheck(row.nativeData.tags.get('FixToFloatFlag'),true),
                Ticker                      : stringNullCheck(row.nativeData.tags.get('Ticker')),
                AccrualDayCntConventionStr  : stringNullCheckUndef(row.nativeData.tags.get('AccrualDayCntConventionStr')),
                CodeTypeStr                 : stringNullCheck(row.nativeData.tags.get('CodeTypeStr')),
                CompoundingFreq             : intValidityCheck(row.nativeData.tags.get('CompoundingFreq'),true),
                IsSecured                   : stringNullCheck(row.nativeData.tags.get('IsSecured')),
                DateFinalMaturity           : getDate(row.nativeData.tags.get('DateFinalMaturity'),true),
                FutureDeliverableBondsISINs : stringNullCheck(row.nativeData.tags.get('FutureDeliverableBondsISINs')),
                DateInterestAccrual         : getDate(row.nativeData.tags.get('DateInterestAccrual'),true),
                DayCntConventionStr         : stringNullCheck(row.nativeData.tags.get('DayCntConventionStr')),
                CalculationType             : intValidityCheck(row.nativeData.tags.get('CalculationType'),true),
                PerpetualFlag               : intValidityCheck(row.nativeData.tags.get('PerpetualFlag'),true),
                CouponCalcTypeStr           : stringNullCheck(row.nativeData.tags.get('CouponCalcTypeStr')),
                FutureDeliverableBondsCusips: stringNullCheck(row.nativeData.tags.get('FutureDeliverableBondsCusips')),
                FactorDate                  : getDate(row.nativeData.tags.get('FactorDate'),true),
                UnderlyingCode              : stringNullCheck(row.nativeData.tags.get('UnderlyingCode')),
                source                      : stringNullCheck(row.header.sentBy),
                ZeroCouponFlag              : intValidityCheck(row.nativeData.tags.get('ZeroCouponFlag'),true),
                DateOptionExpire            : getDate(row.nativeData.tags.get('DateOptionExpire'),true),
                CallNotifyDaysTypeStr       : stringNullCheck(row.nativeData.tags.get('CallNotifyDaysTypeStr')),
                RatingMoodys                : stringNullCheck(row.nativeData.tags.get('RatingMoodys')),
                Desc                        : stringNullCheck(row.nativeData.tags.get('Desc')),
                EomAdjustFlag               : intValidityCheck(row.nativeData.tags.get('EomAdjustFlag'),true),
                Factor                      : doubleValidityCheck(row.nativeData.tags.get('Factor'),true),
                IsAmortiser                 : stringNullCheck(row.nativeData.tags.get('IsAmortiser')),
                IssuerIndustry              : stringNullCheck(row.nativeData.tags.get('IssuerIndustry')),
                AccrualDayCntConvention     : stringNullCheck(row.nativeData.tags.get('AccrualDayCntConvention')),
                FeedToPXE1                  : intValidityCheck(row.nativeData.tags.get('FeedToPXE1'),true),
                CusipBBG                    : stringNullCheck(row.nativeData.tags.get('CusipBBG')),
                QtyIssue                    : doubleValidityCheck(row.nativeData.tags.get('QtyIssue'),true),
                InstrumentMappingId         : stringNullCheck(row.nativeData.tags.get('InstrumentMappingId')),
                CouponInterest              : doubleValidityCheck(row.nativeData.tags.get('CouponInterest'),true),
                InstrumentId                : stringNullCheck(row.nativeData.tags.get('InstrumentId')),
                PutableFlag                 : intValidityCheck(row.nativeData.tags.get('PutableFlag'),true),
                BondDefaultFlag             : stringNullCheck(row.nativeData.tags.get('BondDefaultFlag')),
                WIRollFlag                  : intValidityCheck(row.nativeData.tags.get('WIRollFlag'),true),
                MBSPoolFlag                 : intValidityCheck(row.nativeData.tags.get('MBSPoolFlag'),true),
                InsSubTypeStr               : stringNullCheck(row.nativeData.tags.get('InsSubTypeStr')),
                StatusStr                   : stringNullCheck(row.nativeData.tags.get('StatusStr')),
                CouponScheduleDate          : getDate(row.nativeData.tags.get('CouponScheduleDate'),false),
                UnderlyingId                : stringNullCheck(row.nativeData.tags.get('UnderlyingId')),
                InsSubType                  : stringNullCheck(row.nativeData.tags.get('InsSubType')),
                FutureSettlTypeStr          : stringNullCheck(row.nativeData.tags.get('FutureSettlTypeStr')),
                WAC                         : doubleValidityCheck(row.nativeData.tags.get('WAC'),true),
                RedemptionValue             : doubleValidityCheck(row.nativeData.tags.get('RedemptionValue'),true),
                DateSettl                   : getDate(row.nativeData.tags.get('DateSettl'),true),
                WAM                         : doubleValidityCheck(row.nativeData.tags.get('WAM'),true),
                FloaterResetFreq            : intValidityCheck(row.nativeData.tags.get('FloaterResetFreq'),true),
                DayCntConvention            : intValidityCheck(row.nativeData.tags.get('DayCntConvention'),true),
                CashFlowCount               : intValidityCheck(row.nativeData.tags.get('CashFlowCount'),true),
                Issuer                      : stringNullCheck(row.nativeData.tags.get('Issuer')),
                DateStart                   : getDate(row.nativeData.tags.get('DateStart'),true),
                QtyTick                     : doubleValidityCheck(row.nativeData.tags.get('QtyTick'),true),
                FutureCalcTypeStr           : stringNullCheck(row.nativeData.tags.get('FutureCalcTypeStr')),
                UnderlyingSecurityTypeDesc  : stringNullCheck(row.nativeData.tags.get('UnderlyingSecurityTypeDesc')),
                ValueTypeStr                : stringNullCheck(row.nativeData.tags.get('ValueTypeStr')),
                IndexTicker                 : stringNullCheck(row.nativeData.tags.get('IndexTicker')),
                DateIssue                   : getDate(row.nativeData.tags.get('DateIssue'),true),
                Isin                        : stringNullCheck(row.nativeData.tags.get('Isin')),
                DateLastTrade               : getDate(row.nativeData.tags.get('DateLastTrade'),true),
                MBSBalOnMty                 : doubleValidityCheck(row.nativeData.tags.get('MBSBalOnMty'),true),
                DateMaturity                : getDate(row.nativeData.tags.get('DateMaturity'),true),
                WI                          : intValidityCheck(row.nativeData.tags.get('WI'),true),
                CouponFreqStr               : stringNullCheck(row.nativeData.tags.get('CouponFreqStr')),
                QtyRemaining                : doubleValidityCheck(row.nativeData.tags.get('QtyRemaining'),true),
                PriceTick                   : doubleValidityCheck(row.nativeData.tags.get('PriceTick'),true),
                TOR_TSXEligibleFlag         : stringNullCheck(row.nativeData.tags.get('TOR_TSXEligibleFlag')),
                S0_StubPeriodType           : intValidityCheck(row.nativeData.tags.get('S0_StubPeriodType'),true),
                PaymentDateAdjTypeStr       : stringNullCheck(row.nativeData.tags.get('PaymentDateAdjTypeStr')),
                IndexRatio                  : doubleValidityCheck(row.nativeData.tags.get('IndexRatio'),true),
                FloaterMargin               : doubleValidityCheck(row.nativeData.tags.get('FloaterMargin'),true),
                IsCovered                   : stringNullCheck(row.nativeData.tags.get('IsCovered')),
                IndustrySector              : stringNullCheck(row.nativeData.tags.get('IndustrySector')),
                FuturesCTDDesc              : stringNullCheck(row.nativeData.tags.get('FuturesCTDDesc')),
                RIC                         : stringNullCheck(row.nativeData.tags.get('RIC')),
                Cusip                       : stringNullCheck(row.nativeData.tags.get('Cusip')),
                SettlDays                   : intValidityCheck(row.nativeData.tags.get('SettlDays'),true),
                DateLastCoupon              : getDate(row.nativeData.tags.get('DateLastCoupon'),true),
                InsType                     : stringNullCheck(row.nativeData.tags.get('InsType')),
                Series                      : stringNullCheck(row.nativeData.tags.get('Series')),
                IssuerShortName             : stringNullCheck(row.nativeData.tags.get('IssuerShortName')),
                PriceMultiplier             : doubleValidityCheck(row.nativeData.tags.get('PriceMultiplier'),true),
                OptionTypeStr               : stringNullCheck(row.nativeData.tags.get('OptionTypeStr')),
                CouponCalcType              : intValidityCheck(row.nativeData.tags.get('CouponCalcType'),true),
                GLClass                     : stringNullCheck(row.nativeData.tags.get('GLClass')),
                AccountingConvention        : stringNullCheck(row.nativeData.tags.get('AccountingConvention')),
                DateLastDelivery            : getDate(row.nativeData.tags.get('DateLastDelivery'),true),
                CallFeature                 : stringNullCheck(row.nativeData.tags.get('CallFeature')),
                CouponFreq                  : intValidityCheck(row.nativeData.tags.get('CouponFreq'),true),
        ],
        data: null
    ]
]