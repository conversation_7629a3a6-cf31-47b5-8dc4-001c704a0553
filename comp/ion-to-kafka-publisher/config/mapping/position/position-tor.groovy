import java.text.SimpleDateFormat

import static Constants.*

class Constants {
    static final UNKNOWN = "Unknown"
    static final SCHEMA_VERSION = "1.144"
    static final DEFAULT_DATE = "19000101"
    static final SOURCE_INTERNAL_ID_TYPE = "MMI_ID"
    static final MESSAGE_TYPE_POSITION = "PositionMessage"
    static final HEADER_REGION = "Toronto"
    static final POSITION_TYPE = "TradedPosition"

    static stringNullCheck(String input) {
        if (input == "" || input == null)
            return UNKNOWN
        else
            return input
    }

    static getDate(String input, boolean mandatory) {
        try { // Deprecated: return Date.parse("yyyyMMdd", input)
            return new SimpleDateFormat("yyyyMMdd").parse(input)
        } catch (Exception ex) {
            if (mandatory)
                return new SimpleDateFormat("yyyyMMdd").parse(DEFAULT_DATE)
            else
                return null
        }
    }

    static doubleValidityCheck(String input, boolean mandatory) {
		if (input == "Infinity" || input == "NaN" || input == "" || input == null)
		{
            if (mandatory)
                return 0.0
            else
                return null
		}
		else
            return input
    }
}

[
        header  : [
                messageId                    : row.header.messageId,
                businessId                   : row.header.businessId,
                sourceSystem                 : row.header.sourceSystem,
                sourceSystemCreationTimestamp: row.header.sourceSystemCreationTimestamp,
                sentBy                       : row.header.sourceSystem,
                sentTo                       : row.header.sentTo,
                messageType                  : MESSAGE_TYPE_POSITION,
                schemaVersion                : SCHEMA_VERSION,
                processing                   : row.header.processing,
        ],
        position: [
                positionHeader        : [
                        asset           : [
                                assetIdentifier: [
                                        sourceInternalId    : row.nativeData.tags.get('InstrumentId'),
                                        sourceInternalIdType: SOURCE_INTERNAL_ID_TYPE,
                                ],
                                assetClass     : UNKNOWN,
                        ],
                        sourcePositionId: stringNullCheck(row.nativeData.tags.get('Id')),
                        positionDate    : getDate(row.nativeData.tags.get('Date'), true),
                        asOfDate        : getDate(row.nativeData.tags.get('Date'), true),
                        region          : HEADER_REGION,
                        positionType    : POSITION_TYPE,
                        //positionEvent   : "",
                ],
                book                  : [
                        bookId: stringNullCheck(row.nativeData.tags.get('BookId')),
                ],
                metrics               : [
                        [
                                name : "PositionContracts",
                                value: doubleValidityCheck(row.nativeData.tags.get('NetTradingPos'), true),
                        ],
                        [
                                name : "SettledPositionContracts",
                                value: doubleValidityCheck(row.nativeData.tags.get('SettledPos'), true),
                        ],
                        [
                                name : "UnsettledPositionContracts",
                                value: doubleValidityCheck(row.nativeData.tags.get('UnsettledPos'), true),
                        ],
                        [
                                name : "PositionAge",
                                value: doubleValidityCheck(row.nativeData.tags.get('PositionAge'), true),
                        ],
                        [
                                name : "SettledPosNominalPrevious",
                                value: doubleValidityCheck(row.nativeData.tags.get('SettledPosNominalPrevious'), true),
                        ],
                        [
                                name : "SettledPosPrevious",
                                value: doubleValidityCheck(row.nativeData.tags.get('SettledPosPrevious'), true),
                        ],
                        [
                                name : "SODPos",
                                value: doubleValidityCheck(row.nativeData.tags.get('SODPos'), true),
                        ],
                        [
                                name : "SODPosNominal",
                                value: doubleValidityCheck(row.nativeData.tags.get('SODPosNominal'), true),
                        ],
                        [
                                name : "TradeCount",
                                value: doubleValidityCheck(row.nativeData.tags.get('TradeCount'), true),
                        ],
                        [
                                name : "ValueTodayBuyAvg",
                                value: doubleValidityCheck(row.nativeData.tags.get('ValueTodayBuyAvg'), true),
                        ],
                        [
                                name : "ValueTodaySellAvg",
                                value: doubleValidityCheck(row.nativeData.tags.get('ValueTodaySellAvg'), true),
                        ],
                        [
                                name : "Volume",
                                value: doubleValidityCheck(row.nativeData.tags.get('Volume'), true),
                        ],
                        [
                                name : "VolumeTodayBuy",
                                value: doubleValidityCheck(row.nativeData.tags.get('VolumeTodayBuy'), true),
                        ],
                        [
                                name : "VolumeTodaySell",
                                value: doubleValidityCheck(row.nativeData.tags.get('VolumeTodaySell'), true),
                        ],
                ],
                //counterparty: null,
                positionValue         : doubleValidityCheck(row.nativeData.tags.get('NetTradingPosNominal'), true),
                settledPositionValue  : doubleValidityCheck(row.nativeData.tags.get('SettledPosNominal'), false),
                unsettledPositionValue: doubleValidityCheck(row.nativeData.tags.get('UnsettledPosNominal'), false),
                currency              : UNKNOWN,
                bookValueCurrency     : UNKNOWN,
                bookValue             : 0.0,
                //bookValueGlAccountNo: null,
                traderId              : row.nativeData.tags.get('Trader'),
                // GFI-5317 All Regions - Data Publication on GBM DP: CAD.CM_POSITION.POSITION_TOR.POSITION.StatusStr
                positionStatus: row.nativeData.tags.get('StatusStr'),
                //sourceSystemSpecific: null,
        ],
]