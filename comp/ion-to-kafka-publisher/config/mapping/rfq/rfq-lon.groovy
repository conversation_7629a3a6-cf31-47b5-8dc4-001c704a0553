import java.text.SimpleDateFormat
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter

import static Constants.*

class Constants {
    static final UNKNOWN = "Unknown"
	static final SCHEMA_VERSION = "1.144"
    static final DEFAULT_DATE = "19000101"
    static final MESSAGE_TYPE_RFQ = "RfqMessage"
    static final TIMEZONE_CITY = "Europe/London"
    static final QUANTITY_TYPE = "UNIT"

    static stringNullCheck(String input) {
        if (input == "" || input == null)
            return UNKNOWN
        else
            return input
    }

    static getDate(String input, boolean mandatory) {
        try { // Deprecated: return Date.parse("yyyyMMdd", input)
            return new SimpleDateFormat("yyyyMMdd").parse(input).toInstant()
        } catch(Exception ex) {
            if (mandatory)
                return new SimpleDateFormat("yyyyMMdd").parse(DEFAULT_DATE).toInstant()
            else
                return null
        }
    }

    static doubleValidityCheck(String input, boolean mandatory) {
		if (input == "Infinity" || input == "NaN" || input == "" || input == null)
		{
            if (mandatory)
                return 0.0
            else
                return null
		}
		else
            return input
    }
}

static dateParse(String format, String input, TimeZone zone){
    def sdf = new SimpleDateFormat(format)
    sdf.setTimeZone(zone)
    def datetime = sdf.parse(input)
    return datetime.toInstant()
}


def getDateTime(String dateinput, String timeinput, boolean mandatory) {
    def datepart = getDate(dateinput, mandatory)
    if (datepart == null)
        return null
    try {
        // Process timeinput into timestr
        zoneId = ZoneId.of(TIMEZONE_CITY)
        def timestr = (timeinput != null && timeinput != "") ? String.format("%.0f", Double.parseDouble(timeinput)) : "000000"
        if (timeinput == "" || timeinput == null || timeinput == "0")
            timestr = "000000"
        timestr = timestr.padLeft(6, '0')
        if (timestr.length() > 6)
            timestr = timestr.substring(0, 6)

        // Combine date and time strings and parse to LocalDateTime
        def dateString = datepart.format(DateTimeFormatter.ofPattern("yyyyMMdd"))
        def dateTimeStr = dateString + timestr

        // Parse into LocalDateTime and convert to Instant
        def formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss")
        def dateTime = LocalDateTime.parse(dateTimeStr, formatter)
        return dateTime.atZone(zoneId).toInstant()
    } catch (Exception ex) {
        // Handle exception and return default date as Instant
        def defaultDate = LocalDate.parse(DEFAULT_DATE, DateTimeFormatter.ofPattern("yyyyMMdd"))
        return defaultDate.atStartOfDay(zoneId).toInstant()
    }
}

def getExPlatformId(String source) {
    def sourcesplit = source.tokenize('.')
    if (sourcesplit.size() < 3)
        return ""
    else
        return sourcesplit[2]
}

def getOrderStatus(String status) {
    switch (status) {
        case "Ac":
            return "Accepted"
            break
        case "Zf":
            return "Zero fill"
            break
        case "De":
            return "Deleted"
            break
        case "Cf":
            return "Filled"
            break
        case "Pf":
            return "PartiallyFilled"
            break
        case "Sus":
            return "Suspended"
            break
        case "Rej":
            return "Rejected"
            break
        default:
            return UNKNOWN
    }
    return UNKNOWN
}

def getOrderType(String type) {
    switch (type) {
        case ["Market", "Limit", "Stop", "StopLimit", "MarketOnClose", "WithOrWithout", "LimitOrBetter", "LimitWithOrWithout", "OnBasis", "LimitOnClose", "PreviouslyIndicated", "PreviouslyQuoted", "ForexLimit", "ForexSwap", "Funari", "Pegged"]:
            return type
            break
        case "OnClose":
            return "On Close"
            break
        case "ForexMarket":
            return "Forex Market"
            break
        case "ForexPreviouslyQuoted":
            return "Forex Previously Quoted"
            break
        case "MarketLimit":
            return "Market Limit"
            break
        case "OCO":
            return "One Cancels the Other Order"
            break
        case "BestLimit":
            return "Best Limit"
            break
        case "StopMarketLimit":
            return "Stop Market Limit"
            break
        default:
            return UNKNOWN
    }
    return UNKNOWN
}

def getOrderValidityPeriod(String timeinforce) {
    switch (timeinforce) {
        case "DAY":
            return "Day"
            break
        case "GFD":
            return "Good for day"
            break
        case "GTC":
            return "GoodTillCancel"
            break
        case "OPG":
            return "AtTheOpening"
            break
        case "IOC":
            return "ImmediateOrCancel"
            break
        case "FOK":
            return "FillOrKill"
            break
        case "GTX":
            return "GoodThroughCrossing"
            break
        case "GTD":
            return "GoodTillDate"
            break
        case ["GFA", "GTA"]:
            return "GoodForAuction"
            break
        case "OC":
            return "AtTheClose"
            break
        case "GFT":
            return "GoodForTime"
            break
        case "OPG_GTC":
            return "At the opening good till cancel"
            break
        case "OPG_GTD":
            return "At the opening good till date"
            break
        case "OPG_FAK":
            return "At the opening fill and kill"
            break
        case "FAK":
            return "Fill and Kill"
            break
        case "FAS":
            return "Fill and store"
            break
        case "STO":
            return "Storable"
            break
        case "GIS":
            return "Good in Session"
            break
        case "GTT":
            return "Good Till Time"
            break
        case "Passive":
            return "Passive"
            break
        case "Aggressive":
            return "Aggressive"
            break
        case "AON_Passive":
            return "All or nothing Passive"
            break
        case "AON_Aggressive":
            return "All or nothing Aggressive"
            break
        default:
            return UNKNOWN
    }
    return UNKNOWN
}

[
    header  : [
            messageId                    : row.header.messageId,
            businessId                   : row.header.businessId,
            batchId                      : null,
            sourceSystem                 : row.header.sourceSystem,
            secondarySourceSystem        : row.header.secondarySourceSystem,
            sourceSystemCreationTimestamp: row.header.sourceSystemCreationTimestamp,
            sentBy                       : row.header.sourceSystem,
            sentTo                       : row.header.sentTo,
            messageType                  : MESSAGE_TYPE_RFQ,
            schemaVersion                : SCHEMA_VERSION,
            processing                   : row.header.processing,
            recordOffset                 : null,
    ],
    rfq: [
        rfqHeader : [
            assetClass: "CASH_FIXEDINCOME",
            rfqSubmissionDateTime: null,
            quoteResponseDateTime: 1736444619390,
            quoteAcceptedDateTime: 1736444619390,
            quoteExecutedDateTime: null,
            quoteValidUntilTime: 1736444619000,
            quoteTimeToLive: 150.0,
            quoteStatus: "CustRejectedQuote",
            "quoteIdentifiers": [
                "clientRFQId": null,
                "rfqSequenceNumber": null,
                "quoteId": "67800A8045C400010002",
                "parentQuoteId": null,
                "previousQuoteId": null,
                "venueQuoteId": "67800A8045C400010002",
                "previousVenueQuoteId": null,
                "processedQuoteCount": null,
                "legs": null
            ],
            "venueInfo": [
                "executionVenueType": "MTF",
                "executionPlatformId": null,
                "isExchangeTraded": null,
                "exchangeCode": "BLOOM",
                "exchangeCodeType": null,
                "executionVenueLEI": null,
                "accountNumber": null,
                "applicationSessionIdentifier": null,
                "reportTrackingNumber": null
            ],
            "persons": [
                "tradeExecutorId": null,
                "tradeExecutorName": null,
                "traderId": "11671",
                "traderName": null,
                "traderLocation": null,
                "tradeCreatorId": null,
                "originatingTradeCreatorId": null,
                "salesPersonId": "********",
                "salesPersonName": null,
                "salesPersonLocation": null,
                "algorithmId": "50001",
                "algorithmName": null,
                "algorithmLocation": null,
                "eventUserName": null,
                "originalTradeMarketingUnitName": null,
                "usernote": null
            ],
            "regulatory": [
                "isdaUPIv1": null,
                "isdaUPIv2": null,
                "isdaAssetClass": null,
                "isdaBaseProductId": null,
                "isdaSubProductId": null,
                "isdaAdditionalSubProductId": null,
                "isdaTransactionType": null,
                "cfiCode": null,
                "isDoddFrankUsPerson": null,
                "isVolckerSpot": null,
                "isEmirSpot": null,
                "mifidTradingCapacity": "DEAL",
                "mifidTradingCapacityEnum": null,
                "mifidInvestmentDecisionWithinFirm": null,
                "mifidExecutionWithinFirm": null,
                "isMifidRTO": null,
                "isMifidPriceMaker": true,
                "isHedgeTrade": null,
                "isMifidAgencyTrade": null,
                "isMifidSecuritiesFinancingTrans": null,
                "isMifidCommodityDerivative": null,
                "mifidTransparencyFlag": null,
                "mifidWaiverIndicators": null,
                "mifidOtcPostTradeIndicators": null,
                "mifidInstrumentIdentificationType": null,
                "mifidInstrumentIdentificationCode": null,
                "isMifid2FinInstrument": null,
                "mifidLastLqdtyInd": null,
                "mifidBuySell": null,
                "mifidQuantity": null,
                "mifidQuantityCurrency": null,
                "mifidPriceCurrency": null,
                "mifidNotionalCurrencyAmount": null,
                "mifidOutstandingNotionalAmount": null,
                "mifidNotionalCurrency": null,
                "mifidNotionalCurrency1": null,
                "mifidNotionalCurrency2": null,
                "mifidOtherDetails": null,
                "shortSellingIndicator": null,
                "isMifidESCBExempt": null,
                "fixRegId": null,
                "counterpartyOrigin": null,
                "reportingRegime": null,
                "reportingRole": null,
                "endUserException": null,
                "nonStandardTerms": null,
                "offMarketPrice": null,
                "confirmTimeStamp": null,
                "isRightOfSubstitutionFlag": null,
                "isANEFlag": null,
                "isBlockTradeFlag": null,
                "isMarketMakerFlag": null,
                "isLocateRequiredFlag": null,
                "isLocateBrokerFlag": null,
                "locateIdentifier": null,
                "complianceIdentifier": null,
                "alphaTradeExecutionDateTime": null,
                "alphaTradeConfirmTimeStamp": null,
                "alphaTradeConfirmationIdentifier": null,
                "compressionProcessingIdentifier": null,
                "lastCapacity": null,
                "tradeIdRepeatingNumber": null,
                "tradePublicationGroupRepeatingNumber": null,
                "tradeRegulationPublicationType": null,
                "tradeRegulationPublicationReasonText": null
            ]
    ],
    "productIdentifier": [
    "sourceInternalId": null,
    "sourceInternalIdType": null,
    "sourceExternalId": null,
    "sourceExternalIdType": null,
    "sourceInstrumentName": "ALTA 1.300 07/30",
    "sourceInstrumentCategory": null,
    "sourceIsin": "US013051EM50",
    "sourceCusip": null,
    "sourceSedol1": null,
    "sourceSedol2": null,
    "primaryMarketId": null,
    "primaryMarketIdType": null,
    "adpIdentifier": null
    ],
    "quote": [
        "buySell": "Sell",
        "currency": "USD",
        "quoteType": "Tradeable",
        "inquiryProtocolType": null,
        "numberOfDealers": null,
        "clearingChannel": null,
        "preTradeMidMarketPackagePriceAmount": null,
        "preTradeMidMarketPriceTimestamp": null,
        "pricingengineMarketData": null,
        "pricingengineTradeOrPortfolioData": null,
        "priceTier": null,
        "customerPackagePriceAmount": null,
        "dealerPackagePriceAmount": null,
        "streamingPriceId": null,
        "streamingPriceGenerationTimestamp": null,
        "streamingPriceStatus": null,
        "streamingPackagePriceAmount": null,
        "streamingPackageMidPriceAmount": null,
        "packageTradeType": null,
        "bids": null,
        "offers": [
                [
                    "priceType": "Percentage",
                    "price": 83.433,
                    "unitPrice": null,
                    "priceMultiplier": 1.0,
                    "size": 130000.0,
                    "remainingSize": null,
                    "cumulativeSize": null,
                    "sizeType": "UNIT"
                ]
        ],
        "asks": null
    ],
    "structuredMktQuotes": null,
    "counterparty": [
        "partyId": null,
        "partyIdType": null,
        "partyName": null,
        "partyShortName": null,
        "partyLei": "549300WOIFUSNYH0FL22",
        "cardsId": null,
        "ccdId": null,
        "clientMasterId": null,
        "partyStatusCode": null,
        "traderId": null,
        "traderName": null,
        "additionalPartyAttributes": null
    ],
    "book": [
        "bookingPoint": null,
        "bookId": null,
        "bookDescription": null,
        "scotiaLegalEntityId": "L3I9ZG2KFGXZ61BMYR72",
        "transitNumber": null,
        "transitDescription": null,
        "legalEntityName": null,
        "countryId": null,
        "departmentCode": null,
        "departmentName": null,
        "riskBook": null,
        "bookOriginName": null,
        "transferBookName": null,
        "counterpartyBookName": null,
        "bookStrategy": null
    ],
    "productLookupInfo": null,
    "acceptanceDetails": null
    ],
]
