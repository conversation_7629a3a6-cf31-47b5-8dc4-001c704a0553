# Router name (to connect this component using inOut connection) to subscribe to:
# CAD.CM_TRADESPLIT.TRADESERVER_TOR.TRADESPLIT chain: ROUTERM_KAFKA_TOR
mkv.COMPONENT=KAFKA_PUB_TRADES_SPLIT_WIN_TOR
mkv.CSHOST=sdusvrwm0128.dev.ib.tor.scotiabank.com
#mkv.CSHOST=sctfiqlpa006trb.options-it.com
mkv.CSPORT=24001
mkv.DBDIR=db/KAFKA_PUB_TRADES_SPLIT_WIN_TOR
# mkv.LISTEN=24001
mkv.LISTEN=*
mkv.LOGSDIR=log/KAFKA_PUB_TRADES_SPLIT_WIN_TOR
mkv.user = apppub
#mkv.pwd = apppub123
mkv.pwd = :4F 7C 36 27 12 40 86 EF BC A4 4A BD D9 F4 8D CB
mkv.currency=CAD
mkv.source=KAFKA_PUB_TRADES_SPLIT_WIN_TOR
mkv.usemsecs=3
mkv.entitlement=0

#mkv.ZoneId = America/New_York
mkv.logLevel = DEBUG
mkv.debuglevel=1
mkv.logsdays=5
mkv.logPrintTime = 1
mkv.logPrintThreadName = 1

# Enables traces and logging on log files and define tracing detail.
# Setting mkv.debug to a value different from 0 enables all tracing level >= DEBUG.
# -1 Maximum detail: adds supplied fields.
# 0 Logging disabled.
# 10 Adds supply events.
# 100 Minimum detail. Enables log for: errors, connectivity events, statistics, platform events, and settings.
# 30 Adds object publishing events.
# 50 Adds transaction and function events.
# 90 Adds subscription events.
# Min: -10000, Max: 10000
mkv.DEBUG=50
mkv.logName=KAFKA_PUB_TRADES_SPLIT_WIN_TOR.log
mkv.logToScreen=true

mkv.platform.serviceName=PUB
# period – time in seconds between successive Inner class for handling retry logic executions
mkv.retryInterval=180
mkv.thread.pool.size=1
mkv.timer.timeout = 300
 
