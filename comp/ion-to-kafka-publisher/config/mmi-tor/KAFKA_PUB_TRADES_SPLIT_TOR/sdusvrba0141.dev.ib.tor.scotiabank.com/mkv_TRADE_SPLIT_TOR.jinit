# Router name (to connect this component using inOut connection) to subscribe to:
# CAD.CM_TRADESPLIT.TRADESERVER_TOR.TRADESPLIT chain: ROUTERM_KAFKA_TOR
mkv.COMPONENT=KAFKA_PUB_TRADES_SPLIT_TOR
mkv.CSHOST=sdusvrwm0128.dev.ib.tor.scotiabank.com
mkv.CSPORT=24001
mkv.LISTEN=*
#mkv.LOGSDIR=log/KAFKA_PUB_TRADES_SPLIT_TOR
mkv.user = apppub
#mkv.PWD = apppub123
mkv.env = DEV
#mkv.pwd = :4F 7C 36 27 12 40 86 EF BC A4 4A BD D9 F4 8D CB
mkv.currency=CAD
mkv.source=KAFKA_PUB_TRADES_SPLIT_TOR
mkv.usemsecs=3
mkv.entitlement=0

#mkv.ZoneId = America/New_York
mkv.logLevel = DEBUG
mkv.debuglevel=1
mkv.logsdays=30
mkv.logPrintTime = 1
mkv.logPrintThreadName = 1

mkv.DEBUG=50
mkv.logName=KAFKA_PUB_TRADES_SPLIT_TOR.log
mkv.logToScreen=true

mkv.platform.serviceName=PUB
mkv.retryInterval=180
mkv.thread.pool.size=1
mkv.timer.timeout = 300
 
mkv.db.connection.pwd=:F6 BE 8D 62 58 65 1F BA
#mkv.db.connection.uid=FIETOAPP
#mkv.db.connection.url=*********************************************
 