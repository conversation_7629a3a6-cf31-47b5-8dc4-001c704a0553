[{"comment": ["ionFields - corresponding fields on chain to evaluate target Kafka object (targetClassName). * - means all fields"], "name": "RT_MMI_TRADESPLIT", "chain": "CAD.CM_TRADESPLIT.TRADESERVER_TOR.TRADESPLIT", "enable": true, "statusRecord": "CAD.CM_TRADESPLIT.TRADESERVER_TOR.TRADESPLIT.GSTATUS", "table": "KAFKA_PUB", "keys": ["messageId"], "businessIdField": "Id", "targetClassName": "com.scm.fi.sa.kafka.serializers.plugins.Row", "conversionRules": null, "groovyMappingScript": "row", "targetTopic": "prod.landing.mmi-tor.tradesplit.realtime", "targetControlTopic": "prod.landing.mmi-tor.tradesplit.realtime.control", "targetESIndice": "prod.landing.mmi-tor.tradesplit.realtime-1", "ionFields": ["*"]}]