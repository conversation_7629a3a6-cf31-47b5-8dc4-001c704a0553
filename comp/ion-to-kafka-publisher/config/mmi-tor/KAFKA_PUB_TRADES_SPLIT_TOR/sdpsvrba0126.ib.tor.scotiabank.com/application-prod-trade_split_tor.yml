########################################################################################################################
# Dynamic Configuration: Consider externalizing Kafka settings that might change frequently or depending on the
# environment (like bootstrap-servers, security settings) to avoid redeployment's for configuration changes.
# Kafka related parameters was used from KAFKA_PUB1_TOR component
########################################################################################################################
server:
  port: 8098                  # Application server port
  servlet:
    context-path: /
  compression:
    enabled: true
  cors:
    allowed-origins: "*"
    allowed-methods: GET,POST
  error:
    # Hide error details from response
    include-stacktrace: never
    include-message: never
  #address: 0.0.0.0            # Bind to all network interfaces to allow remote connections
  tomcat:
    max-threads: 200
    threads.max: 200     # Maximum number of threads for Tomcat server
    threads.min-spare: 1 # Minimum spare threads for Tomcat serverApplication Specific Properties
    max-http-form-post-size: 2MB
    # Security configs
    max-http-header-size: 8KB
    max-connections: 10000
    connection-timeout: 5000
    reject-illegal-header-values: true
    remoteip:
      remote-ip-header: x-forwarded-for
      protocol-header: x-forwarded-proto
    # Add protection against malformed requests
    uri-encoding: UTF-8
    max-swallow-size: 2MB
    relaxed-query-chars:
    relaxed-path-chars:
    additional-tld-skip-patterns: "*.jsp,*.php,*.asp,*.aspx,*.do,*.action,*.cgi,*.dll,*.exe,*.pl,*.shtml"
    # Only allow GET requests to root
    url-pattern: "/"

# Logging configuration is outside of jar file:
logging.config: file:/opt/bns/ion/TOR/bin/KAFKA_PUB/KAFKA_PUB_TRADES_SPLIT_TOR/config/logback-spring.xml
########################################################################################################################
# Custom Ion2KafkaPubApp Properties
########################################################################################################################
app:
  ionComponentName: KAFKA_PUB_TRADES_SPLIT_TOR # Needed for DB operations: to remove old records and re-publish records with (is_published==0)
  skipDbOperations: false
  skipDbOperationsInCaseOfError: true
  republishRecordsFromDB: true   # republish records from KAFKA_PUB records with is_published=0 at starting time
  # How many hours to keep records for redundancy in KAFKA_PUB table
  # To remove records from KAFKA_PUB that older than (in hours):
  dataRemovalHours: 168          # Retention Policy: To remove data older than this number of hours
  display-record-step: 200       # Step To display publishing records in info log level (to control log file size)
  isNaNValuesLog: false          # print "NaN values received for field..." warnings to the log (since it could be many)
  run-free-memory-thread: true   # Run Memory Diagnostic Thread (logFreeMemory)
  log-free-memory.interval: 120  # Call logFreeMemory() function every (seconds):
  data-time-zone: Europe/London  # Possible values are: America/Toronto, America/New_York, Europe/London, Asia/Singapore
  refreshInterval: 5000          # Web GUI: Refresh interval in milliseconds for Metrics GUI (http://localhost:8080/)
  errorNumberToDisplay: 10       # Web GUI: How many last errors to shows on web GUI
  # data-time-zone: America/Toronto # Possible values are: America/Toronto, America/New_York, Europe/London, Asia/Singapore
  config:
    dateFormat: yyyy-MM-dd
    #dateFormat: yyyy-MM-dd'T'HH:mm:ss.SSSZZ           # Date format
    path: /opt/bns/ion/TOR/bin/KAFKA_PUB/KAFKA_PUB_TRADES_SPLIT_TOR/config/ # path to json mappings configuration files: value could be overwritten from command line by: ION_KAFKA_CONFIG
    mapping: /opt/bns/ion/TOR/bin/KAFKA_PUB/KAFKA_PUB_TRADES_SPLIT_TOR/config/mapping/     # path to Groovy mapping scripts (from source (Row) to a target object)
    data-extract-mappings: dataExtract.json # Chains, Field list to subscribe to Ion and corresponding Kafka topic
    format-time-field: false      # Format time field from "0" or "" to 000000
    convert-empty-date: false     # Convert empty date "0" or "" to 19000101
    column-symbol-for-time: false # Use column symbol for time: 12:34:56
    dash-symbol-for-date: false   # Use dash symbol for date: 2020-01-01
    control-topic-publish: true   # Publish control topic messages in case of source data issue(s)    
    convert-empty-data-to-null: true # Convert empty values to null for landing (Row) objects
    double-field-precision: 11       # Define fractional part precision to round double (REAL) MMI fields for landing (Row) object
    date-min-int-conversion: 3       # dateMinIntConversion - Define how to convert Integer.MIN_VALUE (-2147483647) date value: 0 to 'null', 1 to '0', 2 to '19000101', 3 - no changes
    add-source-tag: false            # Add source tag (with chain name) to the landing (Row) object

########################################################################################################################
# Asynchronous Execution Configuration:
# Parameters for AsyncConfig: to run all methods with @Async
########################################################################################################################
async:
  executor:
    corePoolSize: 10                 # Core pool size of executor, for a maximum performance this number should be equal to logical processors#
    maxPoolSize: 20                  # Maximum pool size of executor
    keepAliveSeconds: 60
    queueCapacity: 500              # Capacity of the executor queue
    threadNamePrefix: KafkaPublisher- # Prefix for thread names created by the executor: Related Thread will be named like KafkaPublisher-1,2,3,..

########################################################################################################################
# Spring related properties
########################################################################################################################
spring:
  application.name: TradeSplitTor
  main.banner-mode: off
  main.allow-bean-definition-overriding: true
  groovy.template.check-template-location: false # To disable GroovyTemplateAutoConfiguration
  devtools:
    restart.enabled: false
    livereload.enabled: false
  task:
    execution.thread-name-prefix: task-
    scheduling:
      thread-name-prefix: scheduling-
      shutdown.await-termination: true
  ######################################################################################################################
  # Spring Kafka settings:
  #   https://confluence.agile.bns/display/ATH/How+To+Migrate+to+NEW+IST+Kafka
  #   https://confluence.agile.bns/pages/viewpage.action?pageId=264801581
  #   https://confluence.agile.bns/pages/viewpage.action?pageId=265587791&showComments=true
  # Kafka related VM Parameters:
  #   -Djavax.security.auth.useSubjectCredsOnly=true
  #   -Dsun.security.krb5.disableReferrals=true
  #   -Djava.security.auth.login.config=c:/KAFKA/dev/kafka_client_jaas.conf
  #   -Djava.security.krb5.conf=c:/KAFKA/dev/krb5_ist.conf
  #   -Dspring.kafka.ssl.trust-store-location=file:c:\\kafka\\dev\\kafka.client.truststore.jks
  #   -Dspring.kafka.ssl.trust-store-password=kafkaclientssl ######################################################################################################################
  kafka:
    #bootstrap-servers: dp.bns:9040
    bootstrap-servers: dp.bns:9030
    streams.application-id: kafka-streams-app-dev
    properties:
      telemetry.enabled: false
      telemetry.reporter.enabled: false
      max.poll.interval.ms: 600000
      retry.backoff.ms: 1000
      retry.max.backoff.ms: 10000
      max.block.ms: 120000
      metadata.max.age.ms: 180000
      request.timeout.ms: 30000
      reconnect.backoff.ms: 1000
      reconnect.backoff.max.ms: 10000
      bootstrap.backoff.ms: 500
      # Kafka producer will use SASL (Simple Authentication and Security Layer) for authentication and SSL (Secure Sockets Layer) for encryption1.
      #security.protocol: SASL_SSL
      security.protocol: SASL_PLAINTEXT
      ssl.endpoint.identification.algorithm: ""  # Disable hostname verification
      # The Schema Registry is a service that provides a RESTful interface for storing and retrieving Avro schemas.
      # It maintains a versioned history of all schemas, provides compatibility settings, and allows evolution of schemas over time.
      schema.registry.url: https://dp.bns:1443
      #ssl: # - moved to VM command line parameters
      # trust-store-location: "file:\\c:\\kafka\\dev\\kafka.client.truststore.jks"
      #trust-store-location: "file:/tmp/kshiff/IonKafkaPublisher/config/kafka/devkafka.client.truststore.jks"
      #trust-store-password: kafkaclientssl
      sasl.mechanism: GSSAPI
      sasl.kerberos.service: kafka      
    producer:
      buffer-memory: 437248000 # 437M - used to hold the records that are waiting to be sent to the Kafka broker
      # Acknowledgment level: all- producer.send() call will wait for all the brokers (Kafka servers) to respond that
      # they have received the message. This is the most robust setting for guaranteed message delivery, but also the most
      # costly (involves multiple servers to write to disk and respond before the producer gets the acknowledgment)
      acks: all
      compression.type: lz4
      # properties.enable.idempotence: true  # true -  ensures that duplicates are not introduced due to unexpected retries.
      # properties.enable.idempotence: true  # true -  ensures that duplicates are not introduced due to unexpected retries.
      # This identifier will be included in the metadata sent to the broker, making it easier to distinguish between
      # different producers in server logs and monitoring tools.
      client-id: mmi_kafka@BNS
      retries: 7200 # How many times the producer will attempt to send a message before marking it as failed.
      # How many bytes (which translates to messages) can be packaged into a single network request to a Kafka server,
      # thereby reducing network traffic and increasing performance. It defaults to 16384 bytes
      batch-size: 131072                 
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: io.confluent.kafka.serializers.KafkaAvroSerializer
      properties:
        linger.ms: 1000 # Number of milliseconds a producer is willing to wait before sending a batch to the Kafka
        # true - producer will ensure that exactly one copy of each message is written in the stream.
        # false - producer retries due to broker failures, etc., may write duplicates of the retried message in the
        # stream. Note that enabling idempotence requires max.in.flight.requests.per.connection to be less than or
        # equal to 5 (with message ordering preserved for any allowable value), retries to be greater than 0,
        # and acks must be 'all'.
        enable.idempotence: true
        retry.backoff.ms: 1000      # the producer will wait between retries (for individual operations) in ms
        # How many requests can be made in parallel to any partition1. Determines the maximum number of unacknowledged
        # requests the client will send on a single connection before blocking.
        # To strictly ensure ordering set it to 1 and properties.enable.idempotence: true
        # For stress test csv publisher this value should be 10 (or more), for other cases "1"
        max.in.flight.requests.per.connection: 5  # Must be <=5 for idempotence
        # The maximum amount of time the producer will wait for a response from the Kafka broker for each request.
        # If the broker does not respond within this timeout, the producer will consider the request as failed
        # delivery timeout ms=(request timeout ms+linger ms)×retries
        # delivery.timeout.ms should be >= than linger.ms + request.timeout.ms
        request.timeout.ms: 2600000   # Increase for Debug Mode
        delivery.timeout.ms: 3000000  # Record will be failed if they can’t be delivered in 46 minutes
        # maximum idle time for a connection to remain open without sending or receiving any data.
        # If a connection is idle for longer than this period, the client will proactively close it. This helps manage
        # resources by closing unused connections and can improve scalability in environments with many clients.
        connections.max.idle.ms: 540000  # 9 minutes
        # The max.block.ms property specifies the maximum amount of time (in milliseconds) the KafkaProducer send()
        # and partitionsFor() methods will block. This occurs when the producer's send buffer is full, and the producer
        # can't accept more records due to backpressure from the broker or network issues.
        # If the time limit is exceeded, a TimeoutException is thrown.
        max.block.ms: 120000 # 2 minutes
        # The delay in ms between consecutive connection attempts made by the producer or consumer when they lose their
        # connection to the Kafka broker. Should be balanced to handle transient errors without overwhelming the system.
        # retry.backoff.ms property is specific to retries for individual operations, while reconnect.backoff.ms is for
        # reconnections when the producer or consumer loses the connection to the broker.
        reconnect.backoff.ms: 1000
        # maximum amount of time to wait when exponentially backing off between reconnection attempts
        reconnect.backoff.max.ms: 10000
########################################################################################################################
# Spring DB related properties
########################################################################################################################
  datasource:
    driver-class-name: oracle.jdbc.OracleDriver
    url: *****************************************
    username: FIETOAPP
    # Encrypted values from JINIT field: mkv.db.connection.pwd
    password: ":C2 25 42 28 49 6B F5 15 46 09 8F 98 20 42 F1 7A"
    mkv-platform-serviceName: PUB  # mkv.platform.serviceName - we use to encrypt jinit DB password
    hikari: # All default parameters are set in DatabaseConfig could be overwritten here
  jpa:
    show-sql: false
    open-in-view: false
    hibernate.ddl-auto: none
    # specific Hibernate properties to address the ORA-22923 error
    properties:
      jakarta.persistence.query.timeout: 30000
      hibernate:
        jdbc:
          batch_size: 100
        order_inserts: true
        order_updates: true
  transaction:
    default-timeout: 1800   # 30 minutes
    rollback-on-commit-failure: true
  sql.init.mode: never
  # controls how frequently HikariCP will attempt to keep a connection alive, in order to prevent it from being timed out by the database or network infrastructure
  web:
    resources:
      static-locations: classpath:/static/,classpath:/public/
      add-mappings: true
    cors: # CORS configuration for development
      allowed-origins: http://localhost:4200
      allowed-methods: GET,POST,PUT,DELETE,OPTIONS
      allowed-headers: "*"
  mvc:
    throw-exception-if-no-handler-found: false
    static-path-pattern: /**
  security:
    allowed-paths: ["/", "/index.html", "/static/**", "/css/**", "/js/**"]
    # Block all paths except root
    ignored: none
    # Block common scan patterns
    denied-paths: ["/**/*.php", "/**/*.jsp", "/**/*.do", "/**/*.shtml", "/**/*.cgi", "/**/*.asp", "/**/*.aspx", "/**/*.dll", "/**/*.exe", "/**/*.pl", "/**/admin/**", "/**/tmui/**", "/**/mgmt/**", "/**/scgi-bin/**", "/**/login.tdf", "/**/ptz.htm", "/api/**", "/metrics/**", "/actuator/**", "/*.sql", "/*.bak", "/*.old", "/*.conf", "/*.config", "/*.ini", "/*.log", "/*.env", "/*.git*", "/*.htaccess", "/*.htpasswd", "/*.idea/**", "/*.svn/**", "/*.DS_Store", "/WEB-INF/**"]
    # Add headers
    headers:
      x-frame-options: DENY
      strict-transport-security: max-age=31536000; includeSubDomains
      x-content-type-options: nosniff
      x-xss-protection: 1; mode=block
      cache-control: no-cache, no-store, max-age=0, must-revalidate
      pragma: no-cache
      expires: 0

########################################################################################################################
# spring-boot-starter-actuator related properties: /actuator/health
########################################################################################################################
management:
  metrics:
    enable:
      hikaricp: true
      jvm: true
      process: true
  endpoints:
    web.exposure.include: 'health,info,configprops'  # http://localhost:8088/actuator/configprops
    access:
      default:
  endpoint:
    configprops:
      access:
    info:
      access:

kafka:
  # parameters to generate MessageId to publish (usually empty)
  # null value is returns as empty string ("") from yaml file => therefore, we used "null-value" as a value to distinguish it from empty string
  GenericKafkaProducer.sa-secondary-source-system: null-value
  GenericKafkaProducer.sa-message-id-prefix: mmi-tor
  # 1 - means to use separate thread for each source csv file, 0 - means to use single thread to publish all csv files
  isKafkaMultiThreadingPublishing: false
ssl.endpoint.identification.algorithm:
