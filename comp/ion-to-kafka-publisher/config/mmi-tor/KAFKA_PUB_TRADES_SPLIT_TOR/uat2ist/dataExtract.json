[{"comment": ["ionFields - corresponding fields on chain to evaluate target Kafka object (targetClassName)"], "name": "RT_MMI_TRADESPLIT", "chain": "CAD.CM_TRADESPLIT.TRADESERVER_TOR.TRADESPLIT", "enable": true, "statusRecord": "CAD.CM_TRADESPLIT.TRADESERVER_TOR.TRADESPLIT.GSTATUS", "table": "KAFKA_PUB", "keys": ["messageId"], "businessIdField": "Id", "targetClassName": "com.scm.fi.sa.kafka.serializers.plugins.Row", "groovyMappingScript": "row", "targetTopic": "ist2.enterprise.mmi-tor.price.realtime.v1", "targetControlTopic": "ist.landing.mmi-lon.price.eod.control", "targetESIndice": "ist2.enterprise.mmi-tor.price.realtime.v1-1", "ionFields": ["*"]}]