[{"comment": ["region is input parameter for plug-in-class"], "name": "RT_MMI_TRADE", "chain": "CAD.PRICERISK.PXE_TOR.PRICERISK", "enable": true, "statusRecord": "CAD.PRICERISK.PXE_TOR.GSTATUS", "table": "KAFKA_PUB", "keys": ["messageId"], "businessIdField": "Id", "targetClassName": "com.scotia.gcm.sa.v1.marketdata.PriceDataMessage", "conversionRules": null, "groovyMappingScript": "plug-in-class:com.scm.fi.sa.kafka.serializers.plugins.PriceDataMessageBuilder", "region": "TOR", "targetTopic": "ist2.enterprise.mmi-tor.price.realtime.v1", "targetControlTopic": "ist2.enterprise.mmi-tor.price.realtime.control.v1", "targetESIndice": "ist2.enterprise.mmi-tor.price.realtime.v1-1", "ionFields": ["Id", "InstrumentId", "AskPrice", "BidPrice", "MidPrice", "AskYield", "BidYield", "MidYield", "CurrentClosingPriceAsk", "CurrentClosingPriceBid", "CurrentClosingPriceMid", "CurrentClosingYieldAsk", "CurrentClosingYieldBid", "CurrentClosingYieldMid", "CurrentClosingDateAsDate", "SAYldBid", "SAYldAsk", "Date", "Time", "ModelStr", "BenchmarkUnderlyingCusip", "BenchmarkUnderlyingIsin"]}]