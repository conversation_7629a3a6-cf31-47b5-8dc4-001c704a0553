<?xml version="1.0" encoding="UTF-8"?>
<!-- 1. Maintains one log file per application run session (up to 150MB)
	 2. Does not create new files at midnight
	 3. Compresses files after 2 days (only if they are larger than 10MB)
	 4. Removes original files after compression
	 5. Does not require a cron job (or any script) for log rotation
	 6. Keep logs for 30 days and errors for 60 days
	 7. Total size cap across all archives; 5GB for logs, 2GB for errors 
	 8. Log and error file names includes full data/time patten according to application start -->
<configuration>
    <statusListener class="ch.qos.logback.core.status.NopStatusListener" />
    
    <!-- Application startup timestamp for unique filenames -->
    <timestamp key="startTime" datePattern="yyyy-MM-dd_HH-mm-ss"/>
    
    <!-- Property for log file location -->
    <property name="LOG_DIR" value="log/KAFKA_PUB2_TOR_IST_WIN_SS" />
    <property name="ARCHIVE_DIR" value="${LOG_DIR}" />
    <property name="COMPONENT_NAME" value="KAFKA_PUB2_TOR_IST_WIN_SS" />

    <!-- Console appender for development -->
    <appender name="Console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSSXXX}: %-5level [%thread] %-3.33logger{39} : %msg%n</pattern>
        </encoder>
    </appender>

    <!-- Main file appender - pure size-based for runtime -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_DIR}/${COMPONENT_NAME}.${startTime}.log</file>
        <encoder>
            <pattern>%d{yy-MM-dd HH:mm:ss.SSS} %-5level [%thread] %logger{36} - %msg%n</pattern>
        </encoder>
        
        <!-- Size-based policy to prevent midnight rollovers -->
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>${LOG_DIR}/${COMPONENT_NAME}.${startTime}.%i.log.zip</fileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>20</maxIndex>
        </rollingPolicy>
        
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>150MB</maxFileSize>
        </triggeringPolicy>
    </appender>

    <!-- Error log file - Size-based rolling -->
    <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_DIR}/${COMPONENT_NAME}-Errors.${startTime}.log</file>
        <encoder>
            <pattern>%d{yy-MM-dd HH:mm:ss.SSS} %-5level [%thread] %logger{36} - %msg%n</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        
        <!-- Similar size-based policy for error logs -->
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>${LOG_DIR}/${COMPONENT_NAME}-Errors.${startTime}.%i.log.zip</fileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>20</maxIndex>
        </rollingPolicy>
        
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>150MB</maxFileSize>
        </triggeringPolicy>
    </appender>

    <!-- A simple daily appender to keep Logback happy -->
    <appender name="DAILY_TRIGGER" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_DIR}/daily-trigger.log</file>
        <encoder>
            <pattern>%d{yy-MM-dd HH:mm:ss.SSS} Daily check running%n</pattern>
        </encoder>
        
        <!-- This creates a properly-formatted time-based policy -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_DIR}/daily-trigger.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>7</maxHistory>
            <totalSizeCap>10MB</totalSizeCap>
        </rollingPolicy>
    </appender>
    

    <!-- Root Logger -->
    <root level="INFO">
        <appender-ref ref="Console"/>
        <appender-ref ref="FILE"/>
        <appender-ref ref="ERROR_FILE"/>
        <appender-ref ref="DAILY_TRIGGER"/>
    </root>

    <!-- Logger configurations from an original file -->
    <logger name="com.scm.fi" level="DEBUG" />
    <logger name="ch.qos.logback" level="WARN"/>
    <logger name="org.springframework.web.servlet.resource.ResourceHttpRequestHandler" level="ERROR" />
    <logger name="org.springframework.web.servlet.PageNotFound" level="ERROR" />
    <logger name="com.iontrading" level="INFO"/>
    <logger name="org.springframework" level="INFO" />
    <logger name="org.apache.coyote" level="INFO" />
    <logger name="org.springframework.web" level="INFO" />
    <logger name="org.apache.kafka.common.utils.AppInfoParser" level="WARN"/>
    <logger name="org.apache.kafka.common.internals.TransactionManager" level="WARN"/>
    <logger name="org.hibernate.internal.util" level="INFO" />
    <logger name="org.apache.kafka.clients.producer" level="WARN"/>
    <logger name="org.apache.kafka.clients.Metadata" level="WARN"/>
    <logger name="org.apache.kafka.clients" level="WARN"/>
    <logger name="org.apache.kafka.common.security.kerberos.KerberosLogin" level="WARN"/>
    <logger name="kafka-kerberos-refresh-thread" level="WARN"/>
    <logger name="org.hibernate.SQL" level="INFO"/>
    <logger name="org.hibernate.type.descriptor.sql.BasicBinder" level="INFO"/>
    <logger name="com.zaxxer.hikari.HikariConfig" level="INFO" />
    <logger name="com.zaxxer.hikari" level="INFO" />
    <logger name="che.coyote.http11.Http11Processor" level="ERROR"/>
    <logger name="org.springframework.web.servlet.mvc.support.DefaultHandlerExceptionResolver" level="ERROR" />
    <logger name="org.springframework.web.servlet.resource.ResourceHandlerUtils" level="ERROR" />
    <logger name="org.apache.coyote.http11.Http11Processor" level="ERROR" />
    <logger name="org.apache.catalina.core.StandardService" level="ERROR" />
    <logger name="org.apache.catalina" level="ERROR" />
    <logger name="org.springframework.web.servlet.mvc.support.DefaultHandlerExceptionResolver" level="ERROR" />
    <logger name="org.springframework.web.servlet.resource.ResourceHandlerUtils" level="ERROR" />
    <logger name="org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping" level="ERROR"/>
    <logger name="org.apache.tomcat.util.http.parser.Cookie" level="ERROR"/>
</configuration>
