<?xml version="1.0" encoding="UTF-8"?>
<!-- 1. Maintains one log file per application run session (up to 150MB)
	 2. Does not create new files at midnight
	 3. Compresses files after 2 days (only if they are larger than 10MB)
	 4. Removes original files after compression
	 5. Does not require a cron job for log rotation
	 6. Keep logs for 30 days and errors for 60 days
	 7. Total size cap across all archives; 5GB for logs, 2GB for errors -->
<configuration>
    <statusListener class="ch.qos.logback.core.status.NopStatusListener" />
    <!-- Define a timestamp to use in log filenames that's generated at application start -->
    <timestamp key="startTime" datePattern="yyyy-MM-dd_HH-mm-ss"/>

    <!-- Property for log file location -->
    <property name="LOG_DIR" value="log/KAFKA_PUB2_TOR_IST_WIN_SS" />
    <property name="ARCHIVE_DIR" value="${LOG_DIR}" />
    <property name="COMPONENT_NAME" value="KAFKA_PUB2_TOR_IST_WIN_SS" />

    <!--logger levels are set in order of verbosity: TRACE < DEBUG < INFO < WARN < ERROR -->
    <appender name="Console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSSXXX}: %-5level [%thread] %-3.33logger{39} : %msg%n</pattern>
        </encoder>
    </appender>

    <!-- Main file appender - one file per app start -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_DIR}/${COMPONENT_NAME}.${startTime}.log</file>
        <append>true</append>
        <encoder>
            <pattern>%d{yy-MM-dd HH:mm:ss.SSS} %-5level [%thread] %logger{36} - %msg%n</pattern>
        </encoder>

        <!-- Using SizeAndTimeBasedRollingPolicy to trigger based on both size and age -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- The archived file pattern with %i for size-based incrementing -->
            <fileNamePattern>${ARCHIVE_DIR}/${COMPONENT_NAME}.${startTime}.%d{yyyy-MM-dd}.%i.zip</fileNamePattern>
            <!-- Maximum size of 150MB before creating a new file during application run -->
            <maxFileSize>150MB</maxFileSize>
            <!-- Zip files older than 2 days -->
            <maxHistory>30</maxHistory>
            <!-- Total size cap across all archives -->
            <totalSizeCap>5GB</totalSizeCap>
            <!-- Skip compression for files smaller than 10MB -->
            <minFileSize>10MB</minFileSize>
            <!-- Clean up older archives -->
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
    </appender>

    <!-- Error file appender - one file per app start -->
    <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_DIR}/${COMPONENT_NAME}-Errors.${startTime}.log</file>
        <append>true</append>
        <encoder>
            <pattern>%d{yy-MM-dd HH:mm:ss.SSS} %-5level [%thread] %logger{36} - %msg%n</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>

        <!-- Using SizeAndTimeBasedRollingPolicy to trigger based on both size and age -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- The archived file pattern with %i for size-based incrementing -->
            <fileNamePattern>${ARCHIVE_DIR}/${COMPONENT_NAME}-Errors.${startTime}.%d{yyyy-MM-dd}.%i.zip</fileNamePattern>

            <!-- Maximum size of 150MB before creating a new file during application run -->
            <maxFileSize>150MB</maxFileSize>

            <!-- Zip files older than 2 days -->
            <maxHistory>60</maxHistory>

            <!-- Total size cap across all archives -->
            <totalSizeCap>2GB</totalSizeCap>

            <!-- Skip compression for files smaller than 10MB -->
            <minFileSize>10MB</minFileSize>

            <!-- Clean up older archives -->
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
    </appender>

    <!-- Root Logger -->
    <root level="INFO">
        <appender-ref ref="Console"/>
        <appender-ref ref="FILE"/>
        <appender-ref ref="ERROR_FILE"/>
    </root>

    <!-- ProducerConfig.DEBUG_CONFIG, "all" -->
    <logger name="com.scm.fi" level="DEBUG" />
    <logger name="ch.qos.logback" level="WARN"/>
    <logger name="org.springframework.web.servlet.resource.ResourceHttpRequestHandler" level="ERROR" />
    <logger name="org.springframework.web.servlet.PageNotFound" level="ERROR" />
    <logger name="com.iontrading" level="INFO"/>
    <logger name="org.springframework" level="INFO" />
    <logger name="org.apache.coyote" level="INFO" />
    <logger name="org.springframework.web" level="INFO" />
    <logger name="org.apache.kafka.common.utils.AppInfoParser" level="WARN"/>
    <logger name="org.apache.kafka.common.internals.TransactionManager" level="WARN"/>
    <logger name="org.hibernate.internal.util" level="INFO" />
    <logger name="org.apache.kafka.clients.producer" level="WARN"/>
    <logger name="org.apache.kafka.clients.Metadata" level="WARN"/>
    <logger name="org.apache.kafka.clients" level="WARN"/>
    <logger name="org.apache.kafka.common.security.kerberos.KerberosLogin" level="WARN"/>
    <logger name="kafka-kerberos-refresh-thread" level="WARN"/>
    <logger name="org.hibernate.SQL" level="INFO"/>
    <logger name="org.hibernate.type.descriptor.sql.BasicBinder" level="INFO"/>
    <logger name="com.zaxxer.hikari.HikariConfig" level="INFO" />
    <logger name="com.zaxxer.hikari" level="INFO" />
    <logger name="che.coyote.http11.Http11Processor" level="ERROR"/>
    <logger name="org.springframework.web.servlet.mvc.support.DefaultHandlerExceptionResolver" level="ERROR" />
    <logger name="org.springframework.web.servlet.resource.ResourceHandlerUtils" level="ERROR" />
    <logger name="org.apache.coyote.http11.Http11Processor" level="ERROR" />
    <logger name="org.apache.catalina.core.StandardService" level="ERROR" />
    <logger name="org.apache.catalina" level="ERROR" />
    <logger name="org.springframework.web.servlet.mvc.support.DefaultHandlerExceptionResolver" level="ERROR" />
    <logger name="org.springframework.web.servlet.resource.ResourceHandlerUtils" level="ERROR" />
    <logger name="org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping" level="ERROR"/>
    <logger name="org.apache.tomcat.util.http.parser.Cookie" level="ERROR"/>
</configuration>
