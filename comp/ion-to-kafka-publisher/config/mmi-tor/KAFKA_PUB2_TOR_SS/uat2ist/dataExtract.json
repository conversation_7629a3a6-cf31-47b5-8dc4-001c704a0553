[{"comment": ["conversionRules - rules to convert nativeData.tags field values"], "name": "RT_MMI_ADS", "chain": "CAD.AD_IBL.ADS_TOR.AD_IBL", "enable": true, "statusRecord": "CAD.AD_IBL.ADS_TOR.AD_IBL.GSTATUS", "table": "KAFKA_PUB", "keys": ["messageId"], "businessIdField": "Id", "targetClassName": "com.scm.fi.sa.kafka.serializers.plugins.Row", "conversionRules": {"empty": "NaN", "null": "NaN"}, "groovyMappingScript": "row", "targetTopic": "ist2.landing.mmi-tor.instrument.realtime", "targetControlTopic": "ist.landing.mmi-tor.instrument.realtime.control", "targetESIndice": "ist2.landing.mmi-tor.instrument.realtime-1", "ionFields": ["Id", "BalancePriorMaturity1M", "BalancePriorMaturity2M", "BalancePriorMaturity3M", "BalancePriorMaturity4M", "BalancePriorMaturity5M", "PenaltyInterestPaymentRatio", "MortgageLiquidationModel", "MortgageLiquidationModelStr", "MortgageLiquidationParameter", "PartialPrepaymentRate", "InterestAdjustmentDate", "DayCntConvention", "DayCntConventionStr", "AccrualDayCntConvention", "AccrualDayCntConventionStr", "SettleAccrualDayCntConvention", "SettleAccrualDayCntConventionStr", "MoneyMarketDayCntConvention", "MoneyMarketDayCntConventionStr"]}, {"comment": ["ionFields - corresponding fields on chain to evaluate target Kafka object (targetClassName)"], "name": "RT_MMI_INSTRUMENT", "chain": "CAD.CM_INSTRUMENT.REFDATA_TOR.INSTRUMENT", "enable": true, "statusRecord": "CAD.CM_INSTRUMENT.REFDATA_TOR.INSTRUMENT.GSTATUS", "table": "KAFKA_PUB", "keys": ["messageId"], "businessIdField": "Id", "targetClassName": "com.scm.fi.sa.kafka.serializers.plugins.Row", "conversionRules": null, "groovyMappingScript": "row", "targetTopic": "ist2.landing.mmi-tor.instrument.realtime", "targetControlTopic": "ist.landing.mmi-tor.instrument.realtime.control", "targetESIndice": "ist2.landing.mmi-tor.instrument.realtime-1", "ionFields": ["Id", "InstrumentId", "InstrumentMappingId", "SecurityTypeStr", "InsType", "InsTypeStr", "InsSubType", "InsSubTypeStr", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "CusipBBG", "ADPCode", "Desc", "Code", "Country", "CurrencyStr", "DateIssue", "DateMaturity", "DateInterestAccrual", "DateFirstCoupon", "DateLastCoupon", "CouponInterest", "CouponFreq", "CouponFreqStr", "DayCntConvention", "DayCntConventionStr", "SettlDays", "Exchange", "Issuer", "IssuerId", "IssuerShortName", "RatingStandardPoors", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "DateAnnounce", "DateSettl", "DateFirstDelivery", "DateLastDelivery", "DateLastTrade", "DateStop", "DateOptionExpire", "QtyNominal", "ValueTypeStr", "UnderlyingCode", "UnderlyingId", "UnderlyingDesc", "FutureSettlTypeStr", "IndexLinkedFlag", "IndexTicker", "FloaterFlag", "F<PERSON><PERSON><PERSON><PERSON><PERSON>", "Factor", "QtyIssue", "QtyRemaining", "FuturesCTDDesc", "PriceMultiplier", "IndustryGroup", "IndustrySector", "RIC", "CouponCalcType", "CouponCalcTypeStr", "CallableFlag", "OptionTypeStr", "PriceStrike", "StatusStr", "BondCallOptionStyleStr", "CallNotifyDays", "CallNotifyDaysType", "CallNotifyDaysTypeStr", "AccrualDayCntConvention", "AccrualDayCntConventionStr", "CallFeature", "CashFlowCount", "CouponScheduleDate", "S0_StubPeriodType", "CalculationType", "WI", "MBSPoolId", "STRIPTypeStr", "BondCalcType", "EomAdjustFlag", "CollateralType", "PutableFlag", "Ticker", "FixToFloatFlag", "WIRollFlag", "DateStart", "DateFinalMaturity", "RedemptionValue", "TOR_TSXEligibleFlag", "Series", "IssuerIndustry", "UnderlyingSecurityTypeDesc", "FutureProductCode", "BondDefaultFlag", "CountryOfIssue", "CodeTypeStr", "DateDated", "FactorDate", "FloaterIndex", "FloaterResetDate", "AccountingConvention", "PaymentDateAdjTypeStr", "PriceTypeStr", "RatingDBRS", "ResetFreq", "FutureDeliverableBondsCusips", "FutureDeliverableBondsISINs", "WAC", "WAM", "GLClass", "ZeroCouponFlag", "CalculationTypeDescription", "CompoundingFreqStr", "CompoundingFreq", "IsAmortiser", "FloaterResetFreq", "FutureCalcTypeStr", "PerpetualFlag", "FeedToPXE1", "InsertionSource", "InsertionDate", "PriceTick", "QtyTick", "IsCovered", "IsSecured", "IndexRatio", "NextCallDate", "MBSPoolFlag", "ScotiaId", "Called", "MBSBalOnMty"]}]