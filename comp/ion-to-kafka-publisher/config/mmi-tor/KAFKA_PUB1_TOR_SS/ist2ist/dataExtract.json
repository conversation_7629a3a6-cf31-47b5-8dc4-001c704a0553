[{"comment": ["ionFields - corresponding fields on chain to evaluate target Kafka object (targetClassName)"], "name": "RT_MMI_POSITION", "chain": "CAD.CM_POSITION.POSITION_TOR.POSITION", "enable": true, "statusRecord": "CAD.CM_STATUS.POSITION_TOR.GSTATUS", "table": "KAFKA_PUB", "keys": ["messageId"], "businessIdField": "Id", "targetClassName": "com.scotia.gcm.sa.v1.position.PositionMessage", "region": "TOR", "conversionRules": null, "groovyMappingScript": "position/position-tor.groovy", "targetTopic": "ist2.enterprise.mmi-tor.position.realtime.v1", "targetControlTopic": "ist2.enterprise.mmi-tor.position.realtime.control.v1", "targetESIndice": "ist2.enterprise.mmi-tor.position.realtime.v1-1", "ionFields": ["Id", "Date", "BookId", "InstrumentId", "NetTradingPos", "NetTradingPosNominal", "PositionAge", "SettledPos", "SettledPosNominal", "SettledPosNominalPrevious", "SettledPos<PERSON>revious", "SODPos", "SODPosNominal", "StatusStr", "TradeCount", "Trader", "UnsettledPos", "UnsettledPosNominal", "ValueTodayBuyAvg", "ValueTodaySellAvg", "Volume", "VolumeTodayBuy", "VolumeTodaySell"]}, {"comment": ["Fields are missing for TOR: FutureIsin, MiFID2Exemption, RefAssetAcctMethod"], "name": "RT_MMI_TRADE", "chain": "CAD.CM_TRADE.TRADESERVER_TOR.TRADECAPTURE", "enable": true, "statusRecord": "CAD.CM_STATUS.TRADESERVER_TOR.GSTATUS", "table": "KAFKA_PUB", "keys": ["messageId"], "businessIdField": "Id", "targetClassName": "com.scotia.gcm.sa.v1.trade.TradeMessage", "conversionRules": null, "groovyMappingScript": "plug-in-class:com.scm.fi.sa.kafka.serializers.plugins.TradeMessageBuilder", "region": "TOR", "targetTopic": "ist2.enterprise.mmi-tor.trade.realtime.v1", "targetControlTopic": "ist2.enterprise.mmi-tor.trade.realtime.control", "targetESIndice": "ist2.enterprise.mmi-tor.trade.realtime.v1-1", "ionFields": ["Id", "RevId", "TradeId", "OrderId", "QuoteId", "TVTransactionIdCode", "DealId", "InstrumentId", "ExternalId1", "ExternalIdSrc1", "Desc", "SecurityTypeStr", "<PERSON><PERSON>", "Code", "MIC", "Trader", "CreateUserId", "OriginalCreateUserId", "SalesRepId", "Sales<PERSON>erson", "DateSettl", "SettlTradeNetMoney", "CurrencyStr", "WaiverFlag", "ExecutionMktId", "InvestmentMktId", "DateMktCreationUTC", "TimeMktCreationUTC", "TypeStr", "ScotiaInternal", "DateTrade", "DateOriginalCreateStamp", "TimeOriginalCreate", "DateModified", "TimeModified", "DateCreateStamp", "TimeCreate", "Status", "TradeSubStatusStr", "BookId", "LegalEntity", "CP<PERSON>ey", "CPName", "CounterpartyLEI", "CPShortName", "OriginalSource", "ClearingHouse", "CommissionTotal", "CommissionAmount", "DateStatusChange", "TimeStatusChange", "SolicitedFlag", "SynthRef", "TradeStatusStr", "RetailADPCode", "RetailFlag", "SalesCheckedUserId", "ADPBlotterCode", "ADPSTPStatus", "ADPTrailerCode", "IMPACTStatusMessage", "DateStatusChangeStamp", "LegNo", "CancelledFlag", "FreeText", "ExecutionMktIdType", "SettlDays", "DealType", "RefAssetName", "InsType", "InsTypeStr", "InsSubType", "TradeSplitFlagStr", "TradeSplitId", "TradeSplitPct", "TradeSplitStatusStr", "<PERSON><PERSON><PERSON>", "TotalMoney", "FXRate", "FXRateInverted", "IsCrossCurrencySettl", "Price", "DateMaturity", "VerbStr", "S0_CurrencyStr", "QtyNominal", "Qty", "CPAccountId", "AggressedStr", "ExternalStr", "UnderlyingId", "DateOptionExpire", "OptionTypeStr", "PriceStrike", "StrikeTypeStr", "UnderlyingCode", "OptionStyle", "PriceDirty", "Yield", "AccruedAmount", "AccruedGivenStr", "PriceClean", "CouponInterest", "Discount", "IndexFactor", "WIStr", "AccruedValue", "Factor", "ElectronicExecution", "VenLEI"]}]