[{"comment": ["conversionRules - rules to convert nativeData.tags field values"], "name": "RT_MMI_CME_ORDER_TOR", "chain": "USD.CM_ORDER.CME_TOR_SCI.ORDER", "enable": true, "statusRecord": "USD.CM_ORDER.CME_TOR_SCI.GSTATUS", "table": "KAFKA_PUB", "keys": ["messageId"], "businessIdField": "Id", "targetClassName": "com.scotia.gcm.sa.v1.pretrade.OrderMessage", "conversionRules": null, "groovyMappingScript": "order/order-tor.groovy", "targetTopic": "uat2.enterprise.mmi-tor.order.realtime.v1", "targetControlTopic": "uat2.enterprise.mmi-tor.order.realtime.control.v1", "targetESIndice": "uat2.enterprise.mmi-tor.order.realtime.v1-1", "ionFields": ["Id", "OrderNo", "Trader", "Date", "Time", "TrdDateLast", "TrdTimeLast", "ExpireDate", "ExpireTime", "StatusStr", "TradingStatusStr", "InstrumentId", "CurrencyStr", "TypeStr", "TimeInForceStr", "VerbStr", "Value", "ValueTypeStr", "StopPrice", "TrdValueLast", "TrdQtyLast", "QtyFill", "QtyGoal", "QtyStatusStr", "QtyShown", "DateCreation", "TimeCreation", "DateUTC", "TimeUpd"]}, {"comment": [""], "name": "RT_MMI_BTEC_ORDER_TOR", "chain": "USD.CM_ORDER.BTEC_TOR.ORDER", "enable": true, "statusRecord": "USD.CM_ORDER.BTEC_TOR.GSTATUS", "table": "KAFKA_PUB", "keys": ["messageId"], "businessIdField": "Id", "targetClassName": "com.scotia.gcm.sa.v1.pretrade.OrderMessage", "conversionRules": null, "groovyMappingScript": "order/order-tor.groovy", "targetTopic": "uat2.enterprise.mmi-tor.order.realtime.v1", "targetControlTopic": "uat2.enterprise.mmi-tor.order.realtime.control.v1", "targetESIndice": "uat2.enterprise.mmi-tor.order.realtime.v1-1", "ionFields": ["Id", "OrderNo", "Trader", "Date", "Time", "TrdDateLast", "TrdTimeLast", "ExpireDate", "ExpireTime", "StatusStr", "TradingStatusStr", "InstrumentId", "CurrencyStr", "TypeStr", "TimeInForceStr", "VerbStr", "Value", "ValueTypeStr", "StopPrice", "TrdValueLast", "TrdQtyLast", "QtyFill", "QtyGoal", "QtyStatusStr", "QtyShown", "DateCreation", "TimeCreation", "DateUTC", "TimeUpd"]}, {"comment": [""], "name": "RT_MMI_DWAS_ORDER_TOR", "chain": "USD.CM_ORDER.DWAS_OM_BNS.ORDER", "enable": true, "statusRecord": "USD.CM_ORDER.BTEC_TOR.GSTATUS", "table": "KAFKA_PUB", "keys": ["messageId"], "businessIdField": "Id", "targetClassName": "com.scotia.gcm.sa.v1.pretrade.OrderMessage", "conversionRules": null, "groovyMappingScript": "order/order-tor.groovy", "targetTopic": "uat2.enterprise.mmi-tor.order.realtime.v1", "targetControlTopic": "uat2.enterprise.mmi-tor.order.realtime.control.v1", "targetESIndice": "uat2.enterprise.mmi-tor.order.realtime.v1-1", "ionFields": ["Id", "OrderNo", "Trader", "Date", "Time", "TrdDateLast", "TrdTimeLast", "ExpireDate", "ExpireTime", "StatusStr", "TradingStatusStr", "InstrumentId", "CurrencyStr", "TypeStr", "TimeInForceStr", "VerbStr", "Value", "ValueTypeStr", "StopPrice", "TrdValueLast", "TrdQtyLast", "QtyFill", "QtyGoal", "QtyStatusStr", "QtyShown", "DateCreation", "TimeCreation", "DateUTC", "TimeUpd"]}, {"comment": [""], "name": "RT_MMI_CME_ORDER_TOR", "chain": "USD.CM_ORDER.CME_TOR_BNS.ORDER", "enable": true, "statusRecord": "USD.CM_ORDER.BTEC_TOR.GSTATUS", "table": "KAFKA_PUB", "keys": ["messageId"], "businessIdField": "Id", "targetClassName": "com.scotia.gcm.sa.v1.pretrade.OrderMessage", "conversionRules": null, "groovyMappingScript": "order/order-tor.groovy", "targetTopic": "uat2.enterprise.mmi-tor.order.realtime.v1", "targetControlTopic": "uat2.enterprise.mmi-tor.order.realtime.control.v1", "targetESIndice": "uat2.enterprise.mmi-tor.order.realtime.v1-1", "ionFields": ["Id", "OrderNo", "Trader", "Date", "Time", "TrdDateLast", "TrdTimeLast", "ExpireDate", "ExpireTime", "StatusStr", "TradingStatusStr", "InstrumentId", "CurrencyStr", "TypeStr", "TimeInForceStr", "VerbStr", "Value", "ValueTypeStr", "StopPrice", "TrdValueLast", "TrdQtyLast", "QtyFill", "QtyGoal", "QtyStatusStr", "QtyShown", "DateCreation", "TimeCreation", "DateUTC", "TimeUpd"]}, {"comment": [""], "name": "RT_MMI_BTEC_ORDER_TOR2", "chain": "USD.CM_ORDER.BTEC_TOR2.ORDER", "enable": true, "statusRecord": "USD.CM_ORDER.BTEC_TOR2.GSTATUS", "table": "KAFKA_PUB", "keys": ["messageId"], "businessIdField": "Id", "targetClassName": "com.scotia.gcm.sa.v1.pretrade.OrderMessage", "conversionRules": null, "groovyMappingScript": "order/order-tor.groovy", "targetTopic": "uat2.enterprise.mmi-tor.order.realtime.v1", "targetControlTopic": "uat2.enterprise.mmi-tor.order.realtime.control.v1", "targetESIndice": "uat2.enterprise.mmi-tor.order.realtime.v1-1", "ionFields": ["Id", "OrderNo", "Trader", "Date", "Time", "TrdDateLast", "TrdTimeLast", "ExpireDate", "ExpireTime", "StatusStr", "TradingStatusStr", "InstrumentId", "CurrencyStr", "TypeStr", "TimeInForceStr", "VerbStr", "Value", "ValueTypeStr", "StopPrice", "TrdValueLast", "TrdQtyLast", "QtyFill", "QtyGoal", "QtyStatusStr", "QtyShown", "DateCreation", "TimeCreation", "DateUTC", "TimeUpd"]}, {"comment": [""], "name": "RT_MMI_BTEC_ORDER_TOR", "chain": "CAD.CM_ORDER.MX_OM_TOR.ORDER", "enable": true, "statusRecord": "CAD.CM_ORDER.MX_OM_TOR.GSTATUS", "table": "KAFKA_PUB", "keys": ["messageId"], "businessIdField": "Id", "targetClassName": "com.scotia.gcm.sa.v1.pretrade.OrderMessage", "conversionRules": null, "groovyMappingScript": "order/order-tor.groovy", "targetTopic": "uat2.enterprise.mmi-tor.order.realtime.v1", "targetControlTopic": "uat2.enterprise.mmi-tor.order.realtime.control.v1", "targetESIndice": "uat2.enterprise.mmi-tor.order.realtime.v1-1", "ionFields": ["Id", "OrderNo", "Trader", "Date", "Time", "TrdDateLast", "TrdTimeLast", "ExpireDate", "ExpireTime", "StatusStr", "TradingStatusStr", "InstrumentId", "CurrencyStr", "TypeStr", "TimeInForceStr", "VerbStr", "Value", "ValueTypeStr", "StopPrice", "TrdValueLast", "TrdQtyLast", "QtyFill", "QtyGoal", "QtyStatusStr", "QtyShown", "DateCreation", "TimeCreation", "DateUTC", "TimeUpd"]}, {"comment": [""], "name": "RT_MMI_DWAS_OM_SCI_ORDER_TOR", "chain": "USD.CM_ORDER.DWAS_OM_SCI.ORDER", "enable": true, "statusRecord": "CUSD.CM_ORDER.DWAS_OM_SCI.GSTATUS", "table": "KAFKA_PUB", "keys": ["messageId"], "businessIdField": "Id", "targetClassName": "com.scotia.gcm.sa.v1.pretrade.OrderMessage", "conversionRules": null, "groovyMappingScript": "order/order-tor.groovy", "targetTopic": "uat2.enterprise.mmi-tor.order.realtime.v1", "targetControlTopic": "uat2.enterprise.mmi-tor.order.realtime.control.v1", "targetESIndice": "uat2.enterprise.mmi-tor.order.realtime.v1-1", "ionFields": ["Id", "OrderNo", "Trader", "Date", "Time", "TrdDateLast", "TrdTimeLast", "ExpireDate", "ExpireTime", "StatusStr", "TradingStatusStr", "InstrumentId", "CurrencyStr", "TypeStr", "TimeInForceStr", "VerbStr", "Value", "ValueTypeStr", "StopPrice", "TrdValueLast", "TrdQtyLast", "QtyFill", "QtyGoal", "QtyStatusStr", "QtyShown", "DateCreation", "TimeCreation", "DateUTC", "TimeUpd"]}]