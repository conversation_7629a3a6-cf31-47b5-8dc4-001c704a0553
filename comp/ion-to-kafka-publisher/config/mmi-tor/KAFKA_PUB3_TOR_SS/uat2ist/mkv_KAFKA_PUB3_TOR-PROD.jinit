### File automatically generated by SysAdmin ( Settings Export )

mkv.COMPONENT=KAFKA_PUB3_TOR
mkv.CSHOST=sdpsvrba0126.ib.tor.scotiabank.com
mkv.CSPORT=24001
mkv.DEBUG=50
mkv.LISTEN=*
mkv.LOGSDIR=/opt/bns/ion/TOR/log/KAFKA_PUB3_TOR
mkv.PWD=:D5 84 9B 4F C8 A9 BE 56 D9 AB 96 48 09 C5 A6 F2
mkv.USER=apppub
mkv.akka.logger-startup-timeout=60s
mkv.akka.loglevel=INFO
mkv.dbdir=/opt/bns/ion/TOR/db/KAFKA_PUB3_TOR
mkv.entitlement=0
mkv.logsdays=2
mkv.scotia.authorized-components=
mkv.scotia.authorized-groups=TOR_Trader,TOR_ION_WEB_SUPPORT,TOR_SALES
mkv.scotia.guile.load-plugins=kafka-data-source-order-btec,kafka-data-producer-order-btec,kafka-data-source-order-btec2,kafka-data-producer-order-btec2,kafka-data-source-order-cmebns,kafka-data-producer-order-cmebns,kafka-data-source-order-cmesci,kafka-data-producer-order-cmesci,kafka-data-source-order-dwasbns,kafka-data-producer-order-dwasbns,kafka-data-source-order-dwassci,kafka-data-producer-order-dwassci,kafka-data-source-order-mx,kafka-data-producer-order-mx
mkv.scotia.guile.plugin-defaults.GenericKafkaProducer.Kafka-retries=7200
mkv.scotia.guile.plugin-defaults.GenericKafkaProducer.acks=-1
mkv.scotia.guile.plugin-defaults.GenericKafkaProducer.brokers=dp.bns:9030
mkv.scotia.guile.plugin-defaults.GenericKafkaProducer.data-time-zone=America/Toronto
mkv.scotia.guile.plugin-defaults.GenericKafkaProducer.failedToDeliverFolder=/opt/bns/ion/TOR/staging/KAFKA_PUB3_TOR
mkv.scotia.guile.plugin-defaults.GenericKafkaProducer.file_seq_number=1
mkv.scotia.guile.plugin-defaults.GenericKafkaProducer.idField=Id
mkv.scotia.guile.plugin-defaults.GenericKafkaProducer.kafka_buffer_memory=437248000
mkv.scotia.guile.plugin-defaults.GenericKafkaProducer.machine-time-zone=America/Toronto
mkv.scotia.guile.plugin-defaults.GenericKafkaProducer.max_in_flight_requests=1
mkv.scotia.guile.plugin-defaults.GenericKafkaProducer.messagesHashDir=/opt/bns/ion/TOR/db/KAFKA_PUB3_TOR
mkv.scotia.guile.plugin-defaults.GenericKafkaProducer.reconnect_Backoff=1000
mkv.scotia.guile.plugin-defaults.GenericKafkaProducer.request-timeout-ms=30000
mkv.scotia.guile.plugin-defaults.GenericKafkaProducer.retry_Backoff=1000
mkv.scotia.guile.plugin-defaults.GenericKafkaProducer.sa-message-id-prefix=mmi-tor
mkv.scotia.guile.plugin-defaults.GenericKafkaProducer.sa-secondary-source-system=
mkv.scotia.guile.plugin-defaults.GenericKafkaProducer.sa-source-system=mmi-tor
mkv.scotia.guile.plugin-defaults.GenericKafkaProducer.schema-registry=https://dp.bns:1443
mkv.scotia.guile.plugin-defaults.GenericKafkaProducer.security-enabled=true
mkv.scotia.guile.plugin-defaults.GenericKafkaProducer.serializer=IonToKafkaAvroSerializer
mkv.scotia.guile.plugins.kafka-data-producer-order-btec.debug-on=true
mkv.scotia.guile.plugins.kafka-data-producer-order-btec.ensure-unique=false
mkv.scotia.guile.plugins.kafka-data-producer-order-btec.idField=Id
mkv.scotia.guile.plugins.kafka-data-producer-order-btec.topic=prod.landing.mmi-tor.order.realtime
mkv.scotia.guile.plugins.kafka-data-producer-order-btec.type=com.scotiabank.guile.kafka.plugins.GenericKafkaProducer
mkv.scotia.guile.plugins.kafka-data-producer-order-btec2.debug-on=true
mkv.scotia.guile.plugins.kafka-data-producer-order-btec2.ensure-unique=false
mkv.scotia.guile.plugins.kafka-data-producer-order-btec2.idField=Id
mkv.scotia.guile.plugins.kafka-data-producer-order-btec2.topic=prod.landing.mmi-tor.order.realtime
mkv.scotia.guile.plugins.kafka-data-producer-order-btec2.type=com.scotiabank.guile.kafka.plugins.GenericKafkaProducer
mkv.scotia.guile.plugins.kafka-data-producer-order-cmebns.debug-on=true
mkv.scotia.guile.plugins.kafka-data-producer-order-cmebns.ensure-unique=false
mkv.scotia.guile.plugins.kafka-data-producer-order-cmebns.idField=Id
mkv.scotia.guile.plugins.kafka-data-producer-order-cmebns.topic=prod.landing.mmi-tor.order.realtime
mkv.scotia.guile.plugins.kafka-data-producer-order-cmebns.type=com.scotiabank.guile.kafka.plugins.GenericKafkaProducer
mkv.scotia.guile.plugins.kafka-data-producer-order-cmesci.debug-on=true
mkv.scotia.guile.plugins.kafka-data-producer-order-cmesci.ensure-unique=false
mkv.scotia.guile.plugins.kafka-data-producer-order-cmesci.idField=Id
mkv.scotia.guile.plugins.kafka-data-producer-order-cmesci.topic=prod.landing.mmi-tor.order.realtime
mkv.scotia.guile.plugins.kafka-data-producer-order-cmesci.type=com.scotiabank.guile.kafka.plugins.GenericKafkaProducer
mkv.scotia.guile.plugins.kafka-data-producer-order-dwasbns.debug-on=true
mkv.scotia.guile.plugins.kafka-data-producer-order-dwasbns.ensure-unique=false
mkv.scotia.guile.plugins.kafka-data-producer-order-dwasbns.idField=Id
mkv.scotia.guile.plugins.kafka-data-producer-order-dwasbns.topic=prod.landing.mmi-tor.order.realtime
mkv.scotia.guile.plugins.kafka-data-producer-order-dwasbns.type=com.scotiabank.guile.kafka.plugins.GenericKafkaProducer
mkv.scotia.guile.plugins.kafka-data-producer-order-dwassci.debug-on=true
mkv.scotia.guile.plugins.kafka-data-producer-order-dwassci.ensure-unique=false
mkv.scotia.guile.plugins.kafka-data-producer-order-dwassci.idField=Id
mkv.scotia.guile.plugins.kafka-data-producer-order-dwassci.topic=prod.landing.mmi-tor.order.realtime
mkv.scotia.guile.plugins.kafka-data-producer-order-dwassci.type=com.scotiabank.guile.kafka.plugins.GenericKafkaProducer
mkv.scotia.guile.plugins.kafka-data-producer-order-mx.debug-on=true
mkv.scotia.guile.plugins.kafka-data-producer-order-mx.ensure-unique=false
mkv.scotia.guile.plugins.kafka-data-producer-order-mx.idField=Id
mkv.scotia.guile.plugins.kafka-data-producer-order-mx.topic=prod.landing.mmi-tor.order.realtime
mkv.scotia.guile.plugins.kafka-data-producer-order-mx.type=com.scotiabank.guile.kafka.plugins.GenericKafkaProducer
mkv.scotia.guile.plugins.kafka-data-source-order-btec.chain=USD.CM_ORDER.BTEC_TOR.ORDER
mkv.scotia.guile.plugins.kafka-data-source-order-btec.connections.upstream=kafka-data-producer-order-btec
mkv.scotia.guile.plugins.kafka-data-source-order-btec.fields=*
mkv.scotia.guile.plugins.kafka-data-source-order-btec.source=USD.CM_ORDER.BTEC_TOR.ORDER
mkv.scotia.guile.plugins.kafka-data-source-order-btec.type=com.scotiabank.guile.ion.plugins.GenericIonSource
mkv.scotia.guile.plugins.kafka-data-source-order-btec2.chain=USD.CM_ORDER.BTEC_TOR2.ORDER
mkv.scotia.guile.plugins.kafka-data-source-order-btec2.connections.upstream=kafka-data-producer-order-btec2
mkv.scotia.guile.plugins.kafka-data-source-order-btec2.fields=*
mkv.scotia.guile.plugins.kafka-data-source-order-btec2.source=USD.CM_ORDER.BTEC_TOR2.ORDER
mkv.scotia.guile.plugins.kafka-data-source-order-btec2.type=com.scotiabank.guile.ion.plugins.GenericIonSource
mkv.scotia.guile.plugins.kafka-data-source-order-cmebns.chain=USD.CM_ORDER.CME_TOR_BNS.ORDER
mkv.scotia.guile.plugins.kafka-data-source-order-cmebns.connections.upstream=kafka-data-producer-order-cmebns
mkv.scotia.guile.plugins.kafka-data-source-order-cmebns.fields=*
mkv.scotia.guile.plugins.kafka-data-source-order-cmebns.source=USD.CM_ORDER.CME_TOR_BNS.ORDER
mkv.scotia.guile.plugins.kafka-data-source-order-cmebns.type=com.scotiabank.guile.ion.plugins.GenericIonSource
mkv.scotia.guile.plugins.kafka-data-source-order-cmesci.chain=USD.CM_ORDER.CME_TOR_SCI.ORDER
mkv.scotia.guile.plugins.kafka-data-source-order-cmesci.connections.upstream=kafka-data-producer-order-cmesci
mkv.scotia.guile.plugins.kafka-data-source-order-cmesci.fields=*
mkv.scotia.guile.plugins.kafka-data-source-order-cmesci.source=USD.CM_ORDER.CME_TOR_SCI.ORDER
mkv.scotia.guile.plugins.kafka-data-source-order-cmesci.type=com.scotiabank.guile.ion.plugins.GenericIonSource
mkv.scotia.guile.plugins.kafka-data-source-order-dwasbns.chain=USD.CM_ORDER.DWAS_OM_BNS.ORDER
mkv.scotia.guile.plugins.kafka-data-source-order-dwasbns.connections.upstream=kafka-data-producer-order-dwasbns
mkv.scotia.guile.plugins.kafka-data-source-order-dwasbns.fields=*
mkv.scotia.guile.plugins.kafka-data-source-order-dwasbns.source=USD.CM_ORDER.DWAS_OM_BNS.ORDER
mkv.scotia.guile.plugins.kafka-data-source-order-dwasbns.type=com.scotiabank.guile.ion.plugins.GenericIonSource
mkv.scotia.guile.plugins.kafka-data-source-order-dwassci.chain=USD.CM_ORDER.DWAS_OM_SCI.ORDER
mkv.scotia.guile.plugins.kafka-data-source-order-dwassci.connections.upstream=kafka-data-producer-order-dwassci
mkv.scotia.guile.plugins.kafka-data-source-order-dwassci.fields=*
mkv.scotia.guile.plugins.kafka-data-source-order-dwassci.source=USD.CM_ORDER.DWAS_OM_SCI.ORDER
mkv.scotia.guile.plugins.kafka-data-source-order-dwassci.type=com.scotiabank.guile.ion.plugins.GenericIonSource
mkv.scotia.guile.plugins.kafka-data-source-order-mx.chain=CAD.CM_ORDER.MX_OM_TOR.ORDER
mkv.scotia.guile.plugins.kafka-data-source-order-mx.connections.upstream=kafka-data-producer-order-mx
mkv.scotia.guile.plugins.kafka-data-source-order-mx.fields=*
mkv.scotia.guile.plugins.kafka-data-source-order-mx.source=CAD.CM_ORDER.MX_OM_TOR.ORDER
mkv.scotia.guile.plugins.kafka-data-source-order-mx.type=com.scotiabank.guile.ion.plugins.GenericIonSource
mkv.scotia.locale=kafka-cdl-tor
mkv.usemsecs=1
