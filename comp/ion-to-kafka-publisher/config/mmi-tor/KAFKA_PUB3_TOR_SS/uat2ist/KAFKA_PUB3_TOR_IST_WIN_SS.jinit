# Router name (to connect this component using inOut connection) to subscribe to:
# CAD.CM_TRADESPLIT.TRADESERVER_TOR.TRADESPLIT chain: ROUTERM_KAFKA_TOR
mkv.COMPONENT=KAFKA_PUB3_TOR_IST_WIN_SS
mkv.CSHOST=sdusvrwm0128.dev.ib.tor.scotiabank.com
mkv.CSPORT=24001
mkv.DBDIR=db/KAFKA_PUB3_TOR_IST_WIN_SS
mkv.LISTEN=*
mkv.LOGSDIR=log/KAFKA_PUB3_TOR_IST_WIN_SS
mkv.user = apppub
# IST password does not need to be secure (mkv.pwd = apppub123) but we encrypt it only to verify decryption algorithm
mkv.env = DEV
#DEV: mkv.pwd = :C1 02 68 A2 D9 2F 0D AB 82 9F 3D C7 6A B3 35 CE / apppub123
mkv.PWD= :4F 7C 36 27 12 40 86 EF BC A4 4A BD D9 F4 8D CB
mkv.currency=CAD
mkv.source=KAFKA_PUB3_TOR_IST_WIN_SS
mkv.usemsecs=3
mkv.entitlement=0

#mkv.ZoneId = America/New_York
mkv.logLevel = DEBUG
mkv.debuglevel=1
mkv.logsdays=10
mkv.logPrintTime = 1
mkv.logPrintThreadName = 1

# Enables traces and logging on log files and define tracing detail.
# Setting mkv.debug to a value different from 0 enables all tracing level >= DEBUG.
# -1 Maximum detail: adds supplied fields.
# 0 Logging disabled.
# 10 Adds supply events.
# 100 Minimum detail. Enables log for: errors, connectivity events, statistics, platform events, and settings.
# 30 Adds object publishing events.
# 50 Adds transaction and function events.
# 90 Adds subscription events.
# Min: -10000, Max: 10000
mkv.debug = 50
mkv.logName=KAFKA_PUB3_TOR_IST_WIN_SS.log
mkv.logToScreen=true

# period – time in seconds between successive Inner class for handling retry logic executions
mkv.retryInterval=180
mkv.thread.pool.size=1
mkv.timer.timeout = 300
 
mkv.platform.serviceName=PUB
#mkv.db.connection.pwd=:F6 BE 8D 62 58 65 1F BA
#mkv.db.connection.pwd=:5F EF FD D0 AF FA ED 67 94 C8 D1 A1 A9 83 AB 3D
#mkv.db.connection.uid=FIETOAPP
#mkv.db.connection.url=*********************************************
#mkv.db.connection.url=*********************************************

