[{"comment": ["conversionRules - rules to convert nativeData.tags field values"], "name": "RT_MMI_ADS", "chain": "USD.CM_INSTRUMENT.REFDATA_NY.INSTRUMENT", "enable": true, "statusRecord": "USD.CM_INSTRUMENT.REFDATA_NY.GSTATUS", "table": "KAFKA_PUB", "keys": ["messageId"], "businessIdField": "Id", "targetClassName": "com.scm.fi.sa.kafka.serializers.plugins.Row", "conversionRules": null, "groovyMappingScript": "row", "targetTopic": "ist2.landing.mmi-ny.instrument.realtime", "targetControlTopic": "ist.landing.mmi-ny.instrument.realtime.control", "targetESIndice": "ist2.landing.mmi-ny.instrument.realtime-1", "ionFields": ["Id", "InstrumentId", "InstrumentMappingId", "SecurityTypeStr", "InsType", "InsTypeStr", "InsSubType", "InsSubTypeStr", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "CusipBBG", "Desc", "Code", "Country", "CurrencyStr", "DateIssue", "DateMaturity", "DateInterestAccrual", "DateFirstCoupon", "DateLastCoupon", "CouponInterest", "CouponFreq", "CouponFreqStr", "DayCntConvention", "DayCntConventionStr", "SettlDays", "Exchange", "Issuer", "IssuerId", "IssuerShortName", "RatingStandardPoors", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "DateAnnounce", "DateSettl", "DateFirstDelivery", "DateLastDelivery", "DateLastTrade", "DateStop", "DateOptionExpire", "QtyNominal", "ValueTypeStr", "UnderlyingCode", "UnderlyingId", "UnderlyingDesc", "FutureSettlTypeStr", "IndexLinkedFlag", "IndexTicker", "FloaterFlag", "F<PERSON><PERSON><PERSON><PERSON><PERSON>", "Factor", "QtyIssue", "QtyRemaining", "FuturesCTDDesc", "PriceMultiplier", "IndustryGroup", "IndustrySector", "RIC", "CouponCalcType", "CouponCalcTypeStr", "CallableFlag", "OptionTypeStr", "PriceStrike", "StatusStr", "BondCallOptionStyleStr", "CallNotifyDays", "CallNotifyDaysType", "CallNotifyDaysTypeStr", "AccrualDayCntConvention", "AccrualDayCntConventionStr", "CashFlowCount", "CouponScheduleDate", "S0_StubPeriodType", "WI", "MBSPoolId", "StripTypeStr", "BondCalcType", "EomAdjustFlag", "CollateralType", "PutableFlag", "Ticker", "FixToFloatFlag", "WIRollFlag", "DateStart", "RedemptionValue", "Series", "IssuerIndustry", "UnderlyingSecurityTypeStr", "FutureProductCode", "CodeTypeStr", "DateDated", "FactorDate", "FloaterIndex", "FloaterResetDate", "PaymentDateAdjTypeStr", "PriceTypeStr", "WAC", "WAM", "ZeroCouponFlag", "CompoundingFreqStr", "CompoundingFreq", "FloaterResetFreq", "FutureCalcTypeStr", "PerpetualFlag", "InsertionSource", "InsertionDate", "PriceTick", "QtyTick", "MBSPoolFlag"]}]