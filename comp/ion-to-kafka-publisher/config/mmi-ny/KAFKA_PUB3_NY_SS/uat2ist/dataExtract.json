[{"comment": ["conversionRules - rules to convert nativeData.tags field values"], "name": "RT_MMI_CME_ORDER_NY", "chain": "USD.CM_ORDER.CME_NY_SCI.ORDER", "enable": true, "statusRecord": "USD.CM_ORDER.CME_NY_SCI.GSTATUS", "table": "KAFKA_PUB", "keys": ["messageId"], "businessIdField": "Id", "targetClassName": "com.scotia.gcm.sa.v1.pretrade.OrderMessage", "conversionRules": null, "groovyMappingScript": "order/order-ny.groovy", "targetTopic": "ist2.enterprise.mmi-ny.order.realtime.v1", "targetControlTopic": "ist2.enterprise.mmi-ny.position.realtime.control.v1", "targetESIndice": "ist2.enterprise.mmi-ny.order.realtime.v1-1", "ionFields": ["Id", "OrderNo", "Trader", "Date", "Time", "TrdDateLast", "TrdTimeLast", "ExpireDate", "ExpireTime", "StatusStr", "TradingStatusStr", "InstrumentId", "CurrencyStr", "TypeStr", "TimeInForceStr", "VerbStr", "Value", "ValueTypeStr", "StopPrice", "TrdValueLast", "TrdQtyLast", "QtyFill", "QtyGoal", "QtyStatusStr", "QtyShown", "DateCreation", "TimeCreation", "DateUTC", "TimeUpd"]}, {"comment": [""], "name": "RT_MMI_BTEC_ORDER_NY", "chain": "USD.CM_ORDER.BTEC_NY.ORDER", "enable": true, "statusRecord": "USD.CM_ORDER.BTEC_NY.GSTATUS", "table": "KAFKA_PUB", "keys": ["messageId"], "businessIdField": "Id", "targetClassName": "com.scotia.gcm.sa.v1.pretrade.OrderMessage", "conversionRules": null, "groovyMappingScript": "order/order-ny.groovy", "targetTopic": "ist2.enterprise.mmi-ny.order.realtime.v1", "targetControlTopic": "ist2.enterprise.mmi-ny.position.realtime.control.v1", "targetESIndice": "ist2.enterprise.mmi-ny.order.realtime.v1-1", "ionFields": ["Id", "OrderNo", "Trader", "Date", "Time", "TrdDateLast", "TrdTimeLast", "ExpireDate", "ExpireTime", "StatusStr", "TradingStatusStr", "InstrumentId", "CurrencyStr", "TypeStr", "TimeInForceStr", "VerbStr", "Value", "ValueTypeStr", "StopPrice", "TrdValueLast", "TrdQtyLast", "QtyFill", "QtyGoal", "QtyStatusStr", "QtyShown", "DateCreation", "TimeCreation", "DateUTC", "TimeUpd"]}, {"comment": [""], "name": "RT_MMI_DWAS_ORDER_NY", "chain": "USD.CM_ORDER.DWAS_OM_BNS.ORDER", "enable": true, "statusRecord": "USD.CM_ORDER.BTEC_NY.GSTATUS", "table": "KAFKA_PUB", "keys": ["messageId"], "businessIdField": "Id", "targetClassName": "com.scotia.gcm.sa.v1.pretrade.OrderMessage", "conversionRules": null, "groovyMappingScript": "order/order-ny.groovy", "targetTopic": "ist2.enterprise.mmi-ny.order.realtime.v1", "targetControlTopic": "ist2.enterprise.mmi-ny.position.realtime.control.v1", "targetESIndice": "ist2.enterprise.mmi-ny.order.realtime.v1-1", "ionFields": ["Id", "OrderNo", "Trader", "Date", "Time", "TrdDateLast", "TrdTimeLast", "ExpireDate", "ExpireTime", "StatusStr", "TradingStatusStr", "InstrumentId", "CurrencyStr", "TypeStr", "TimeInForceStr", "VerbStr", "Value", "ValueTypeStr", "StopPrice", "TrdValueLast", "TrdQtyLast", "QtyFill", "QtyGoal", "QtyStatusStr", "QtyShown", "DateCreation", "TimeCreation", "DateUTC", "TimeUpd"]}, {"comment": [""], "name": "RT_MMI_CME_ORDER_NY", "chain": "USD.CM_ORDER.CME_NY_BNS.ORDER", "enable": true, "statusRecord": "USD.CM_ORDER.BTEC_NY.GSTATUS", "table": "KAFKA_PUB", "keys": ["messageId"], "businessIdField": "Id", "targetClassName": "com.scotia.gcm.sa.v1.pretrade.OrderMessage", "conversionRules": null, "groovyMappingScript": "order/order-ny.groovy", "targetTopic": "ist2.enterprise.mmi-ny.order.realtime.v1", "targetControlTopic": "ist2.enterprise.mmi-ny.position.realtime.control.v1", "targetESIndice": "ist2.enterprise.mmi-ny.order.realtime.v1-1", "ionFields": ["Id", "OrderNo", "Trader", "Date", "Time", "TrdDateLast", "TrdTimeLast", "ExpireDate", "ExpireTime", "StatusStr", "TradingStatusStr", "InstrumentId", "CurrencyStr", "TypeStr", "TimeInForceStr", "VerbStr", "Value", "ValueTypeStr", "StopPrice", "TrdValueLast", "TrdQtyLast", "QtyFill", "QtyGoal", "QtyStatusStr", "QtyShown", "DateCreation", "TimeCreation", "DateUTC", "TimeUpd"]}, {"comment": [""], "name": "RT_MMI_BTEC_ORDER_NY2", "chain": "USD.CM_ORDER.BTEC_NY2.ORDER", "enable": true, "statusRecord": "USD.CM_ORDER.BTEC_NY2.GSTATUS", "table": "KAFKA_PUB", "keys": ["messageId"], "businessIdField": "Id", "targetClassName": "com.scotia.gcm.sa.v1.pretrade.OrderMessage", "conversionRules": null, "groovyMappingScript": "order/order-ny.groovy", "targetTopic": "ist2.enterprise.mmi-ny.order.realtime.v1", "targetControlTopic": "ist2.enterprise.mmi-ny.position.realtime.control.v1", "targetESIndice": "ist2.enterprise.mmi-ny.order.realtime.v1-1", "ionFields": ["Id", "OrderNo", "Trader", "Date", "Time", "TrdDateLast", "TrdTimeLast", "ExpireDate", "ExpireTime", "StatusStr", "TradingStatusStr", "InstrumentId", "CurrencyStr", "TypeStr", "TimeInForceStr", "VerbStr", "Value", "ValueTypeStr", "StopPrice", "TrdValueLast", "TrdQtyLast", "QtyFill", "QtyGoal", "QtyStatusStr", "QtyShown", "DateCreation", "TimeCreation", "DateUTC", "TimeUpd"]}, {"comment": [""], "name": "RT_MMI_BTEC_ORDER_NY", "chain": "USD.CM_ORDER.MX_OM_NY.ORDER", "enable": true, "statusRecord": "USD.CM_ORDER.MX_OM_NY.GSTATUS", "table": "KAFKA_PUB", "keys": ["messageId"], "businessIdField": "Id", "targetClassName": "com.scotia.gcm.sa.v1.pretrade.OrderMessage", "conversionRules": null, "groovyMappingScript": "order/order-ny.groovy", "targetTopic": "ist2.enterprise.mmi-ny.order.realtime.v1", "targetControlTopic": "ist2.enterprise.mmi-ny.position.realtime.control.v1", "targetESIndice": "ist2.enterprise.mmi-ny.order.realtime.v1-1", "ionFields": ["Id", "OrderNo", "Trader", "Date", "Time", "TrdDateLast", "TrdTimeLast", "ExpireDate", "ExpireTime", "StatusStr", "TradingStatusStr", "InstrumentId", "CurrencyStr", "TypeStr", "TimeInForceStr", "VerbStr", "Value", "ValueTypeStr", "StopPrice", "TrdValueLast", "TrdQtyLast", "QtyFill", "QtyGoal", "QtyStatusStr", "QtyShown", "DateCreation", "TimeCreation", "DateUTC", "TimeUpd"]}, {"comment": [""], "name": "RT_MMI_DWAS_OM_SCI_ORDER_NY", "chain": "USD.CM_ORDER.DWAS_OM_SCI.ORDER", "enable": true, "statusRecord": "CUSD.CM_ORDER.DWAS_OM_SCI.GSTATUS", "table": "KAFKA_PUB", "keys": ["messageId"], "businessIdField": "Id", "targetClassName": "com.scotia.gcm.sa.v1.pretrade.OrderMessage", "conversionRules": null, "groovyMappingScript": "order/order-ny.groovy", "targetTopic": "ist2.enterprise.mmi-ny.order.realtime.v1", "targetControlTopic": "ist2.enterprise.mmi-ny.position.realtime.control.v1", "targetESIndice": "ist2.enterprise.mmi-ny.order.realtime.v1-1", "ionFields": ["Id", "OrderNo", "Trader", "Date", "Time", "TrdDateLast", "TrdTimeLast", "ExpireDate", "ExpireTime", "StatusStr", "TradingStatusStr", "InstrumentId", "CurrencyStr", "TypeStr", "TimeInForceStr", "VerbStr", "Value", "ValueTypeStr", "StopPrice", "TrdValueLast", "TrdQtyLast", "QtyFill", "QtyGoal", "QtyStatusStr", "QtyShown", "DateCreation", "TimeCreation", "DateUTC", "TimeUpd"]}]