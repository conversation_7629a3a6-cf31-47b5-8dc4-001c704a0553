[{"comment": ["#1: conversionRules - rules to convert nativeData.tags field values"], "name": "RT_BTEC_ORDER_NY", "chain": "USD.CM_ORDER.BTEC.ORDER", "enable": true, "statusRecord": "USD.CM_ORDER.BTEC.GSTATUS", "table": "KAFKA_PUB", "keys": ["messageId"], "businessIdField": "Id", "targetClassName": "com.scotia.gcm.sa.v1.pretrade.OrderMessage", "conversionRules": null, "groovyMappingScript": "order/order-ny.groovy", "targetTopic": "uat2.enterprise.mmi-ny.order.realtime.v1", "targetControlTopic": "uat2.enterprise.mmi-ny.order.realtime.control.v1", "targetESIndice": "uat2.enterprise.mmi-ny.order.realtime.v1-1", "ionFields": ["Id", "OrderNo", "Trader", "Date", "Time", "TrdDateLast", "TrdTimeLast", "ExpireDate", "ExpireTime", "StatusStr", "TradingStatusStr", "InstrumentId", "CurrencyStr", "TypeStr", "TimeInForceStr", "VerbStr", "Value", "ValueTypeStr", "StopPrice", "TrdValueLast", "TrdQtyLast", "QtyFill", "QtyGoal", "QtyStatusStr", "QtyShown", "DateCreation", "TimeCreation", "DateUTC", "TimeUpd"]}, {"comment": ["#2"], "name": "CITI_VELOCITY_NY_ORDER_NY", "chain": "USD.CM_ORDER.CITI_VELOCITY_NY.ORDER", "enable": true, "statusRecord": "USD.CM_ORDER.CITI_VELOCITY_NY.GSTATUS", "table": "KAFKA_PUB", "keys": ["messageId"], "businessIdField": "Id", "targetClassName": "com.scotia.gcm.sa.v1.pretrade.OrderMessage", "conversionRules": null, "groovyMappingScript": "order/order-ny.groovy", "targetTopic": "uat2.enterprise.mmi-ny.order.realtime.v1", "targetControlTopic": "uat2.enterprise.mmi-ny.order.realtime.control.v1", "targetESIndice": "uat2.enterprise.mmi-ny.order.realtime.v1-1", "ionFields": ["Id", "OrderNo", "Trader", "Date", "Time", "TrdDateLast", "TrdTimeLast", "ExpireDate", "ExpireTime", "StatusStr", "TradingStatusStr", "InstrumentId", "CurrencyStr", "TypeStr", "TimeInForceStr", "VerbStr", "Value", "ValueTypeStr", "StopPrice", "TrdValueLast", "TrdQtyLast", "QtyFill", "QtyGoal", "QtyStatusStr", "QtyShown", "DateCreation", "TimeCreation", "DateUTC", "TimeUpd"]}, {"comment": ["#3"], "name": "RT_CME_ORDER_NY", "chain": "USD.CM_ORDER.CME.ORDER", "enable": true, "statusRecord": "USD.CM_ORDER.CME.GSTATUS", "table": "KAFKA_PUB", "keys": ["messageId"], "businessIdField": "Id", "targetClassName": "com.scotia.gcm.sa.v1.pretrade.OrderMessage", "conversionRules": null, "groovyMappingScript": "order/order-ny.groovy", "targetTopic": "uat2.enterprise.mmi-ny.order.realtime.v1", "targetControlTopic": "uat2.enterprise.mmi-ny.order.realtime.control.v1", "targetESIndice": "uat2.enterprise.mmi-ny.order.realtime.v1-1", "ionFields": ["Id", "OrderNo", "Trader", "Date", "Time", "TrdDateLast", "TrdTimeLast", "ExpireDate", "ExpireTime", "StatusStr", "TradingStatusStr", "InstrumentId", "CurrencyStr", "TypeStr", "TimeInForceStr", "VerbStr", "Value", "ValueTypeStr", "StopPrice", "TrdValueLast", "TrdQtyLast", "QtyFill", "QtyGoal", "QtyStatusStr", "QtyShown", "DateCreation", "TimeCreation", "DateUTC", "TimeUpd"]}, {"comment": ["#4"], "name": "RT_CME_OPT_ORDER_NY", "chain": "USD.CM_ORDER.CME_OPT.ORDER", "enable": true, "statusRecord": "USD.CM_ORDER.CME_OPT.GSTATUS", "table": "KAFKA_PUB", "keys": ["messageId"], "businessIdField": "Id", "targetClassName": "com.scotia.gcm.sa.v1.pretrade.OrderMessage", "conversionRules": null, "groovyMappingScript": "order/order-ny.groovy", "targetTopic": "uat2.enterprise.mmi-ny.order.realtime.v1", "targetControlTopic": "uat2.enterprise.mmi-ny.order.realtime.control.v1", "targetESIndice": "uat2.enterprise.mmi-ny.order.realtime.v1-1", "ionFields": ["Id", "OrderNo", "Trader", "Date", "Time", "TrdDateLast", "TrdTimeLast", "ExpireDate", "ExpireTime", "StatusStr", "TradingStatusStr", "InstrumentId", "CurrencyStr", "TypeStr", "TimeInForceStr", "VerbStr", "Value", "ValueTypeStr", "StopPrice", "TrdValueLast", "TrdQtyLast", "QtyFill", "QtyGoal", "QtyStatusStr", "QtyShown", "DateCreation", "TimeCreation", "DateUTC", "TimeUpd"]}, {"comment": ["#5"], "name": "RT_DWAS_OM_CSC1_ORDER_NY", "chain": "USD.CM_ORDER.DWAS_OM_CSC1.ORDER", "enable": true, "statusRecord": "USD.CM_ORDER.DWAS_OM_CSC1.GSTATUS", "table": "KAFKA_PUB", "keys": ["messageId"], "businessIdField": "Id", "targetClassName": "com.scotia.gcm.sa.v1.pretrade.OrderMessage", "conversionRules": null, "groovyMappingScript": "order/order-ny.groovy", "targetTopic": "uat2.enterprise.mmi-ny.order.realtime.v1", "targetControlTopic": "uat2.enterprise.mmi-ny.order.realtime.control.v1", "targetESIndice": "uat2.enterprise.mmi-ny.order.realtime.v1-1", "ionFields": ["Id", "OrderNo", "Trader", "Date", "Time", "TrdDateLast", "TrdTimeLast", "ExpireDate", "ExpireTime", "StatusStr", "TradingStatusStr", "InstrumentId", "CurrencyStr", "TypeStr", "TimeInForceStr", "VerbStr", "Value", "ValueTypeStr", "StopPrice", "TrdValueLast", "TrdQtyLast", "QtyFill", "QtyGoal", "QtyStatusStr", "QtyShown", "DateCreation", "TimeCreation", "DateUTC", "TimeUpd"]}, {"comment": ["#6"], "name": "RT_DWAS_OM_CSC2_ORDER_NY", "chain": "USD.CM_ORDER.DWAS_OM_CSC2.ORDER", "enable": true, "statusRecord": "USD.CM_ORDER.DWAS_OM_CSC2.GSTATUS", "table": "KAFKA_PUB", "keys": ["messageId"], "businessIdField": "Id", "targetClassName": "com.scotia.gcm.sa.v1.pretrade.OrderMessage", "conversionRules": null, "groovyMappingScript": "order/order-ny.groovy", "targetTopic": "uat2.enterprise.mmi-ny.order.realtime.v1", "targetControlTopic": "uat2.enterprise.mmi-ny.order.realtime.control.v1", "targetESIndice": "uat2.enterprise.mmi-ny.order.realtime.v1-1", "ionFields": ["Id", "OrderNo", "Trader", "Date", "Time", "TrdDateLast", "TrdTimeLast", "ExpireDate", "ExpireTime", "StatusStr", "TradingStatusStr", "InstrumentId", "CurrencyStr", "TypeStr", "TimeInForceStr", "VerbStr", "Value", "ValueTypeStr", "StopPrice", "TrdValueLast", "TrdQtyLast", "QtyFill", "QtyGoal", "QtyStatusStr", "QtyShown", "DateCreation", "TimeCreation", "DateUTC", "TimeUpd"]}, {"comment": ["#7"], "name": "RT_ESPEED_US_ORDER_NY", "chain": "USD.CM_ORDER.ESPEED_US.ORDER", "enable": true, "statusRecord": "USD.CM_ORDER.ESPEED_US.GSTATUS", "table": "KAFKA_PUB", "keys": ["messageId"], "businessIdField": "Id", "targetClassName": "com.scotia.gcm.sa.v1.pretrade.OrderMessage", "conversionRules": null, "groovyMappingScript": "order/order-ny.groovy", "targetTopic": "uat2.enterprise.mmi-ny.order.realtime.v1", "targetControlTopic": "uat2.enterprise.mmi-ny.order.realtime.control.v1", "targetESIndice": "uat2.enterprise.mmi-ny.order.realtime.v1-1", "ionFields": ["Id", "OrderNo", "Trader", "Date", "Time", "TrdDateLast", "TrdTimeLast", "ExpireDate", "ExpireTime", "StatusStr", "TradingStatusStr", "InstrumentId", "CurrencyStr", "TypeStr", "TimeInForceStr", "VerbStr", "Value", "ValueTypeStr", "StopPrice", "TrdValueLast", "TrdQtyLast", "QtyFill", "QtyGoal", "QtyStatusStr", "QtyShown", "DateCreation", "TimeCreation", "DateUTC", "TimeUpd"]}, {"comment": ["#8"], "name": "RT_FENICS_OM_ORDER_NY", "chain": "USD.CM_ORDER.FENICS_OM.ORDER", "enable": true, "statusRecord": "USD.CM_ORDER.FENICS_OM.GSTATUS", "table": "KAFKA_PUB", "keys": ["messageId"], "businessIdField": "Id", "targetClassName": "com.scotia.gcm.sa.v1.pretrade.OrderMessage", "conversionRules": null, "groovyMappingScript": "order/order-ny.groovy", "targetTopic": "uat2.enterprise.mmi-ny.order.realtime.v1", "targetControlTopic": "uat2.enterprise.mmi-ny.order.realtime.control.v1", "targetESIndice": "uat2.enterprise.mmi-ny.order.realtime.v1-1", "ionFields": ["Id", "OrderNo", "Trader", "Date", "Time", "TrdDateLast", "TrdTimeLast", "ExpireDate", "ExpireTime", "StatusStr", "TradingStatusStr", "InstrumentId", "CurrencyStr", "TypeStr", "TimeInForceStr", "VerbStr", "Value", "ValueTypeStr", "StopPrice", "TrdValueLast", "TrdQtyLast", "QtyFill", "QtyGoal", "QtyStatusStr", "QtyShown", "DateCreation", "TimeCreation", "DateUTC", "TimeUpd"]}, {"comment": ["#9"], "name": "RT_LIQUIDITYEDGE_ORDER_NY", "chain": "USD.CM_ORDER.LIQUIDITYEDGE.ORDER", "enable": true, "statusRecord": "USD.CM_ORDER.LIQUIDITYEDGE.GSTATUS", "table": "KAFKA_PUB", "keys": ["messageId"], "businessIdField": "Id", "targetClassName": "com.scotia.gcm.sa.v1.pretrade.OrderMessage", "conversionRules": null, "groovyMappingScript": "order/order-ny.groovy", "targetTopic": "uat2.enterprise.mmi-ny.order.realtime.v1", "targetControlTopic": "uat2.enterprise.mmi-ny.order.realtime.control.v1", "targetESIndice": "uat2.enterprise.mmi-ny.order.realtime.v1-1", "ionFields": ["Id", "OrderNo", "Trader", "Date", "Time", "TrdDateLast", "TrdTimeLast", "ExpireDate", "ExpireTime", "StatusStr", "TradingStatusStr", "InstrumentId", "CurrencyStr", "TypeStr", "TimeInForceStr", "VerbStr", "Value", "ValueTypeStr", "StopPrice", "TrdValueLast", "TrdQtyLast", "QtyFill", "QtyGoal", "QtyStatusStr", "QtyShown", "DateCreation", "TimeCreation", "DateUTC", "TimeUpd"]}]