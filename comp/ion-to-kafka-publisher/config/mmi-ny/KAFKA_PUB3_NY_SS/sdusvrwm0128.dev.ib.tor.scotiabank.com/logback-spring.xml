<?xml version="1.0" encoding="UTF-8"?>
<!-- 1. One log file per application run: Uses the startTime timestamp in the filename ✓
     2. No new files at midnight: Uses only size-based rolling, not time-based ✓
     3. Size limit of 150MB: Files roll when they reach 150MB ✓
     4. No cron job required: Everything is handled by Logback ✓ -->
<configuration>
    <statusListener class="ch.qos.logback.core.status.NopStatusListener" />
    
    <!-- Define a timestamp at application start -->
    <timestamp key="startTime" datePattern="yyyy-MM-dd_HH-mm-ss"/>

    <!-- Property for log file location -->
    <property name="LOG_DIR" value="/opt/bns/ion/NY/log/KAFKA_PUB/KAFKA_PUB3_NY_SS" />
    <property name="ARCHIVE_DIR" value="${LOG_DIR}" />
    <property name="COMPONENT_NAME" value="KAFKA_PUB3_NY_SS" />

    <!-- Console <PERSON>ppender -->
    <appender name="Console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSSXXX}: %-5level [%thread] %-3.33logger{39} : %msg%n</pattern>
        </encoder>
    </appender>

    <!-- Main Log File Appender -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- Active file name includes timestamp from app start -->
        <file>${LOG_DIR}/${COMPONENT_NAME}.${startTime}.log</file>
        <append>true</append>
        <encoder>
            <pattern>%d{yy-MM-dd HH:mm:ss.SSS} %-5level [%thread] %logger{36} - %msg%n</pattern>
        </encoder>

        <!-- Size-based rolling policy -->
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <!-- Pattern for rolled files - includes session timestamp and index -->
            <fileNamePattern>${LOG_DIR}/${COMPONENT_NAME}.${startTime}.%i.log</fileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>100</maxIndex>
        </rollingPolicy>

        <!-- Trigger rolling when file reaches 150MB -->
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>150MB</maxFileSize>
        </triggeringPolicy>
    </appender>

    <!-- Async wrapper for main file appender to improve performance -->
    <appender name="ASYNC_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="FILE" />
        <queueSize>512</queueSize>
        <discardingThreshold>0</discardingThreshold>
    </appender>

    <!-- Error Log File Appender -->
    <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_DIR}/${COMPONENT_NAME}-Errors.${startTime}.log</file>
        <append>true</append>
        <encoder>
            <pattern>%d{yy-MM-dd HH:mm:ss.SSS} %-5level [%thread] %logger{36} - %msg%n</pattern>
        </encoder>
        
        <!-- Only log ERROR and above -->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>

        <!-- Size-based rolling policy for errors -->
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>${LOG_DIR}/${COMPONENT_NAME}-Errors.${startTime}.%i.log</fileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>100</maxIndex>
        </rollingPolicy>

        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>150MB</maxFileSize>
        </triggeringPolicy>
    </appender>

    <!-- Async wrapper for error file appender -->
    <appender name="ASYNC_ERROR_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="ERROR_FILE" />
        <queueSize>512</queueSize>
        <discardingThreshold>0</discardingThreshold>
    </appender>

    <!-- Root Logger -->
    <root level="INFO">
        <appender-ref ref="Console"/>
        <appender-ref ref="ASYNC_FILE"/>
        <appender-ref ref="ASYNC_ERROR_FILE"/>
    </root>

    <!-- Application-specific loggers -->
    <logger name="com.scm.fi" level="INFO" />
    <logger name="ch.qos.logback" level="WARN"/>
    <logger name="org.springframework.web.servlet.resource.ResourceHttpRequestHandler" level="ERROR" />
    <logger name="org.springframework.web.servlet.PageNotFound" level="ERROR" />
    <logger name="com.iontrading" level="INFO"/>
    <logger name="org.springframework" level="INFO" />
    <logger name="org.apache.coyote" level="INFO" />
    <logger name="org.springframework.web" level="INFO" />
    <logger name="org.apache.kafka.common.utils.AppInfoParser" level="WARN"/>
    <logger name="org.apache.kafka.common.internals.TransactionManager" level="WARN"/>
    <logger name="org.hibernate.internal.util" level="INFO" />
    <logger name="org.apache.kafka.clients.producer" level="WARN"/>
    <logger name="org.apache.kafka.clients.Metadata" level="WARN"/>
    <logger name="org.apache.kafka.clients" level="WARN"/>
    <logger name="org.apache.kafka.common.security.kerberos.KerberosLogin" level="WARN"/>
    <logger name="kafka-kerberos-refresh-thread" level="WARN"/>
    <logger name="org.hibernate.SQL" level="INFO"/>
    <logger name="org.hibernate.type.descriptor.sql.BasicBinder" level="INFO"/>
    <logger name="com.zaxxer.hikari.HikariConfig" level="INFO" />
    <logger name="com.zaxxer.hikari" level="INFO" />
    <logger name="che.coyote.http11.Http11Processor" level="ERROR"/>
    <logger name="org.springframework.web.servlet.mvc.support.DefaultHandlerExceptionResolver" level="ERROR" />
    <logger name="org.springframework.web.servlet.resource.ResourceHandlerUtils" level="ERROR" />
    <logger name="org.apache.coyote.http11.Http11Processor" level="ERROR" />
    <logger name="org.apache.catalina.core.StandardService" level="ERROR" />
    <logger name="org.apache.catalina" level="ERROR" />
    <logger name="org.springframework.web.servlet.mvc.support.DefaultHandlerExceptionResolver" level="ERROR" />
    <logger name="org.springframework.web.servlet.resource.ResourceHandlerUtils" level="ERROR" />
    <logger name="org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping" level="ERROR"/>
    <logger name="org.apache.tomcat.util.http.parser.Cookie" level="ERROR"/>
</configuration>
