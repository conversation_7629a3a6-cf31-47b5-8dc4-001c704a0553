<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- Define a timestamp to use in log filenames that's generated at application start -->
    <timestamp key="startTime" datePattern="yyyy-MM-dd_HH-mm-ss"/>

    <!-- Property for log file location -->
    <property name="LOG_DIR" value="/opt/bns/ion/NY/log/KAFKA_PUB/KAFKA_PUB_TRADES_SPLIT_NY" />
    <property name="ARCHIVE_DIR" value="${LOG_DIR}" />

    <!--logger levels are set in order of verbosity: TRACE < DEBUG < INFO < WARN < ERROR -->
    <appender name="Console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSSXXX}: %-5level [%thread] %-3.33logger{39} : %msg%n</pattern>
        </encoder>
    </appender>

    <!-- Main file appender - one file per app start -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_DIR}/KAFKA_PUB_TRADES_SPLIT_NY.${startTime}.log</file>
        <append>true</append>
        <encoder>
            <pattern>%d{yy-MM-dd HH:mm:ss.SSS} %-5level [%thread] %logger{36} - %msg%n</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>DEBUG</level>
        </filter>
        
        <!-- This rolling policy will trigger based on time and not file size -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- The archived file pattern - this captures the original timestamp -->
            <fileNamePattern>${ARCHIVE_DIR}/KAFKA_PUB_TRADES_SPLIT_NY.${startTime}.%d{yyyy-MM-dd}.zip</fileNamePattern>
            <!-- Keep logs for 30 days -->
            <maxHistory>30</maxHistory>
            <!-- Total size cap -->
            <totalSizeCap>5GB</totalSizeCap>
            <!-- This is the key setting: archive files that are 5 days old -->
            <maxFileSize>100MB</maxFileSize>
            <!-- Clean older archives -->
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
    </appender>

    <!-- Error file appender - one file per app start -->
    <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_DIR}/KAFKA_PUB_TRADES_SPLIT_NY-Errors.${startTime}.log</file>
        <append>true</append>
        <encoder>
            <pattern>%d{yy-MM-dd HH:mm:ss.SSS} %-5level [%thread] %logger{36} - %msg%n</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        
        <!-- This rolling policy will trigger based on time -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- The archived file pattern - this captures the original timestamp -->
            <fileNamePattern>${ARCHIVE_DIR}/KAFKA_PUB_TRADES_SPLIT_NY-Errors.${startTime}.%d{yyyy-MM-dd}.zip</fileNamePattern>
            <!-- Keep logs for 60 days -->
            <maxHistory>60</maxHistory>
            <!-- Total size cap -->
            <totalSizeCap>2GB</totalSizeCap>
            <!-- Archive files that are 5 days old -->
            <maxFileSize>100MB</maxFileSize>
            <!-- Clean older archives -->
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
    </appender>
 
    <!-- Root Logger -->
    <root level="INFO">
        <appender-ref ref="Console"/>
        <appender-ref ref="FILE"/>
        <appender-ref ref="ERROR_FILE"/>
    </root>

    <!-- ProducerConfig.DEBUG_CONFIG, "all" -->
    <logger name="com.scm.fi" level="DEBUG" />
    <logger name="org.springframework.web.servlet.resource.ResourceHttpRequestHandler" level="ERROR" />
    <logger name="org.springframework.web.servlet.PageNotFound" level="ERROR" />
    <logger name="com.iontrading" level="INFO"/>
    <logger name="org.springframework" level="INFO" />
    <logger name="org.apache.coyote" level="INFO" />
    <logger name="org.springframework.web" level="INFO" />
    <logger name="org.apache.kafka.common.utils.AppInfoParser" level="WARN"/>
    <logger name="org.apache.kafka.common.internals.TransactionManager" level="WARN"/>
    <logger name="org.hibernate.internal.util" level="INFO" />
    <logger name="org.apache.kafka.clients.producer" level="WARN"/>
    <logger name="org.apache.kafka.clients.Metadata" level="WARN"/>
    <logger name="org.apache.kafka.clients" level="WARN"/>
    <logger name="org.apache.kafka.common.security.kerberos.KerberosLogin" level="WARN"/>
    <logger name="kafka-kerberos-refresh-thread" level="WARN"/>
    <logger name="org.hibernate.SQL" level="INFO"/>
    <logger name="org.hibernate.type.descriptor.sql.BasicBinder" level="INFO"/>
    <logger name="com.zaxxer.hikari.HikariConfig" level="INFO" />
    <logger name="com.zaxxer.hikari" level="INFO" />
    <logger name="che.coyote.http11.Http11Processor" level="ERROR"/>
    <logger name="org.springframework.web.servlet.mvc.support.DefaultHandlerExceptionResolver" level="ERROR" />
    <logger name="org.springframework.web.servlet.resource.ResourceHandlerUtils" level="ERROR" />
    <logger name="org.apache.coyote.http11.Http11Processor" level="ERROR" />
    <logger name="org.apache.catalina.core.StandardService" level="ERROR" />
    <logger name="org.apache.catalina" level="ERROR" />
    <logger name="org.springframework.web.servlet.mvc.support.DefaultHandlerExceptionResolver" level="ERROR" />
    <logger name="org.springframework.web.servlet.resource.ResourceHandlerUtils" level="ERROR" />
    <logger name="org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping" level="ERROR"/>
    <logger name="org.apache.tomcat.util.http.parser.Cookie" level="ERROR"/>
</configuration>
