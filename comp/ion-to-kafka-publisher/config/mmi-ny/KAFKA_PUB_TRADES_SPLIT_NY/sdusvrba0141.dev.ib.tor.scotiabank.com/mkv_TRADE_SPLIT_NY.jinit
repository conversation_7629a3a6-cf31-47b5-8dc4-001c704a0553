mkv.COMPONENT=KAFKA_PUB_TRADES_SPLIT_NY
mkv.CSHOST=sdusvrba0141.dev.ib.tor.scotiabank.com
mkv.CSPORT=24001
mkv.DBDIR=/opt/bns/ion/NY/db/KAFKA_PUB/KAFKA_PUB_TRADES_SPLIT_NY
mkv.LISTEN=*
mkv.LOGSDIR=/opt/bns/ion/NY/log/KAFKA_PUB/KAFKA_PUB_TRADES_SPLIT_NY
mkv.user = apppub
mkv.env = DEV
mkv.currency=USD
mkv.source=KAFKA_PUB_TRADES_SPLIT_NY
mkv.usemsecs=3
mkv.entitlement=0
#mkv.ZoneId = America/New_York
mkv.logLevel = DEBUG
mkv.debuglevel=1
mkv.logsdays=30
mkv.logPrintTime = 1
mkv.logPrintThreadName = 1

# Enables traces and logging on log files and define tracing detail.
# Setting mkv.debug to a value different from 0 enables all tracing level >= DEBUG.
# -1 Maximum detail: adds supplied fields.
# 0 Logging disabled.
# 10 Adds supply events.
# 100 Minimum detail. Enables log for: errors, connectivity events, statistics, platform events, and settings.
# 30 Adds object publishing events.
# 50 Adds transaction and function events.
# 90 Adds subscription events.
# Min: -10000, Max: 10000
mkv.DEBUG=50
mkv.logName=KAFKA_PUB_TRADES_SPLIT_NY.log
mkv.logToScreen=true

mkv.platform.serviceName=PUB
# period – time in seconds between successive Inner class for handling retry logic executions
mkv.retryInterval=180
mkv.thread.pool.size=1
mkv.timer.timeout = 300
 