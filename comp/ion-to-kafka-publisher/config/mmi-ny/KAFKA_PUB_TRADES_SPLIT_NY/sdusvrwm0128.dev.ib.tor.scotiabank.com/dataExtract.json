[{"comment": ["ionFields - corresponding fields on chain to evaluate target Kafka object (targetClassName)"], "name": "RT_MMI_TRADESPLIT", "chain": "USD.CM_TRADESPLIT.TRADESERVER_NY.TRADESPLIT", "enable": true, "statusRecord": "CAD.CM_TRADESPLIT.TRADESERVER_NY.TRADESPLIT.GSTATUS", "table": "KAFKA_PUB", "keys": ["messageId"], "businessIdField": "Id", "targetClassName": "com.scm.fi.sa.kafka.serializers.plugins.Row", "conversionRules": null, "groovyMappingScript": "row", "targetTopic": "uat.landing.mmi-ny.tradesplit.realtime", "targetControlTopic": "uat.landing.mmi-ny.tradesplit.realtime.control", "targetESIndice": "uat.landing.mmi-ny.tradesplit.realtime-1", "ionFields": ["*"]}]