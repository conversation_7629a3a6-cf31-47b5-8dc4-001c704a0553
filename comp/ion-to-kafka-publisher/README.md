MMI to Kafka universal publisher component

It is java 17/Spring Boot 3.x MMI Component (server) that been taken Realtime data from one or multiple chains and publish them to ONE Kafka topic (per thread). 
It is support redundancy in form of Oracle DB: to accumulate MMI chain(s) records before sending them to Kafka and republish directly from DB in case of any Kafka publication issue(s).

Project description: https://confluence.agile.bns/pages/viewpage.action?pageId=780770282
Project sources: https://bitbucket.agile.bns/projects/GFI/repos/ion-kafka-publisher/browse/comp/ion-to-kafka-publisher

The Main purposes of this new component:
Completely replace Scala + NiFi based components for Realtime and EOD publications.
Completely eliminated Scala and Nifi since we have multiple issues to support them

Application limitations:
1. It supports all (515) SpecificRecordBase based classes ONLY from sa-schema (v1.97) library as target classes to publish on Kafka
2. If class is not in the list (like to publish on landing topic), it has to be manually implemented (See: Row Landing object - corresponding to Ion Chains on Detail Design page)
3. it can publish to original chain only: it can not do merge to existing chain (published from another component the same time)

Related projects are:
    1. csv-to-ion-kafka-publisher   - csv to Ion and csv to Kafka application (this one)
    2. ion-to-kafka-publisher       - MMI to Kafka realtime published component
    3. ion-kafka-common-lib         - common library for 1 & 2
	4. kafka-serializers-plugins    - dependency for ion-kafka-common-lib that contains all plug-ins classes to generate target objects to publish to Kafka. That library has to be separate from main fat-jar
    https://bitbucket.agile.bns/projects/GFI/repos/ion-kafka-publisher/browse

Sample command line:
exec /opt/bns/ion/TOR/bin/JAVA/JDK/x64/openjdk-17.0.4.1.1-1/bin/java -Djavax.security.auth.useSubjectCredsOnly=true -Dspring.kafka.ssl.trust-store-password=kafkaclientssl -Dspring.devtools.restart.enabled=false -server -XX:+UseZGC -XX:+UseStringDeduplication -Xrs -Xms512m -Xmx4096m -Xss4096k -Dloader.path=/opt/bns/ion/TOR/bin/KAFKA_PUB/kafka-serializers-plugins-1.0.06.jar -Djava.security.auth.login.config=/opt/bns/ion/TOR/bin/common/kafka_client_jaas_uat.conf -Djava.security.krb5.conf=/opt/bns/ion/TOR/bin/common/krb5_uat.conf -Dsun.security.krb5.disableReferrals=true -Dspring.kafka.ssl.trust-store-location=file:/opt/bns/ion/TOR/bin/common/kafka.client.truststore.jks -jar /opt/bns/ion/TOR/bin/KAFKA_PUB/ion-to-kafka-publisher-1.0.06.jar --spring.profiles.active=uat-trade_split_tor --spring.config.location=file:/opt/bns/ion/TOR/bin/KAFKA_PUB/KAFKA_PUB_TRADES_SPLIT_TOR/config/ -init /opt/bns/ion/TOR/wrk/KAFKA_PUB/KAFKA_PUB_TRADES_SPLIT_TOR/mkvDS_sdusvrwm0128_TOR_KAFKA_PUB_TRADES_SPLIT_TOR.jinit openjdk-17.0.4.1.1-1 512m 4096m UAT TOR uat-trade_split_tor KAFKA_PUB_TRADES_SPLIT_TOR 2> >(tee /opt/bns/ion/TOR/log/KAFKA_PUB/KAFKA_PUB_TRADES_SPLIT_TOR/errors.log)

VM Parameters:
-DDB_USERNAME=fietoapp -Djavax.security.auth.useSubjectCredsOnly=true -Dspring.kafka.ssl.trust-store-password=kafkaclientssl -Dspring.devtools.restart.enabled=false -server -XX:+ZGenerational -XX:+UseZGC -XX:+UseStringDeduplication -Xrs -Xms128m -Xmx4096m -Xss4096k -Djava.security.auth.login.config=/opt/bns/ion/TOR/bin/common/kafka_client_jaas_ist.conf -Djava.security.krb5.conf=/opt/bns/ion/TOR/bin/common/krb5_ist.conf -Dsun.security.krb5.disableReferrals=true -Dspring.kafka.ssl.trust-store-location=file:/opt/bns/ion/TOR/bin/common/kafka.client.truststore.jks -jar /opt/bns/ion/TOR/bin/KAFKA_PUB/ion-to-kafka-publisher-1.0-SNAPSHOT.jar --spring.profiles.active=ist-trade_split_tor --spring.config.location=file:/opt/bns/ion/TOR/bin/KAFKA_PUB/KAFKA_PUB_TRADES_SPLIT_TOR/config/
Command line parameters:
-init /opt/bns/ion/TOR/wrk/KAFKA_PUB/KAFKA_PUB_TRADES_SPLIT_TOR/mkvDS_SDUSVRBA0141_TOR_KAFKA_PUB_TRADES_SPLIT_TOR.jinit openjdk-21.0.5-11 128m 4096m IST TOR ist-trade_split_tor

1. Project Structure
The project structure for a Spring Boot application using Maven:
config/
      +--kafka
      +--mapping
      +--csv2ion-field-mappings.json
      +--csv2kafka-field-mappings.json
      +--java.properties
      +--mkv_CSV2ION_PUB_TOR-IST.jinit

2. Configuration Files
following configuration files:
application-<area>-<env>.yml: for Spring Boot and Kafka properties.
logback-spring.xml: for logging configurations.
csv2ion-field-mappings.json: field mapping configurations for csv file to ion chains
csv2kafka-field-mappings: field mapping configurations for csv file to Kafka Topics

3. Reading CSV Files
Implement a CsvReaderService that reads CSV files using OpenCSV or a similar library and maps them to Kafka records.

6. Kafka Producer Service
Create a KafkaProducerService that uses KafkaTemplate to send records to Kafka topics.

6. Multithreading
Use @Async or ExecutorService to run separate threads for different topics.

7. Error Handling
Implement appropriate error handling for file reading and Kafka publishing.

8. Testing
Write integration tests using @SpringBootTest and mock your Kafka producer for unit testing.

9. Logging
Configure logback-spring.xml for your logging requirements.

10. Application Entry Point
    IonKafkaPublisherApp.java is the main class for Spring Boot application.

For more detail description please check:
https://confluence.agile.bns/pages/viewpage.action?pageId=780770282

To build Angular application html and JavaScript files use versions (ng version):
Angular CLI: 19.1.6
NodeJS: 18.20.6
Package Manager: npm 10.8.2
1. Install ng (Angular cli):
  npm config set proxy http://proxyprd.scotia-capital.com:8080
  npm config set https-proxy http://proxyprd.scotia-capital.com:8080
  npm config set strict-ssl false
  npm cache clean --force
  npm install --verbose -g @angular/cli
  ng version
2.Build Angular application:
  from folder: cd comp\ion-to-kafka-publisher run:
  ng build --configuration production
