#!/bin/bash
# start-component.sh 
# To run KAFKA_PUB* Ion components on 
# DEV: TOR/NY:  sdusvrba0141.dev.ib.tor.scotiabank.com [172.26.22.101],  LON: sddvmionln01
# UAT: TOR/NY:  sdusvrwm0128.dev.ib.tor.scotiabank.com [172.22.205.118], LON: sduionln002.lon.bns
# PROD: TOR/NY: sdpsvrba0126.ib.tor.scotiabank.com [172.26.74.111], LON: sdpionln006.lon.bns / sdbionln006.scglobal.ad.scotiacapital.com
# start-component.sh parameters:  -init <jinit> <jdk_version> <heap_size> <max_size> <Env> <profile> <ComponentName> (openjdk-********.1-1 1024m 4096m UAT TOR uat-trade_split_tor KAFKA_PUB_TRADES_SPLIT_TOR)
#
# ScotiaBank Server folders:TOR:
#  - Executable directory: /opt/bns/ion/TOR/bin/KAFKA_PUB/KAFKA_PUB_TRADES_SPLIT_TOR
#  - Working directory:    /opt/bns/ion/TOR/wrk/KAFKA_PUB/KAFKA_PUB_TRADES_SPLIT_TOR
#  - Logs directory:       /opt/bns/ion/TOR/log/KAFKA_PUB/KAFKA_PUB_TRADES_SPLIT_TOR
#  - Db directory:         /opt/bns/ion/TOR/db/KAFKA_PUB/KAFKA_PUB_TRADES_SPLIT_TOR
#  - CONFIG directory:     /opt/bns/ion/TOR/bin/KAFKA_PUB/KAFKA_PUB_TRADES_SPLIT_TOR/config
#  - Error file: /opt/bns/ion/TOR/wrk/KAFKA_PUB/KAFKA_PUB_TRADES_SPLIT_TOR/component.stderr
#  - Command line:  /opt/bns/ion/TOR/bin/KAFKA_PUB/start-component.sh -init /opt/bns/ion/TOR/wrk/KAFKA_PUB/KAFKA_PUB_TRADES_SPLIT_TOR/mkvDS_sdusvrwm0128_TOR_KAFKA_PUB_TRADES_SPLIT_TOR.jinit openjdk-********.1-1 1024m 4096m UAT TOR uat-trade_split_tor KAFKA_PUB_TRADES_SPLIT_TOR 
#  - Dev metrics use url: http://sdusvrba0141.dev.ib.tor.scotiabank.com:8090/
#
# ScotiaBank Server folders:NY:
#  - Executable directory: /opt/bns/ion/NY/bin/KAFKA_PUB/KAFKA_PUB_TRADES_SPLIT_NY
#  - Working directory:    /opt/bns/ion/NY/wrk/KAFKA_PUB/KAFKA_PUB_TRADES_SPLIT_NY
#  - Logs directory:       /opt/bns/ion/NY/log/KAFKA_PUB/KAFKA_PUB_TRADES_SPLIT_NY
#  - Db directory:         /opt/bns/ion/NY/db/KAFKA_PUB/KAFKA_PUB_TRADES_SPLIT_NY
#  - Error file: /opt/bns/ion/TOR/wrk/KAFKA_PUB_TRADES_SPLIT_NY/component.stderr
#  - Dev metrics use url: http://sdusvrba0141.dev.ib.tor.scotiabank.com:8091/
#
# ScotiaBank Server folders:LON:
#  - Executable directory: /cm/gfi/apps/mkv/custom/KAFKA_PUB_SS/KAFKA_PUB_TRADES_SPLIT_LON
#  - Working directory:    /cm/gfi/apps/mkv/custom/KAFKA_PUB_SS/KAFKA_PUB_TRADES_SPLIT_LON
#  - Logs directory:       /cm/gfi/logs/mkv/custom/KAFKA_PUB_SS/KAFKA_PUB_TRADES_SPLIT_LON
#  - Db directory:         /cm/gfi/apps/mkv/custom/KAFKA_PUB_SS/KAFKA_PUB_TRADES_SPLIT_LON
#  - Binary folder:        /cm/gfi/apps/mkv/binaries/KAFKA_PUB_SS
#  - Config folder:        /cm/gfi/apps/mkv/binaries/KAFKA_PUB_SS/KAFKA_PUB_TRADES_SPLIT_LON/config
#  - Error file:           /cm/gfi/apps/mkv/custom/KAFKA_PUB_SS/KAFKA_PUB_TRADES_SPLIT_LON/component.stderr
#  - Dev metrics use url: http://sddvmionln01.lon.bns:8091/


# JVM Parameters Guide for 8 Independent Instances (TOR+NY)- OpenJDK 17
# Environment & Resource Constraints
# JVM Version: OpenJDK ********+1-LTS
# Total Available RAM: 77GB, Total CPU Cores: 56
# High-load instances: 2 (up to 40M messages/day each), Low-load instances: 6 (about 50K records/day each)
# COMMON PARAMETERS (All Instances)
# -server                            : Enables server JVM optimizations for long-running applications, Better JIT compilation, more aggressive optimizations
# --add-modules=jdk.incubator.vector : JDK 17: Enables Vector API for SIMD operations, Can significantly improve array/collection processing performance
# -XX:+UseNUMA                       : Optimizes memory allocation for NUMA architecture (4 nodes on your server), Reduces memory access latency by keeping memory local to CPU
# -XX:+AlwaysPreTouch                : Pre-touches all heap pages at JVM startup, Eliminates runtime page faults, provides predictable performance, Worth the startup time cost for production workloads
# -XX:+DisableExplicitGC             : Prevents application code from triggering System.gc(), Protects against poorly written libraries disrupting GC timing
# -XX:+UseStringDeduplication        : Deduplicates identical strings in heap (G1GC and ZGC feature), JDK 17: Improved performance, works efficiently with both GCs, Typical savings: 10-15% of heap
# -XX:+ParallelRefProcEnabled        : Processes references (weak, soft, phantom) in parallel, Reduces GC pause times when dealing with many references
# -XX:+ExitOnOutOfMemoryError 		 : JVM exits on OutOfMemoryError instead of limping along, Allows external monitoring to restart the instance cleanly
# -XX:+HeapDumpOnOutOfMemoryError    : Creates heap dump on OOM for post-mortem analysis, Essential for debugging production memory issues
# -XX:+UseCompressedOops			 : Compresses 64-bit object pointers to 32-bit when heap < 32GB, JDK 17: Still valuable, automatic for heaps under 32GB
# -XX:+UseCompressedClassPointers	 : Compresses class metadata pointers, Further memory savings, especially with many loaded classes
# -XX:+UnlockDiagnosticVMOptions	 : -XX:+DebugNonSafepoints, JDK 17: Improves profiling accuracy, Allows async-profiler to capture more accurate stack traces
# -Djava.awt.headless=true			 : Runs in headless mode (no GUI support), Prevents unnecessary AWT/Swing initialization
# -Dfile.encoding=UTF-8				 : Sets default character encoding, Ensures consistent string handling across environments
# -Djava.security.egd=file:/dev/./urandom : Uses non-blocking random number generator, Prevents startup delays from entropy starvation
# Logging configuration
# -XX:HeapDumpPath=./heapdump_${INSTANCE_ID}.hprof : Instance-specific heap dump file, Prevents overwrites when multiple instances OOM
# -XX:ErrorFile=./hs_err_pid%p_${INSTANCE_ID}.log  : JVM crash log with PID and instance ID, Essential for debugging JVM crashes

# High Load:
# -XX:+UseTransparentHugePages       : JDK 17: Better support for Linux transparent huge pages, Improves memory access performance for large heaps (5-10%). Requires THP enabled in OS (cat /sys/kernel/mm/transparent_hugepage/enabled should return: [always] madvise never)

SCRIPT_DIR=$(dirname "$(realpath "$0")")
# trap (ignore) the SIGPIPE signal to prevent the script from crashing
trap '' PIPE

check_parameter_value() {
    local PARAMETER=$1
    local DESCRIPTION=$2
    local VALID_VALUES=$3
    #echo "PARAMETER=$PARAMETER, DESCRIPTION=$DESCRIPTION, VALID_VALUES=$VALID_VALUES."
    if [ -z "$PARAMETER" ]; then
        echo "$(date): ${DESCRIPTION} parameter is missing in command line." | tee -a ${SCRIPT_DIR}/parameter_errors.log
        if [ -n "${VALID_VALUES}" ]; then
            echo "Valid values are: '${VALID_VALUES}'." | tee -a ${SCRIPT_DIR}/parameter_errors.log
        fi
        echo "Expected command line is:" | tee -a ${SCRIPT_DIR}/parameter_errors.log
        echo "$0 -init config.jinit <Java Folder Name> <Heap Size> <Max Memory Size> <Environment> <Region> <Spring profile> <Component Name>" | tee -a ${SCRIPT_DIR}/parameter_errors.log
        exit 1
    fi
    if [ -n "$VALID_VALUES" ]; then
        IFS='|' read -r -a valid_array <<< "$VALID_VALUES"
        if [[ ! " ${valid_array[@]} " =~ " ${PARAMETER} " ]]; then
            echo "$(date): ${DESCRIPTION} parameter value '$PARAMETER' is invalid." | tee -a ${SCRIPT_DIR}/parameter_errors.log
            echo "Valid values are: '${VALID_VALUES}'." | tee -a ${SCRIPT_DIR}/parameter_errors.log
            exit 1
        fi
    fi
}

check_temp_folder() {
	# Get the free space in /tmp in Kbytes
	FREE_SPACE=$(df -k /tmp | awk 'NR==2 {print $4}')
	# Convert 1GB to Kbytes
	ONE_GB=$((1024 * 1024))
	# Check if free space is less than 1GB
	if [ "$FREE_SPACE" -lt "$ONE_GB" ]; then
	  echo "Free space in /tmp is less than 1GB. Cleaning up..." >> ${START_LOG}
	  rm -rf /tmp/* 2>/dev/null
	  FREE_SPACE=$(df -k /tmp | awk 'NR==2 {print $4}')
	  echo "Free space in /tmp after cleaning: $FREE_SPACE Kbytes." >> ${START_LOG}
	else
	  echo "Free space ($FREE_SPACE Kbytes) in /tmp is sufficient." >> ${START_LOG}
	fi
}

check_yaml_file() {
	YAML_FILE=$1
	CONFIG_FOLDER=$2
	YAML_FOLDER=$3
	if [ "$YAML_FOLDER" = "" ]; then
		FINAL_YAML_FOLDER=${CONFIG_FOLDER}/	
	elif [ ! "${YAML_FOLDER: -1}" == "/" ]; then   
		FINAL_YAML_FOLDER=${YAML_FOLDER}/ 
	else
		FINAL_YAML_FOLDER=${YAML_FOLDER}
	fi
	
	FULL_YAML_FILE=${FINAL_YAML_FOLDER}application-${YAML_FILE}.yml
	if [ -f "${FULL_YAML_FILE}" ]; then
		echo "YAML file is exists as: ${FULL_YAML_FILE}" >> ${START_LOG}
	else
		echo "ERROR: YAML file DOES NOT exists as: ${FULL_YAML_FILE}. YAML_FILE=${YAML_FILE}, CONFIG_FOLDER=${CONFIG_FOLDER}, YAML_FOLDER=${YAML_FOLDER}." | tee -a ${SCRIPT_DIR}/parameter_errors.log
		echo "ERROR: YAML file DOES NOT exists as: ${FULL_YAML_FILE}. YAML_FILE=${YAML_FILE}, CONFIG_FOLDER=${CONFIG_FOLDER}, YAML_FOLDER=${YAML_FOLDER}." >> ${START_LOG}
		exit 1
	fi
	echo ${FINAL_YAML_FOLDER}
}

# Mandatory parameters
JAVA_VERSION=${3:-"openjdk-********.1-1"}
JVM_HEAPSIZE=${4:-"1024m"}
JVM_MAXSIZE=${5:-"4096"}
ENV_NAME=${6}
REGION=${7}
SPRING_BOOT_PROFILE=${8}
COMP_NAME=${9}
# Optional parameters
COMPONENT_LOAD=${10:-"LOW_LOAD"} # HIGH_LOAD or LOW_LOAD
SPRING_BOOT_YAML_FOLDER=${11}

check_parameter_value "${ENV_NAME}" "Environment" "IST|UAT|PROD"
check_parameter_value "${REGION}" "Region" "TOR|NY|LON"
check_parameter_value "${SPRING_BOOT_PROFILE}" "Spring Boot profile name"
check_parameter_value "${COMP_NAME}" "Component name"
check_parameter_value "${COMPONENT_LOAD}" "Component load type" "HIGH_LOAD|LOW_LOAD"

if [ "$REGION" = "LON" ]; then
	export APP_HOME=/cm/gfi
	# /cm/gfi/logs/mkv/custom/KAFKA_PUB_SS/KAFKA_PUB_TRADES_SPLIT_LON
	export LOG_HOME=${APP_HOME}/logs/mkv/custom/KAFKA_PUB_SS/${COMP_NAME}
	# /cm/gfi/apps/mkv/binaries/KAFKA_PUB_SS
	export BINPATH=${APP_HOME}/apps/mkv/binaries/KAFKA_PUB_SS/${COMP_NAME}
	# /cm/gfi/apps/mkv/binaries/KAFKA_PUB_SS/KAFKA_PUB_TRADES_SPLIT_LON/config
	export CONFIG=${BINPATH}/config
	export CONFIG_KAFKA=${CONFIG}
	# /cm/gfi/apps/mkv/binaries/JAVA/JDK/x64/java-17-openjdk-********.1-1.portable.jdk.el.x86_64
	export JDK_HOME=${APP_HOME}/apps/mkv/binaries/JAVA/JDK/x64/${JAVA_VERSION}
else
	export APP_HOME=/opt/bns/ion/${REGION}
	export LOG_HOME=${APP_HOME}/log/KAFKA_PUB/${COMP_NAME}
	export BINPATH=${APP_HOME}/bin/KAFKA_PUB/${COMP_NAME}
	export CONFIG=${BINPATH}/config
	export CONFIG_KAFKA=${APP_HOME}/bin/common
	# /opt/bns/ion/TOR/bin/JAVA/JDK/x64/openjdk-********.1-1
	export JDK_HOME=${APP_HOME}/bin/JAVA/JDK/x64/${JAVA_VERSION}
fi

# Only add the latest version jar
LATEST_JAR_FILE=$(ls -rt1 ${BINPATH}/ion-to-kafka-publisher*.jar | tail -1)
PLUG_IN_JAR_FILE=$(ls -rt1 ${BINPATH}/kafka-serializers-plugins*.jar | tail -1)

# -XX:+UseZGC: Enables the Z Garbage Collector. Designed for: Low latency (pauses typically < 1ms), Large heaps (multi-terabyte), Concurrent processing
# -XX:+ZGenerational (New in Java 21). Enables generational ZGC. Splits heap into: Young generation (newly created objects), Old generation (long-lived objects)
# Benefits: Better performance for short-lived objects. Reduced garbage collection overhead. Better memory utilization 
# -XX:+ZGenerational - option will be added for openJdk 21

# Common JVM parameters for all instances - OpenJDK 17 optimized
COMMON_JVM_OPTS="-XX:+UseNUMA -XX:+AlwaysPreTouch -XX:+DisableExplicitGC -XX:+UseStringDeduplication -XX:+ParallelRefProcEnabled -XX:+UseCompressedOops -XX:+UseCompressedClassPointers -XX:+UnlockDiagnosticVMOptions -XX:+DebugNonSafepoints"

# Automatically generates heap dumps if application crashes with an OutOfMemoryError
# Forces the JVM to exit on OOM errors, allowing process supervisors to restart it.
HEAP_DUMP="-XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=${LOG_HOME}/heapdump-%t.hprof -XX:+ExitOnOutOfMemoryError"

# Base VM options
BASE_VM_OPTIONS="-Djavax.security.auth.useSubjectCredsOnly=true -Dspring.devtools.restart.enabled=false -server -Xrs -Xss2m -Dloader.path=${PLUG_IN_JAR_FILE} -Djavax.net.ssl.trustStore=${CONFIG_KAFKA}/cacerts -Djavax.net.ssl.trustStorePassword=changeit ${HEAP_DUMP}"

# Configure based on component load
if [ "$COMPONENT_LOAD" = "HIGH_LOAD" ]; then
    # High-load: ZGC with optimized settings for 40M messages/day
    # Calculate SoftMaxHeapSize as 90% of max heap (ZGC specific)
    if [[ ${JVM_MAXSIZE} =~ ^([0-9]+)([gGmM])$ ]]; then
        HEAP_NUM=${BASH_REMATCH[1]}
        HEAP_UNIT=${BASH_REMATCH[2]}
        SOFT_HEAP=$(awk "BEGIN {printf \"%.0f\", $HEAP_NUM * 0.9}")
        SOFT_HEAP_SIZE="${SOFT_HEAP}${HEAP_UNIT}"
    else
        # Fallback if pattern doesn't match
        SOFT_HEAP_SIZE="${JVM_MAXSIZE}"
    fi
    GC_OPTS="-XX:+UseZGC -XX:+UseTransparentHugePages -XX:ParallelGCThreads=10 -XX:ConcGCThreads=5 -XX:ZAllocationSpikeTolerance=5 -XX:+ZProactive -XX:SoftMaxHeapSize=${SOFT_HEAP_SIZE} -XX:ZCollectionInterval=30 -XX:ZFragmentationLimit=10"
    META_OPTS="-XX:MaxMetaspaceSize=1g -XX:MetaspaceSize=256m -XX:MaxDirectMemorySize=2g"
    # Enhanced GC logging for high-load
    GC_LOG="-Xlog:gc*,gc+heap=info,gc+ergo=info:${LOG_HOME}/gc-%t.log:time,level,tags:filecount=10,filesize=100M"
else
    # Low-load: G1GC optimized for 50K records/day
    GC_OPTS="-XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:G1PeriodicGCInterval=300000 -XX:+G1PeriodicGCInvokesConcurrent -XX:ParallelGCThreads=2 -XX:ConcGCThreads=1 -XX:G1HeapRegionSize=1m -XX:InitiatingHeapOccupancyPercent=45 -XX:G1ReservePercent=10 -XX:StringDeduplicationAgeThreshold=3"
    META_OPTS="-XX:MaxMetaspaceSize=256m -XX:MetaspaceSize=64m -XX:MaxDirectMemorySize=256m"
    # Reduced GC logging for low-load
    GC_LOG="-Xlog:gc:${LOG_HOME}/gc-%t.log:time,uptime:filecount=5,filesize=20M"
fi

# JVM Flight Recorder configuration
if [ "$ENV_NAME" = "PROD" ] && [ "$COMPONENT_LOAD" = "HIGH_LOAD" ]; then
    # Enhanced JFR for production high-load
    JFR="-XX:StartFlightRecording=name=DailyRecording,settings=profile,delay=30s,disk=true,dumponexit=true,filename=${LOG_HOME}/recording.jfr,maxsize=2G,maxage=7d"
else
    # Standard JFR for others
    JFR="-XX:StartFlightRecording=name=DailyRecording,settings=profile,delay=30s,disk=true,dumponexit=true,filename=${LOG_HOME}/recording.jfr,maxsize=1G,maxage=2d"
fi

# Combine all options
VM_OPTIONS="${BASE_VM_OPTIONS} ${COMMON_JVM_OPTS} ${GC_OPTS} ${META_OPTS} -Xms${JVM_HEAPSIZE} -Xmx${JVM_MAXSIZE}"

if [ "$ENV_NAME" = "IST" ]; then
	VM_OPTIONS="${VM_OPTIONS} ${GC_LOG} ${JFR} -Djava.security.auth.login.config=${CONFIG_KAFKA}/kafka_client_jaas_ist.conf -Djava.security.krb5.conf=${CONFIG_KAFKA}/krb5_ist.conf -Dsun.security.krb5.disableReferrals=true"
elif [ "$ENV_NAME" = "UAT" ]; then
	VM_OPTIONS="${VM_OPTIONS} ${GC_LOG} ${JFR} -Djava.security.auth.login.config=${CONFIG_KAFKA}/kafka_client_jaas_uat.conf -Djava.security.krb5.conf=${CONFIG_KAFKA}/krb5_uat.conf -Dsun.security.krb5.disableReferrals=true"	
elif [ "$ENV_NAME" = "PROD" ]; then
	VM_OPTIONS="${VM_OPTIONS} ${GC_LOG} ${JFR} -Djava.security.auth.login.config=${CONFIG_KAFKA}/kafka_client_jaas_prod.conf -Djava.security.krb5.conf=${CONFIG_KAFKA}/krb5_prod.conf -Dsun.security.krb5.disableReferrals=true"
else
    echo "ERROR: ENV_NAME ($ENV_NAME) must be either IST, UAT, or PROD." | tee -a ${SCRIPT_DIR}/parameter_errors.log
    exit 1
fi

if [ -f "${LOG_HOME}/errors.log" ]; then
    mv ${LOG_HOME}/errors.log ${LOG_HOME}/errors.previous.log
fi
if [ -f "${LOG_HOME}/recording.jfr" ]; then
    mv ${LOG_HOME}/recording.jfr ${LOG_HOME}/recording.previous.jfr
fi

export START_LOG=${LOG_HOME}/${COMP_NAME}_starts.txt
# Create LOG directory if it doesn't exist
if [ ! -d ${LOG_HOME} ]; then
	mkdir -p ${LOG_HOME}
	chmod -R 755 ${LOG_HOME}
	echo "LOG_HOME is mission on Host: $(hostname) => therefore it is created as: ${LOG_HOME}." >> ${START_LOG}
fi

echo "**************************************" $(date "+%Y-%m-%d %T") "*********************************************************************" >> ${START_LOG}
check_temp_folder
echo "User: $(id -a), Host: $(hostname)" >> ${START_LOG}
echo "Script: ${SCRIPT_DIR}/$0 $@" >> ${START_LOG}
echo "SPRING_BOOT_PROFILE=${SPRING_BOOT_PROFILE}, CONFIG=${CONFIG}, SPRING_BOOT_YAML_FOLDER=${SPRING_BOOT_YAML_FOLDER}."  >> ${START_LOG}
echo "Component Type: ${COMPONENT_LOAD} (HIGH_LOAD = up to 40M msgs/day, LOW_LOAD = about 50K records/day)" >> ${START_LOG}
echo "GC Configuration: $(echo ${GC_OPTS} | grep -oE 'Use[A-Z]+GC' | sed 's/+Use//')" >> ${START_LOG}
echo "Heap Configuration: -Xms${JVM_HEAPSIZE} -Xmx${JVM_MAXSIZE}, SOFT_HEAP_SIZE=$SOFT_HEAP_SIZE" >> ${START_LOG}

YAML_FOLDER=$(check_yaml_file "${SPRING_BOOT_PROFILE}" "${CONFIG}" "${SPRING_BOOT_YAML_FOLDER}")
PARAMS="--spring.profiles.active=${SPRING_BOOT_PROFILE} --spring.config.location=file:${YAML_FOLDER}"

echo "Free ports from: 8080 to 8100: $(for port in $(seq 8080 8100); do (echo >/dev/tcp/127.0.0.1/$port) &>/dev/null || echo -n "$port "; done; echo)" >> ${START_LOG}
echo COMP_NAME=${COMP_NAME}, LATEST_JAR_FILE=${LATEST_JAR_FILE}. >> ${START_LOG}
echo "exec ${JDK_HOME}/bin/java ${VM_OPTIONS} -jar ${LATEST_JAR_FILE} ${PARAMS} $@ 2>> ${LOG_HOME}/errors.log" >> ${START_LOG}
exec ${JDK_HOME}/bin/java ${VM_OPTIONS} -jar ${LATEST_JAR_FILE} ${PARAMS} $@ 2>> ${LOG_HOME}/errors.log

