package com.scm.fi.db.async.processor;

import com.scm.fi.db.async.config.AsyncDatabaseConfig;
import com.scm.fi.db.async.model.DatabaseOperation;
import com.scm.fi.db.async.model.OperationResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.stereotype.Component;
import org.springframework.dao.DataAccessException;

import javax.sql.DataSource;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;

/**
 * High-performance JDBC batch processor for database operations
 * Optimized for Oracle database with proper error handling
 */
@Slf4j
@Component
public class JdbcBatchProcessor {
    
    private final JdbcTemplate jdbcTemplate;
    private final AsyncDatabaseConfig config;
    private final AtomicLong batchCounter = new AtomicLong(0);
    
    // SQL templates for different operations
    private static final String INSERT_KAFKA_PUB_SQL = 
        "INSERT INTO KAFKA_PUB (MESSAGEID, CHAIN, TOPIC, TARGETCLASS, TS, BUSINESSID, " +
        "CHAINFIELDS, TARGETOBJECT, IONCOMPONENT, IS_PUBLISHED) " +
        "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    private static final String UPDATE_KAFKA_PUB_SQL = 
        "UPDATE KAFKA_PUB SET IS_PUBLISHED = ? WHERE MESSAGEID = ?";
    
    private static final String CHECK_EXISTS_SQL = 
        "SELECT COUNT(*) FROM KAFKA_PUB WHERE MESSAGEID = ?";
    
    public JdbcBatchProcessor(DataSource dataSource, AsyncDatabaseConfig config) {
        this.jdbcTemplate = new JdbcTemplate(dataSource);
        this.config = config;
        
        // Optimize JDBC template for batch operations
        this.jdbcTemplate.setFetchSize(1000);
        this.jdbcTemplate.setMaxRows(0);
        
        log.info("JdbcBatchProcessor initialized with batch processing optimizations");
    }
    
    /**
     * Process a batch of database operations
     */
    public List<OperationResult> processBatch(List<DatabaseOperation> operations) {
        if (operations == null || operations.isEmpty()) {
            return Collections.emptyList();
        }
        
        String batchId = "BATCH-" + batchCounter.incrementAndGet();
        long startTime = System.currentTimeMillis();
        
        log.debug("Processing batch {} with {} operations", batchId, operations.size());
        
        // Group operations by type for efficient batch processing
        Map<DatabaseOperation.OperationType, List<DatabaseOperation>> groupedOps = 
            groupOperationsByType(operations);
        
        List<OperationResult> results = new ArrayList<>();
        
        // Process INSERTs first to maintain ordering
        if (groupedOps.containsKey(DatabaseOperation.OperationType.INSERT)) {
            results.addAll(processInsertBatch(groupedOps.get(DatabaseOperation.OperationType.INSERT), batchId));
        }
        
        // Then process UPDATEs
        if (groupedOps.containsKey(DatabaseOperation.OperationType.UPDATE)) {
            results.addAll(processUpdateBatch(groupedOps.get(DatabaseOperation.OperationType.UPDATE), batchId));
        }
        
        long totalTime = System.currentTimeMillis() - startTime;
        double throughput = operations.size() / (totalTime / 1000.0);
        
        log.info("Batch {} completed: {} operations in {}ms ({:.1f} ops/sec)", 
                batchId, operations.size(), totalTime, throughput);
        
        return results;
    }
    
    /**
     * Process INSERT operations in batch
     */
    private List<OperationResult> processInsertBatch(List<DatabaseOperation> insertOps, String batchId) {
        List<OperationResult> results = new ArrayList<>();
        
        if (insertOps.isEmpty()) {
            return results;
        }
        
        try {
            long startTime = System.currentTimeMillis();
            
            // Execute batch insert
            int[] updateCounts = jdbcTemplate.batchUpdate(INSERT_KAFKA_PUB_SQL, 
                new BatchPreparedStatementSetter() {
                    @Override
                    public void setValues(PreparedStatement ps, int i) throws SQLException {
                        DatabaseOperation op = insertOps.get(i);
                        Map<String, Object> data = op.getData();
                        
                        ps.setString(1, (String) data.get("messageId"));
                        ps.setString(2, (String) data.get("chain"));
                        ps.setString(3, (String) data.get("topic"));
                        ps.setString(4, (String) data.get("targetClass"));
                        ps.setTimestamp(5, Timestamp.valueOf((LocalDateTime) data.get("ts")));
                        ps.setString(6, (String) data.get("businessId"));
                        ps.setString(7, (String) data.get("chainFields"));
                        ps.setString(8, (String) data.get("targetObject"));
                        ps.setString(9, (String) data.get("ionComponent"));
                        ps.setInt(10, (Integer) data.getOrDefault("isPublished", 0));
                    }
                    
                    @Override
                    public int getBatchSize() {
                        return insertOps.size();
                    }
                });
            
            long executionTime = System.currentTimeMillis() - startTime;
            
            // Create results for each operation
            for (int i = 0; i < insertOps.size(); i++) {
                DatabaseOperation op = insertOps.get(i);
                int affectedRows = (i < updateCounts.length) ? updateCounts[i] : 0;
                
                OperationResult result;
                if (affectedRows > 0) {
                    result = OperationResult.success(op.getOperationId(), op.getMessageId(), 
                            op.getType(), affectedRows);
                } else {
                    result = OperationResult.failure(op.getOperationId(), op.getMessageId(),
                            op.getType(), "No rows affected", "BATCH_INSERT_FAILED", null, true);
                }
                
                result.setBatchId(batchId);
                result.setBatchSize(insertOps.size());
                result.setBatchPosition(i);
                result.setExecutionTimeMs(executionTime / insertOps.size()); // Approximate per-operation time
                
                results.add(result);
            }
            
            log.debug("INSERT batch completed: {} operations in {}ms", insertOps.size(), executionTime);
            
        } catch (DataAccessException e) {
            log.error("Batch INSERT failed: {}", e.getMessage(), e);
            
            // Handle batch failure - retry individual operations
            results.addAll(retryIndividualInserts(insertOps, batchId, e));
        }
        
        return results;
    }
    
    /**
     * Process UPDATE operations in batch
     */
    private List<OperationResult> processUpdateBatch(List<DatabaseOperation> updateOps, String batchId) {
        List<OperationResult> results = new ArrayList<>();
        
        if (updateOps.isEmpty()) {
            return results;
        }
        
        try {
            long startTime = System.currentTimeMillis();
            
            // Execute batch update
            int[] updateCounts = jdbcTemplate.batchUpdate(UPDATE_KAFKA_PUB_SQL,
                new BatchPreparedStatementSetter() {
                    @Override
                    public void setValues(PreparedStatement ps, int i) throws SQLException {
                        DatabaseOperation op = updateOps.get(i);
                        Map<String, Object> whereClause = op.getWhereClause();
                        
                        ps.setInt(1, 1); // IS_PUBLISHED = 1
                        ps.setString(2, (String) whereClause.get("messageId"));
                    }
                    
                    @Override
                    public int getBatchSize() {
                        return updateOps.size();
                    }
                });
            
            long executionTime = System.currentTimeMillis() - startTime;
            
            // Create results for each operation
            for (int i = 0; i < updateOps.size(); i++) {
                DatabaseOperation op = updateOps.get(i);
                int affectedRows = (i < updateCounts.length) ? updateCounts[i] : 0;
                
                OperationResult result;
                if (affectedRows > 0) {
                    result = OperationResult.success(op.getOperationId(), op.getMessageId(), 
                            op.getType(), affectedRows);
                } else {
                    // Check if record exists before marking as failure
                    boolean recordExists = checkRecordExists(op.getMessageId());
                    if (!recordExists) {
                        result = OperationResult.failure(op.getOperationId(), op.getMessageId(),
                                op.getType(), "Record not found for update", "RECORD_NOT_FOUND", null, false);
                    } else {
                        result = OperationResult.failure(op.getOperationId(), op.getMessageId(),
                                op.getType(), "Update failed - record exists but not updated", "UPDATE_FAILED", null, true);
                    }
                }
                
                result.setBatchId(batchId);
                result.setBatchSize(updateOps.size());
                result.setBatchPosition(i);
                result.setExecutionTimeMs(executionTime / updateOps.size());
                
                results.add(result);
            }
            
            log.debug("UPDATE batch completed: {} operations in {}ms", updateOps.size(), executionTime);
            
        } catch (DataAccessException e) {
            log.error("Batch UPDATE failed: {}", e.getMessage(), e);
            
            // Handle batch failure - retry individual operations
            results.addAll(retryIndividualUpdates(updateOps, batchId, e));
        }
        
        return results;
    }
    
    /**
     * Determine if an error is retryable
     */
    private boolean isRetryableError(Exception e) {
        String message = e.getMessage().toLowerCase();
        
        // Connection errors are retryable
        if (message.contains("connection") || message.contains("network") || message.contains("timeout")) {
            return true;
        }
        
        // Oracle-specific retryable errors
        if (message.contains("ora-00060") || // Deadlock
            message.contains("ora-00054") || // Resource busy
            message.contains("ora-17002") || // IO Error
            message.contains("ora-17008")) { // Closed connection
            return true;
        }
        
        // ORA-22923 is retryable with different approach
        if (message.contains("ora-22923")) {
            return true;
        }
        
        return false;
    }
    
    /**
     * Extract error code from exception
     */
    private String extractErrorCode(Exception e) {
        String message = e.getMessage();
        if (message != null && message.contains("ORA-")) {
            int start = message.indexOf("ORA-");
            int end = message.indexOf(":", start);
            if (end == -1) end = message.indexOf(" ", start);
            if (end == -1) end = start + 9; // ORA-XXXXX
            return message.substring(start, Math.min(end, message.length()));
        }
        return "UNKNOWN";
    }
}
