package com.scm.fi.db.async.queue;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * Metrics for the async database queue
 * Used for monitoring and performance analysis
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QueueMetrics {
    
    // Current state
    private int currentSize;
    private long currentMemoryUsageMB;
    private long maxMemoryMB;
    private double memoryUtilizationPercent;
    private boolean isDatabaseDown;
    
    // Throughput metrics
    private long totalOperationsAdded;
    private long totalOperationsRemoved;
    private long totalMemoryProcessedMB;
    private double operationsPerSecond;
    private double memoryThroughputMBPerSecond;
    
    // Overflow metrics
    private long overflowEventsCount;
    private LocalDateTime lastOverflowEvent;
    private long operationsDroppedDueToOverflow;
    
    // Performance indicators
    private double averageOperationSizeKB;
    private long queueTurnoverTimeMs;
    private boolean isNearCapacity;
    private boolean isHealthy;
    
    /**
     * Calculate derived metrics
     */
    public void calculateDerivedMetrics() {
        // Calculate average operation size
        if (totalOperationsAdded > 0) {
            this.averageOperationSizeKB = (double) (totalMemoryProcessedMB * 1024) / totalOperationsAdded;
        }
        
        // Determine if near capacity
        this.isNearCapacity = memoryUtilizationPercent > 80.0 || 
                             (maxMemoryMB > 0 && currentSize > (maxMemoryMB * 1024 * 1024 * 0.8 / averageOperationSizeKB));
        
        // Determine overall health
        this.isHealthy = !isDatabaseDown && 
                        memoryUtilizationPercent < 90.0 && 
                        (lastOverflowEvent == null || 
                         lastOverflowEvent.isBefore(LocalDateTime.now().minusMinutes(5)));
    }
    
    /**
     * Get health status as string
     */
    public String getHealthStatus() {
        if (isHealthy) {
            return "HEALTHY";
        } else if (isDatabaseDown) {
            return "DATABASE_DOWN";
        } else if (memoryUtilizationPercent > 95.0) {
            return "MEMORY_CRITICAL";
        } else if (isNearCapacity) {
            return "NEAR_CAPACITY";
        } else {
            return "DEGRADED";
        }
    }
    
    /**
     * Get summary string for logging
     */
    public String getSummary() {
        return String.format("Queue: %d ops, %.1fMB/%.1fMB (%.1f%%), %s, Health: %s", 
                currentSize, 
                (double) currentMemoryUsageMB, 
                (double) maxMemoryMB,
                memoryUtilizationPercent,
                isDatabaseDown ? "DB_DOWN" : "DB_UP",
                getHealthStatus());
    }
    
    /**
     * Get detailed metrics for monitoring dashboard
     */
    public String getDetailedMetrics() {
        StringBuilder sb = new StringBuilder();
        sb.append("=== Async Database Queue Metrics ===\n");
        sb.append(String.format("Current Size: %d operations\n", currentSize));
        sb.append(String.format("Memory Usage: %.1f MB / %.1f MB (%.1f%%)\n", 
                (double) currentMemoryUsageMB, (double) maxMemoryMB, memoryUtilizationPercent));
        sb.append(String.format("Database Status: %s\n", isDatabaseDown ? "DOWN" : "UP"));
        sb.append(String.format("Health Status: %s\n", getHealthStatus()));
        sb.append(String.format("Total Operations: Added=%d, Removed=%d\n", 
                totalOperationsAdded, totalOperationsRemoved));
        sb.append(String.format("Average Operation Size: %.1f KB\n", averageOperationSizeKB));
        sb.append(String.format("Overflow Events: %d\n", overflowEventsCount));
        if (lastOverflowEvent != null) {
            sb.append(String.format("Last Overflow: %s\n", lastOverflowEvent));
        }
        sb.append(String.format("Performance: %.1f ops/sec, %.1f MB/sec\n", 
                operationsPerSecond, memoryThroughputMBPerSecond));
        
        return sb.toString();
    }
}
