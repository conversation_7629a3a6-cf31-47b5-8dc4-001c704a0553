package com.scm.fi.db.async.queue;

import com.scm.fi.db.async.config.AsyncDatabaseConfig;
import com.scm.fi.db.async.model.DatabaseOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.PriorityBlockingQueue;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.ReentrantReadWriteLock;
import java.util.List;
import java.util.ArrayList;
import java.util.Comparator;
import java.time.LocalDateTime;

/**
 * High-performance bounded queue for database operations with memory management
 * Supports priority ordering and overflow protection
 */
@Slf4j
@Component
public class AsyncDatabaseQueue {
    
    private final AsyncDatabaseConfig config;
    private final BlockingQueue<DatabaseOperation> queue;
    private final AtomicLong currentMemoryUsage = new AtomicLong(0);
    private final AtomicInteger operationCount = new AtomicInteger(0);
    private final AtomicLong totalOperationsAdded = new AtomicLong(0);
    private final AtomicLong totalOperationsRemoved = new AtomicLong(0);
    private final AtomicLong totalMemoryBytesProcessed = new AtomicLong(0);
    private final AtomicLong overflowEventsCount = new AtomicLong(0);
    
    private final ReentrantReadWriteLock lock = new ReentrantReadWriteLock();
    private volatile boolean isDatabaseDown = false;
    private volatile LocalDateTime lastOverflowEvent;
    
    // Memory thresholds
    private final long maxMemoryBytes;
    private final long overflowThresholdBytes;
    private final int drainBatchSize;
    
    public AsyncDatabaseQueue(AsyncDatabaseConfig config) {
        this.config = config;
        this.maxMemoryBytes = config.getQueue().getMaxMemoryMB() * 1024 * 1024; // Convert MB to bytes
        this.overflowThresholdBytes = (long) (maxMemoryBytes * config.getQueue().getOverflowThreshold());
        this.drainBatchSize = config.getQueue().getDrainBatchSize();
        
        // Use PriorityBlockingQueue for operation ordering
        this.queue = new PriorityBlockingQueue<>(
            config.getQueue().getMaxRecords(),
            new DatabaseOperationComparator()
        );
        
        log.info("AsyncDatabaseQueue initialized - Max Memory: {}MB, Overflow Threshold: {}MB, Max Records: {}", 
                config.getQueue().getMaxMemoryMB(), 
                overflowThresholdBytes / (1024 * 1024),
                config.getQueue().getMaxRecords());
    }
    
    /**
     * Add operation to queue with memory management
     */
    public boolean offer(DatabaseOperation operation) {
        if (operation == null) {
            return false;
        }
        
        lock.writeLock().lock();
        try {
            // Calculate memory footprint
            long operationMemory = operation.calculateMemoryFootprint();
            long newMemoryUsage = currentMemoryUsage.get() + operationMemory;
            
            // Check if we're approaching memory limits
            if (newMemoryUsage > overflowThresholdBytes && config.getQueue().isEnableOverflowProtection()) {
                handleOverflowCondition();
                
                // Recalculate after overflow handling
                newMemoryUsage = currentMemoryUsage.get() + operationMemory;
                
                // If still over limit and database is down, reject operation
                if (newMemoryUsage > maxMemoryBytes && isDatabaseDown) {
                    log.warn("Queue memory limit exceeded and database is down, rejecting operation: {}", 
                            operation.getOperationId());
                    return false;
                }
            }
            
            // Add to queue
            boolean added = queue.offer(operation);
            if (added) {
                currentMemoryUsage.addAndGet(operationMemory);
                operationCount.incrementAndGet();
                totalOperationsAdded.incrementAndGet();
                totalMemoryBytesProcessed.addAndGet(operationMemory);
                
                if (log.isDebugEnabled()) {
                    log.debug("Added operation {} to queue. Queue size: {}, Memory usage: {}MB", 
                            operation.getOperationId(), 
                            operationCount.get(),
                            currentMemoryUsage.get() / (1024 * 1024));
                }
            }
            
            return added;
            
        } finally {
            lock.writeLock().unlock();
        }
    }
    
    /**
     * Remove and return the next operation from queue
     */
    public DatabaseOperation poll() {
        lock.writeLock().lock();
        try {
            DatabaseOperation operation = queue.poll();
            if (operation != null) {
                currentMemoryUsage.addAndGet(-operation.getEstimatedMemoryBytes());
                operationCount.decrementAndGet();
                totalOperationsRemoved.incrementAndGet();
            }
            return operation;
        } finally {
            lock.writeLock().unlock();
        }
    }
    
    /**
     * Blocking take operation
     */
    public DatabaseOperation take() throws InterruptedException {
        DatabaseOperation operation = queue.take();
        
        lock.writeLock().lock();
        try {
            if (operation != null) {
                currentMemoryUsage.addAndGet(-operation.getEstimatedMemoryBytes());
                operationCount.decrementAndGet();
                totalOperationsRemoved.incrementAndGet();
            }
        } finally {
            lock.writeLock().unlock();
        }
        
        return operation;
    }
    
    /**
     * Drain operations for batch processing
     */
    public List<DatabaseOperation> drainTo(List<DatabaseOperation> collection, int maxElements) {
        if (collection == null) {
            collection = new ArrayList<>();
        }
        
        lock.writeLock().lock();
        try {
            int drained = queue.drainTo(collection, maxElements);
            
            // Update memory usage
            long memoryFreed = 0;
            for (DatabaseOperation op : collection) {
                memoryFreed += op.getEstimatedMemoryBytes();
            }
            
            currentMemoryUsage.addAndGet(-memoryFreed);
            operationCount.addAndGet(-drained);
            totalOperationsRemoved.addAndGet(drained);
            
            if (drained > 0) {
                log.debug("Drained {} operations from queue. Remaining: {}, Memory freed: {}MB", 
                        drained, operationCount.get(), memoryFreed / (1024 * 1024));
            }
            
            return collection;
            
        } finally {
            lock.writeLock().unlock();
        }
    }
    
    /**
     * Handle queue overflow condition
     */
    private void handleOverflowCondition() {
        if (!config.getQueue().isEnableOverflowProtection()) {
            return;
        }
        
        log.warn("Queue overflow condition detected. Current memory: {}MB, threshold: {}MB", 
                currentMemoryUsage.get() / (1024 * 1024),
                overflowThresholdBytes / (1024 * 1024));
        
        // Drain oldest operations
        List<DatabaseOperation> drainedOps = new ArrayList<>();
        int drained = queue.drainTo(drainedOps, drainBatchSize);
        
        if (drained > 0) {
            long memoryFreed = 0;
            for (DatabaseOperation op : drainedOps) {
                memoryFreed += op.getEstimatedMemoryBytes();
                
                // Complete the operation with overflow error
                if (op.getCompletionFuture() != null) {
                    op.getCompletionFuture().completeExceptionally(
                        new RuntimeException("Operation dropped due to queue overflow"));
                }
            }
            
            currentMemoryUsage.addAndGet(-memoryFreed);
            operationCount.addAndGet(-drained);
            overflowEventsCount.incrementAndGet();
            lastOverflowEvent = LocalDateTime.now();
            
            log.warn("Dropped {} operations due to overflow. Memory freed: {}MB", 
                    drained, memoryFreed / (1024 * 1024));
        }
    }
    
    /**
     * Set database status for overflow handling
     */
    public void setDatabaseDown(boolean isDown) {
        this.isDatabaseDown = isDown;
        if (isDown) {
            log.warn("Database marked as DOWN - overflow protection will be more aggressive");
        } else {
            log.info("Database marked as UP - normal queue operation resumed");
        }
    }
    
    /**
     * Get current queue metrics
     */
    public QueueMetrics getMetrics() {
        lock.readLock().lock();
        try {
            return QueueMetrics.builder()
                    .currentSize(operationCount.get())
                    .currentMemoryUsageMB(currentMemoryUsage.get() / (1024 * 1024))
                    .maxMemoryMB(maxMemoryBytes / (1024 * 1024))
                    .memoryUtilizationPercent((double) currentMemoryUsage.get() / maxMemoryBytes * 100)
                    .totalOperationsAdded(totalOperationsAdded.get())
                    .totalOperationsRemoved(totalOperationsRemoved.get())
                    .totalMemoryProcessedMB(totalMemoryBytesProcessed.get() / (1024 * 1024))
                    .overflowEventsCount(overflowEventsCount.get())
                    .lastOverflowEvent(lastOverflowEvent)
                    .isDatabaseDown(isDatabaseDown)
                    .build();
        } finally {
            lock.readLock().unlock();
        }
    }
    
    /**
     * Clear all operations from queue
     */
    public void clear() {
        lock.writeLock().lock();
        try {
            queue.clear();
            currentMemoryUsage.set(0);
            operationCount.set(0);
            log.info("Queue cleared");
        } finally {
            lock.writeLock().unlock();
        }
    }
    
    /**
     * Comparator for database operation ordering
     * Ensures INSERTs are processed before UPDATEs for the same messageId
     */
    private static class DatabaseOperationComparator implements Comparator<DatabaseOperation> {
        @Override
        public int compare(DatabaseOperation o1, DatabaseOperation o2) {
            // Use the shouldExecuteBefore method from DatabaseOperation
            if (o1.shouldExecuteBefore(o2)) {
                return -1;
            } else if (o2.shouldExecuteBefore(o1)) {
                return 1;
            } else {
                return 0;
            }
        }
    }
}
