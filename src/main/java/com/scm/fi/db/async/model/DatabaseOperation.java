package com.scm.fi.db.async.model;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Represents a database operation to be executed asynchronously
 * Supports both INSERT and UPDATE operations with proper ordering
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DatabaseOperation {
    
    // Operation identification
    private String operationId;
    private String messageId; // Business key for ordering
    private OperationType type;
    private OperationPriority priority;
    
    // Timing information
    private LocalDateTime createdAt;
    private LocalDateTime scheduledAt;
    private int retryCount;
    private LocalDateTime lastRetryAt;
    
    // Operation data
    private String tableName;
    private Map<String, Object> data;
    private Map<String, Object> whereClause; // For UPDATE operations
    
    // Callback and completion
    private CompletableFuture<OperationResult> completionFuture;
    private String correlationId; // For tracking related operations
    
    // Memory estimation (for queue management)
    private long estimatedMemoryBytes;
    
    // Error handling
    private String lastErrorMessage;
    private String lastErrorCode;
    private boolean isPermanentFailure;
    
    public enum OperationType {
        INSERT,
        UPDATE,
        DELETE // Future extension
    }
    
    public enum OperationPriority {
        LOW(1),
        NORMAL(2),
        HIGH(3),
        CRITICAL(4);
        
        private final int level;
        
        OperationPriority(int level) {
            this.level = level;
        }
        
        public int getLevel() {
            return level;
        }
    }
    
    /**
     * Calculate estimated memory usage for this operation
     */
    public long calculateMemoryFootprint() {
        if (estimatedMemoryBytes > 0) {
            return estimatedMemoryBytes;
        }
        
        long size = 200; // Base object overhead
        
        // String fields
        size += estimateStringSize(operationId);
        size += estimateStringSize(messageId);
        size += estimateStringSize(tableName);
        size += estimateStringSize(correlationId);
        size += estimateStringSize(lastErrorMessage);
        size += estimateStringSize(lastErrorCode);
        
        // Map data
        if (data != null) {
            size += estimateMapSize(data);
        }
        
        if (whereClause != null) {
            size += estimateMapSize(whereClause);
        }
        
        this.estimatedMemoryBytes = size;
        return size;
    }
    
    private long estimateStringSize(String str) {
        return str != null ? str.length() * 2L : 0; // 2 bytes per char in Java
    }
    
    private long estimateMapSize(Map<String, Object> map) {
        long size = 64; // Map overhead
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            size += estimateStringSize(entry.getKey());
            Object value = entry.getValue();
            if (value instanceof String) {
                size += estimateStringSize((String) value);
            } else if (value != null) {
                size += value.toString().length() * 2L;
            }
        }
        return size;
    }
    
    /**
     * Check if this operation can be retried
     */
    public boolean canRetry(int maxRetries) {
        return !isPermanentFailure && retryCount < maxRetries;
    }
    
    /**
     * Mark operation for retry
     */
    public void markForRetry() {
        this.retryCount++;
        this.lastRetryAt = LocalDateTime.now();
    }
    
    /**
     * Mark operation as permanently failed
     */
    public void markAsPermanentFailure(String errorMessage, String errorCode) {
        this.isPermanentFailure = true;
        this.lastErrorMessage = errorMessage;
        this.lastErrorCode = errorCode;
    }
    
    /**
     * Create a copy for retry
     */
    public DatabaseOperation createRetryOperation() {
        return DatabaseOperation.builder()
                .operationId(this.operationId + "_retry_" + (this.retryCount + 1))
                .messageId(this.messageId)
                .type(this.type)
                .priority(this.priority)
                .createdAt(this.createdAt)
                .scheduledAt(LocalDateTime.now())
                .retryCount(this.retryCount + 1)
                .lastRetryAt(LocalDateTime.now())
                .tableName(this.tableName)
                .data(this.data)
                .whereClause(this.whereClause)
                .correlationId(this.correlationId)
                .estimatedMemoryBytes(this.estimatedMemoryBytes)
                .build();
    }
    
    /**
     * Check if this operation should be executed before another operation
     * Ensures INSERT operations for a messageId are executed before UPDATEs
     */
    public boolean shouldExecuteBefore(DatabaseOperation other) {
        // Same message ID - INSERT before UPDATE
        if (this.messageId != null && this.messageId.equals(other.messageId)) {
            if (this.type == OperationType.INSERT && other.type == OperationType.UPDATE) {
                return true;
            }
            if (this.type == OperationType.UPDATE && other.type == OperationType.INSERT) {
                return false;
            }
        }
        
        // Priority-based ordering
        if (this.priority.getLevel() != other.priority.getLevel()) {
            return this.priority.getLevel() > other.priority.getLevel();
        }
        
        // Time-based ordering (older first)
        return this.createdAt.isBefore(other.createdAt);
    }
}
