package com.scm.fi.db.async.model;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Result of a database operation execution
 * Contains success/failure information and performance metrics
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OperationResult {
    
    // Operation identification
    private String operationId;
    private String messageId;
    private DatabaseOperation.OperationType operationType;
    
    // Execution status
    private boolean success;
    private int affectedRows;
    private LocalDateTime executedAt;
    private long executionTimeMs;
    
    // Error information
    private String errorMessage;
    private String errorCode;
    private Throwable exception;
    private boolean isRetryable;
    
    // Batch information
    private String batchId;
    private int batchSize;
    private int batchPosition;
    
    // Performance metrics
    private long queueWaitTimeMs;
    private long dbConnectionTimeMs;
    private long sqlExecutionTimeMs;
    
    /**
     * Create a successful result
     */
    public static OperationResult success(String operationId, String messageId, 
                                        DatabaseOperation.OperationType type, int affectedRows) {
        return OperationResult.builder()
                .operationId(operationId)
                .messageId(messageId)
                .operationType(type)
                .success(true)
                .affectedRows(affectedRows)
                .executedAt(LocalDateTime.now())
                .isRetryable(false)
                .build();
    }
    
    /**
     * Create a failed result
     */
    public static OperationResult failure(String operationId, String messageId,
                                        DatabaseOperation.OperationType type, 
                                        String errorMessage, String errorCode,
                                        Throwable exception, boolean isRetryable) {
        return OperationResult.builder()
                .operationId(operationId)
                .messageId(messageId)
                .operationType(type)
                .success(false)
                .affectedRows(0)
                .executedAt(LocalDateTime.now())
                .errorMessage(errorMessage)
                .errorCode(errorCode)
                .exception(exception)
                .isRetryable(isRetryable)
                .build();
    }
    
    /**
     * Check if this is a database connectivity error
     */
    public boolean isConnectionError() {
        if (errorCode == null && errorMessage == null) {
            return false;
        }
        
        String message = (errorMessage != null ? errorMessage : "").toLowerCase();
        String code = (errorCode != null ? errorCode : "").toLowerCase();
        
        return message.contains("connection") ||
               message.contains("network") ||
               message.contains("timeout") ||
               code.contains("08") || // SQL state for connection errors
               code.contains("17002") || // Oracle IO Error
               code.contains("17008"); // Oracle Closed Connection
    }
    
    /**
     * Check if this is a temporary error that should be retried
     */
    public boolean isTemporaryError() {
        if (!isRetryable || success) {
            return false;
        }
        
        String message = (errorMessage != null ? errorMessage : "").toLowerCase();
        String code = (errorCode != null ? errorCode : "").toLowerCase();
        
        // Connection errors are temporary
        if (isConnectionError()) {
            return true;
        }
        
        // Oracle deadlocks are temporary
        if (code.contains("60") || message.contains("deadlock")) {
            return true;
        }
        
        // Resource busy errors are temporary
        if (code.contains("54") || message.contains("resource busy")) {
            return true;
        }
        
        return false;
    }
    
    /**
     * Check if this is a permanent error (constraint violation, etc.)
     */
    public boolean isPermanentError() {
        if (success || isRetryable) {
            return false;
        }
        
        String code = (errorCode != null ? errorCode : "").toLowerCase();
        String message = (errorMessage != null ? errorMessage : "").toLowerCase();
        
        // Constraint violations are permanent
        if (code.contains("23") || message.contains("constraint")) {
            return true;
        }
        
        // Invalid SQL is permanent
        if (code.contains("42") || message.contains("syntax")) {
            return true;
        }
        
        // Table/column not found is permanent
        if (code.contains("942") || code.contains("904")) {
            return true;
        }
        
        return false;
    }
    
    /**
     * Get a summary string for logging
     */
    public String getSummary() {
        if (success) {
            return String.format("SUCCESS: %s %s affected %d rows in %dms", 
                    operationType, messageId, affectedRows, executionTimeMs);
        } else {
            return String.format("FAILED: %s %s - %s (%s) - retryable: %s", 
                    operationType, messageId, errorMessage, errorCode, isRetryable);
        }
    }
}

/**
 * Result of a batch operation containing multiple individual results
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
class BatchResult {
    
    private String batchId;
    private LocalDateTime executedAt;
    private long totalExecutionTimeMs;
    private int batchSize;
    
    // Results breakdown
    private List<OperationResult> results;
    private int successCount;
    private int failureCount;
    private int retryableFailureCount;
    private int permanentFailureCount;
    
    // Performance metrics
    private long avgExecutionTimeMs;
    private long maxExecutionTimeMs;
    private long minExecutionTimeMs;
    private double throughputPerSecond;
    
    /**
     * Calculate performance metrics from individual results
     */
    public void calculateMetrics() {
        if (results == null || results.isEmpty()) {
            return;
        }
        
        successCount = 0;
        failureCount = 0;
        retryableFailureCount = 0;
        permanentFailureCount = 0;
        
        long totalTime = 0;
        long maxTime = 0;
        long minTime = Long.MAX_VALUE;
        
        for (OperationResult result : results) {
            if (result.isSuccess()) {
                successCount++;
            } else {
                failureCount++;
                if (result.isRetryable()) {
                    retryableFailureCount++;
                } else {
                    permanentFailureCount++;
                }
            }
            
            long execTime = result.getExecutionTimeMs();
            totalTime += execTime;
            maxTime = Math.max(maxTime, execTime);
            minTime = Math.min(minTime, execTime);
        }
        
        this.avgExecutionTimeMs = totalTime / results.size();
        this.maxExecutionTimeMs = maxTime;
        this.minExecutionTimeMs = minTime == Long.MAX_VALUE ? 0 : minTime;
        
        if (totalExecutionTimeMs > 0) {
            this.throughputPerSecond = (double) batchSize / (totalExecutionTimeMs / 1000.0);
        }
    }
    
    /**
     * Get batch summary for logging
     */
    public String getSummary() {
        return String.format("Batch %s: %d operations, %d success, %d failed (%d retryable), %.2f ops/sec", 
                batchId, batchSize, successCount, failureCount, retryableFailureCount, throughputPerSecond);
    }
}
